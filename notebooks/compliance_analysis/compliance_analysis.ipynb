{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📊 Compliance Analysis\n", "\n", "This notebook implements compliance analysis as part of the compliance analysis stage. It compares detected pile data with design data from IFC or CAD to calculate deviation statistics.\n", "\n", "**Stage**: Compliance Analysis  \n", "**Input Data**: Detected vs design data (from IFC or CAD)  \n", "**Output**: Deviation stats (spacing, verticality, rotation, etc.)  \n", "**Format**: .json (for nested deviations per pile), .csv (for sortable analysis, one row per pile)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Detection Results**: Import pile detection results from pile detection stage\n", "2. **Load Design Data**: Import IFC/CAD design specifications and positions\n", "3. **Spatial Matching**: Match detected piles with design pile locations\n", "4. **Deviation Analysis**: Calculate spacing, verticality, rotation deviations\n", "5. **Compliance Assessment**: Evaluate against tolerance thresholds\n", "6. **Export Results**: Save deviation statistics in .json and .csv formats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1️⃣ Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "import math\n", "\n", "# Scientific computing\n", "from scipy.spatial.distance import cdist\n", "from scipy.spatial import cKDTree\n", "from scipy.stats import pearsonr\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error\n", "\n", "# Visualization\n", "import seaborn as sns\n", "from matplotlib.patches import Circle, Rectangle\n", "import matplotlib.patches as mpatches\n", "\n", "# Suppress warnings\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Project organization - adjust based on your project\n", "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "\n", "# Input and output paths following the specified organization\n", "project_base = base_path / 'output' / PROJECT_TYPE / PROJECT_NAME\n", "detection_path = project_base / 'pile_detection'\n", "preprocessing_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'preprocessing'\n", "compliance_path = project_base / 'compliance_analysis'\n", "compliance_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"📊 Compliance Analysis - Ready!\")\n", "print(f\"📁 Data path: {data_path}\")\n", "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n", "print(f\"🔍 Detection results path: {detection_path}\")\n", "print(f\"📐 Design data path: {preprocessing_path}\")\n", "print(f\"💾 Compliance output path: {compliance_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2️⃣ Load Detection Results\n", "\n", "Load detected pile data from the pile detection stage."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_detection_results():\n", "    \"\"\"\n", "    Load pile detection results from the pile detection stage.\n", "    Expected input: CSV with detected pile centers + types.\n", "    \"\"\"\n", "    print(f\"🔍 Loading detection results...\")\n", "    \n", "    # Try different detection result sources\n", "    detection_sources = [\n", "        detection_path / f\"{PROJECT_NAME}_detected_pile_centers.csv\",\n", "        detection_path / f\"{PROJECT_NAME}_detected_piles.csv\",\n", "        # Legacy paths\n", "        detection_path / \"detected_pile_centers.csv\"\n", "    ]\n", "    \n", "    for det_path in detection_sources:\n", "        if det_path.exists():\n", "            print(f\"✅ Found detection results: {det_path}\")\n", "            \n", "            try:\n", "                detected_piles = pd.read_csv(det_path)\n", "                print(f\"📊 Loaded {len(detected_piles)} detected piles\")\n", "                print(f\"📋 Columns: {list(detected_piles.columns)}\")\n", "                \n", "                # Validate required columns\n", "                required_cols = ['x', 'y', 'z']\n", "                if all(col in detected_piles.columns for col in required_cols):\n", "                    return detected_piles, det_path\n", "                else:\n", "                    print(f\"⚠️ Missing required columns: {required_cols}\")\n", "                    continue\n", "                    \n", "            except Exception as e:\n", "                print(f\"❌ Error loading {det_path}: {e}\")\n", "                continue\n", "    \n", "    print(f\"❌ No detection results found\")\n", "    return None, None\n", "\n", "# Load detection results\n", "detected_piles, detection_source = load_detection_results()\n", "\n", "if detected_piles is None:\n", "    print(\"\\n🔧 Creating synthetic detection data for demonstration...\")\n", "    detected_piles = pd.DataFrame({\n", "        'pile_id': [f'P{i+1:03d}' for i in range(6)],\n", "        'x': [10.1, 20.2, 30.0, 15.1, 25.0, 35.2],\n", "        'y': [10.0, 10.1, 9.9, 20.2, 19.8, 20.1],\n", "        'z': [0.8, 1.0, 0.9, 0.7, 1.1, 0.8],\n", "        'pile_type': ['I-section', 'cylindrical', 'I-section', 'cylindrical', 'I-section', 'cylindrical'],\n", "        'confidence': [0.85, 0.92, 0.78, 0.88, 0.91, 0.83]\n", "    })\n", "    detection_source = \"synthetic\"\n", "    print(f\"🔧 Created {len(detected_piles)} synthetic detected piles\")\n", "\n", "print(f\"\\n📊 Detection data summary:\")\n", "print(f\"  Total detected piles: {len(detected_piles)}\")\n", "print(f\"  Source: {detection_source}\")\n", "if 'pile_type' in detected_piles.columns:\n", "    print(f\"  Pile types: {detected_piles['pile_type'].value_counts().to_dict()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3️⃣ Load Design Data\n", "\n", "Load design data from IFC or CAD sources."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_design_data():\n", "    \"\"\"\n", "    Load design data from IFC or CAD sources.\n", "    Expected input: IFC/CAD metadata with design specifications.\n", "    \"\"\"\n", "    print(f\"🔍 Loading design data...\")\n", "    \n", "    # Try different design data sources\n", "    design_sources = [\n", "        # IFC metadata (preferred)\n", "        {\n", "            'type': 'ifc_json',\n", "            'path': preprocessing_path / f'{PROJECT_NAME}_ifc_metadata.json'\n", "        },\n", "        # CAD metadata\n", "        {\n", "            'type': 'cad_json',\n", "            'path': preprocessing_path / f'{PROJECT_NAME}_cad_metadata.json'\n", "        },\n", "        # CSV pile positions\n", "        {\n", "            'type': 'csv',\n", "            'path': preprocessing_path / f'{PROJECT_NAME}_pile_positions.csv'\n", "        }\n", "    ]\n", "    \n", "    for source in design_sources:\n", "        if source['path'].exists():\n", "            print(f\"✅ Found design data: {source['path']}\")\n", "            \n", "            try:\n", "                if source['type'] in ['ifc_json', 'cad_json']:\n", "                    # Load JSON metadata\n", "                    with open(source['path'], 'r') as f:\n", "                        metadata = json.load(f)\n", "                    \n", "                    # Extract pile data from JSON structure\n", "                    pile_data = []\n", "                    \n", "                    if 'pile_elements' in metadata:\n", "                        for pile in metadata['pile_elements']:\n", "                            if source['type'] == 'ifc_json':\n", "                                # IFC structure\n", "                                coords = pile.get('LocalCoordinates', {})\n", "                                pile_data.append({\n", "                                    'design_id': pile.get('GlobalId', pile.get('Name', 'Unknown')),\n", "                                    'x': coords.get('X_Local', 0),\n", "                                    'y': coords.get('Y_Local', 0),\n", "                                    'z': coords.get('Z_Local', 0),\n", "                                    'pile_type': pile.get('ObjectType', 'COLUMN'),\n", "                                    'design_spacing': None,  # To be calculated\n", "                                    'design_rotation': 0,  # De<PERSON>ult\n", "                                    'design_verticality': 90  # Default vertical\n", "                                })\n", "                            else:\n", "                                # CAD structure\n", "                                coords = pile.get('coordinates', {})\n", "                                pile_data.append({\n", "                                    'design_id': pile.get('block_name', 'Unknown'),\n", "                                    'x': coords.get('x', 0),\n", "                                    'y': coords.get('y', 0),\n", "                                    'z': coords.get('z', 0),\n", "                                    'pile_type': pile.get('layer', 'Unknown'),\n", "                                    'design_spacing': None,\n", "                                    'design_rotation': 0,\n", "                                    'design_verticality': 90\n", "                                })\n", "                    \n", "                    if pile_data:\n", "                        design_piles = pd.DataFrame(pile_data)\n", "                        print(f\"📊 Loaded {len(design_piles)} design piles from {source['type']}\")\n", "                        return design_piles, source\n", "                \n", "                elif source['type'] == 'csv':\n", "                    # Load CSV pile positions\n", "                    design_piles = pd.read_csv(source['path'])\n", "                    \n", "                    # Standardize column names\n", "                    if 'pile_id' not in design_piles.columns and 'Pile No.' in design_piles.columns:\n", "                        design_piles = design_piles.rename(columns={'Pile No.': 'design_id'})\n", "                    \n", "                    # Add missing design parameters\n", "                    if 'design_spacing' not in design_piles.columns:\n", "                        design_piles['design_spacing'] = None\n", "                    if 'design_rotation' not in design_piles.columns:\n", "                        design_piles['design_rotation'] = 0\n", "                    if 'design_verticality' not in design_piles.columns:\n", "                        design_piles['design_verticality'] = 90\n", "                    \n", "                    print(f\"📊 Loaded {len(design_piles)} design piles from CSV\")\n", "                    return design_piles, source\n", "                    \n", "            except Exception as e:\n", "                print(f\"❌ Error loading {source['path']}: {e}\")\n", "                continue\n", "    \n", "    print(f\"❌ No design data found\")\n", "    return None, None\n", "\n", "# Load design data\n", "design_piles, design_source = load_design_data()\n", "\n", "if design_piles is None:\n", "    print(\"\\n🔧 Creating synthetic design data for demonstration...\")\n", "    design_piles = pd.DataFrame({\n", "        'design_id': [f'D{i+1:03d}' for i in range(6)],\n", "        'x': [10.0, 20.0, 30.0, 15.0, 25.0, 35.0],  # Design positions\n", "        'y': [10.0, 10.0, 10.0, 20.0, 20.0, 20.0],\n", "        'z': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0],  # Ground level\n", "        'pile_type': ['I-section', 'cylindrical', 'I-section', 'cylindrical', 'I-section', 'cylindrical'],\n", "        'design_spacing': [10.0, 10.0, None, 10.0, 10.0, None],  # Spacing to next pile\n", "        'design_rotation': [0, 0, 0, 0, 0, 0],  # Rotation in degrees\n", "        'design_verticality': [90, 90, 90, 90, 90, 90]  # Verticality in degrees\n", "    })\n", "    design_source = {'type': 'synthetic'}\n", "    print(f\"🔧 Created {len(design_piles)} synthetic design piles\")\n", "\n", "print(f\"\\n📊 Design data summary:\")\n", "print(f\"  Total design piles: {len(design_piles)}\")\n", "print(f\"  Source: {design_source['type']}\")\n", "if 'pile_type' in design_piles.columns:\n", "    print(f\"  Pile types: {design_piles['pile_type'].value_counts().to_dict()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4️⃣ Compliance Analysis Functions\n", "\n", "Calculate deviation statistics for spacing, verticality, rotation, etc."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_spacing_deviations(detected_piles, design_piles, matches):\n", "    \"\"\"\n", "    Calculate spacing deviations between detected and design piles.\n", "    \"\"\"\n", "    spacing_deviations = []\n", "    \n", "    for match in matches:\n", "        det_idx = match['detected_idx']\n", "        ref_idx = match['reference_idx']\n", "        \n", "        # Get detected and design pile coordinates\n", "        det_coords = detected_piles.iloc[det_idx][['x', 'y']].values\n", "        design_coords = design_piles.iloc[ref_idx][['x', 'y']].values\n", "        \n", "        # Calculate actual spacing to nearest neighbors\n", "        other_detected = detected_piles.drop(det_idx)[['x', 'y']].values\n", "        other_design = design_piles.drop(ref_idx)[['x', 'y']].values\n", "        \n", "        if len(other_detected) > 0 and len(other_design) > 0:\n", "            # Find nearest neighbor distances\n", "            det_distances = cdist([det_coords], other_detected)[0]\n", "            design_distances = cdist([design_coords], other_design)[0]\n", "            \n", "            actual_spacing = np.min(det_distances)\n", "            design_spacing = np.min(design_distances)\n", "            \n", "            spacing_deviation = actual_spacing - design_spacing\n", "            spacing_deviation_pct = (spacing_deviation / design_spacing) * 100 if design_spacing > 0 else 0\n", "            \n", "            spacing_deviations.append({\n", "                'pile_id': detected_piles.iloc[det_idx]['pile_id'],\n", "                'design_id': design_piles.iloc[ref_idx]['design_id'],\n", "                'actual_spacing': actual_spacing,\n", "                'design_spacing': design_spacing,\n", "                'spacing_deviation': spacing_deviation,\n", "                'spacing_deviation_pct': spacing_deviation_pct\n", "            })\n", "    \n", "    return spacing_deviations\n", "\n", "def calculate_verticality_deviations(detected_piles, design_piles, matches):\n", "    \"\"\"\n", "    Calculate verticality deviations (simplified - assumes vertical design).\n", "    \"\"\"\n", "    verticality_deviations = []\n", "    \n", "    for match in matches:\n", "        det_idx = match['detected_idx']\n", "        ref_idx = match['reference_idx']\n", "        \n", "        # For demonstration, calculate based on height variation\n", "        detected_z = detected_piles.iloc[det_idx]['z']\n", "        design_z = design_piles.iloc[ref_idx]['z']\n", "        \n", "        # Assume design verticality is 90 degrees (vertical)\n", "        design_verticality = design_piles.iloc[ref_idx].get('design_verticality', 90)\n", "        \n", "        # Calculate apparent verticality based on height difference\n", "        # This is simplified - in practice would need 3D pile orientation\n", "        height_deviation = abs(detected_z - design_z)\n", "        \n", "        # Estimate verticality deviation (simplified calculation)\n", "        # In practice, this would require pile orientation vectors\n", "        estimated_verticality = 90 - (height_deviation * 10)  # Simplified\n", "        verticality_deviation = estimated_verticality - design_verticality\n", "        \n", "        verticality_deviations.append({\n", "            'pile_id': detected_piles.iloc[det_idx]['pile_id'],\n", "            'design_id': design_piles.iloc[ref_idx]['design_id'],\n", "            'design_verticality': design_verticality,\n", "            'estimated_verticality': estimated_verticality,\n", "            'verticality_deviation': verticality_deviation,\n", "            'height_deviation': height_deviation\n", "        })\n", "    \n", "    return verticality_deviations\n", "\n", "def calculate_rotation_deviations(detected_piles, design_piles, matches):\n", "    \"\"\"\n", "    Calculate rotation deviations (simplified - assumes no rotation data).\n", "    \"\"\"\n", "    rotation_deviations = []\n", "    \n", "    for match in matches:\n", "        det_idx = match['detected_idx']\n", "        ref_idx = match['reference_idx']\n", "        \n", "        # For demonstration, assume design rotation is 0\n", "        design_rotation = design_piles.iloc[ref_idx].get('design_rotation', 0)\n", "        \n", "        # In practice, would extract rotation from pile orientation\n", "        # For now, use a simplified estimation based on pile type\n", "        pile_type = detected_piles.iloc[det_idx].get('pile_type', 'unknown')\n", "        \n", "        if pile_type.lower() == 'i-section':\n", "            # I-section piles have orientation - estimate from position\n", "            estimated_rotation = 0  # Simplified\n", "        else:\n", "            # Cylindrical piles have no meaningful rotation\n", "            estimated_rotation = 0\n", "        \n", "        rotation_deviation = estimated_rotation - design_rotation\n", "        \n", "        rotation_deviations.append({\n", "            'pile_id': detected_piles.iloc[det_idx]['pile_id'],\n", "            'design_id': design_piles.iloc[ref_idx]['design_id'],\n", "            'design_rotation': design_rotation,\n", "            'estimated_rotation': estimated_rotation,\n", "            'rotation_deviation': rotation_deviation,\n", "            'pile_type': pile_type\n", "        })\n", "    \n", "    return rotation_deviations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5️⃣ Perform Compliance Analysis\n", "\n", "Execute the compliance analysis workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def perform_spatial_matching(detected_piles, design_piles, max_distance=2.0):\n", "    \"\"\"\n", "    Match detected piles with design piles based on spatial proximity.\n", "    \"\"\"\n", "    print(f\"🔍 Performing spatial matching...\")\n", "    print(f\"  Max matching distance: {max_distance}m\")\n", "    \n", "    # Extract coordinates\n", "    detected_coords = detected_piles[['x', 'y']].values\n", "    design_coords = design_piles[['x', 'y']].values\n", "    \n", "    # Calculate distance matrix\n", "    distance_matrix = cdist(detected_coords, design_coords)\n", "    \n", "    # Find matches using greedy assignment\n", "    matches = []\n", "    used_design = set()\n", "    used_detected = set()\n", "    \n", "    # Sort by distance to prioritize closest matches\n", "    flat_indices = np.unravel_index(np.argsort(distance_matrix.ravel()), distance_matrix.shape)\n", "    \n", "    for det_idx, des_idx in zip(flat_indices[0], flat_indices[1]):\n", "        distance = distance_matrix[det_idx, des_idx]\n", "        \n", "        # Check if within max distance and not already used\n", "        if (distance <= max_distance and \n", "            det_idx not in used_detected and \n", "            des_idx not in used_design):\n", "            \n", "            match = {\n", "                'detected_idx': int(det_idx),\n", "                'reference_idx': int(des_idx),\n", "                'distance': float(distance)\n", "            }\n", "            \n", "            matches.append(match)\n", "            used_detected.add(det_idx)\n", "            used_design.add(des_idx)\n", "    \n", "    print(f\"✅ Found {len(matches)} matches\")\n", "    return matches\n", "\n", "# Perform compliance analysis if we have both detected and design data\n", "if detected_piles is not None and design_piles is not None:\n", "    \n", "    # Spatial matching\n", "    MAX_MATCHING_DISTANCE = 3.0  # meters\n", "    matches = perform_spatial_matching(detected_piles, design_piles, MAX_MATCHING_DISTANCE)\n", "    \n", "    if matches:\n", "        print(f\"\\n📊 Calculating deviation statistics...\")\n", "        \n", "        # Calculate various deviation types\n", "        spacing_deviations = calculate_spacing_deviations(detected_piles, design_piles, matches)\n", "        verticality_deviations = calculate_verticality_deviations(detected_piles, design_piles, matches)\n", "        rotation_deviations = calculate_rotation_deviations(detected_piles, design_piles, matches)\n", "        \n", "        print(f\"✅ Calculated deviations for {len(matches)} matched piles\")\n", "        print(f\"  Spacing deviations: {len(spacing_deviations)}\")\n", "        print(f\"  Verticality deviations: {len(verticality_deviations)}\")\n", "        print(f\"  Rotation deviations: {len(rotation_deviations)}\")\n", "        \n", "    else:\n", "        print(\"❌ No matches found - cannot calculate deviations\")\n", "        spacing_deviations = []\n", "        verticality_deviations = []\n", "        rotation_deviations = []\n", "        \n", "else:\n", "    print(\"❌ Cannot perform compliance analysis - missing detected piles or design data\")\n", "    matches = []\n", "    spacing_deviations = []\n", "    verticality_deviations = []\n", "    rotation_deviations = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6️⃣ Export Compliance Analysis Results\n", "\n", "Export deviation statistics in the specified formats."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_compliance_analysis_results(matches, spacing_deviations, verticality_deviations, \n", "                                      rotation_deviations, detected_piles, design_piles, \n", "                                      output_path, project_name):\n", "    \"\"\"\n", "    Export compliance analysis results in the specified formats.\n", "    \n", "    Parameters:\n", "    -----------\n", "    matches : list\n", "        Spatial matches between detected and design piles\n", "    spacing_deviations : list\n", "        Spacing deviation calculations\n", "    verticality_deviations : list\n", "        Verticality deviation calculations\n", "    rotation_deviations : list\n", "        Rotation deviation calculations\n", "    detected_piles : DataFrame\n", "        Detected pile data\n", "    design_piles : DataFrame\n", "        Design pile data\n", "    output_path : Path\n", "        Output directory\n", "    project_name : str\n", "        Project name\n", "        \n", "    Returns:\n", "    --------\n", "    exported_files : dict\n", "        Dictionary of exported file paths\n", "    \"\"\"\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    exported_files = {}\n", "    \n", "    # 1. Export as CSV (for sortable analysis, one row per pile)\n", "    if matches:\n", "        compliance_data = []\n", "        \n", "        for i, match in enumerate(matches):\n", "            det_idx = match['detected_idx']\n", "            des_idx = match['reference_idx']\n", "            \n", "            detected_pile = detected_piles.iloc[det_idx]\n", "            design_pile = design_piles.iloc[des_idx]\n", "            \n", "            # Get deviation data\n", "            spacing_dev = spacing_deviations[i] if i < len(spacing_deviations) else {}\n", "            verticality_dev = verticality_deviations[i] if i < len(verticality_deviations) else {}\n", "            rotation_dev = rotation_deviations[i] if i < len(rotation_deviations) else {}\n", "            \n", "            # Calculate position deviations\n", "            x_deviation = detected_pile['x'] - design_pile['x']\n", "            y_deviation = detected_pile['y'] - design_pile['y']\n", "            z_deviation = detected_pile['z'] - design_pile['z']\n", "            spatial_deviation = match['distance']\n", "            \n", "            # Compliance thresholds (configurable)\n", "            position_tolerance = 1.0  # meters\n", "            spacing_tolerance = 0.5   # meters\n", "            verticality_tolerance = 2.0  # degrees\n", "            rotation_tolerance = 5.0  # degrees\n", "            \n", "            # Compliance flags\n", "            position_compliant = spatial_deviation <= position_tolerance\n", "            spacing_compliant = abs(spacing_dev.get('spacing_deviation', 0)) <= spacing_tolerance\n", "            verticality_compliant = abs(verticality_dev.get('verticality_deviation', 0)) <= verticality_tolerance\n", "            rotation_compliant = abs(rotation_dev.get('rotation_deviation', 0)) <= rotation_tolerance\n", "            \n", "            overall_compliant = (position_compliant and spacing_compliant and \n", "                               verticality_compliant and rotation_compliant)\n", "            \n", "            compliance_data.append({\n", "                'pile_id': detected_pile['pile_id'],\n", "                'design_id': design_pile['design_id'],\n", "                \n", "                # Position data\n", "                'detected_x': round(detected_pile['x'], 3),\n", "                'detected_y': round(detected_pile['y'], 3),\n", "                'detected_z': round(detected_pile['z'], 3),\n", "                'design_x': round(design_pile['x'], 3),\n", "                'design_y': round(design_pile['y'], 3),\n", "                'design_z': round(design_pile['z'], 3),\n", "                \n", "                # Position deviations\n", "                'x_deviation': round(x_deviation, 3),\n", "                'y_deviation': round(y_deviation, 3),\n", "                'z_deviation': round(z_deviation, 3),\n", "                'spatial_deviation': round(spatial_deviation, 3),\n", "                \n", "                # Spacing deviations\n", "                'actual_spacing': round(spacing_dev.get('actual_spacing', 0), 3),\n", "                'design_spacing': round(spacing_dev.get('design_spacing', 0), 3),\n", "                'spacing_deviation': round(spacing_dev.get('spacing_deviation', 0), 3),\n", "                'spacing_deviation_pct': round(spacing_dev.get('spacing_deviation_pct', 0), 2),\n", "                \n", "                # Verticality deviations\n", "                'design_verticality': round(verticality_dev.get('design_verticality', 90), 2),\n", "                'estimated_verticality': round(verticality_dev.get('estimated_verticality', 90), 2),\n", "                'verticality_deviation': round(verticality_dev.get('verticality_deviation', 0), 2),\n", "                \n", "                # Rotation deviations\n", "                'design_rotation': round(rotation_dev.get('design_rotation', 0), 2),\n", "                'estimated_rotation': round(rotation_dev.get('estimated_rotation', 0), 2),\n", "                'rotation_deviation': round(rotation_dev.get('rotation_deviation', 0), 2),\n", "                \n", "                # Pile types\n", "                'detected_type': detected_pile.get('pile_type', 'unknown'),\n", "                'design_type': design_pile.get('pile_type', 'unknown'),\n", "                'type_match': detected_pile.get('pile_type', '') == design_pile.get('pile_type', ''),\n", "                \n", "                # Compliance flags\n", "                'position_compliant': position_compliant,\n", "                'spacing_compliant': spacing_compliant,\n", "                'verticality_compliant': verticality_compliant,\n", "                'rotation_compliant': rotation_compliant,\n", "                'overall_compliant': overall_compliant,\n", "                \n", "                # Metadata\n", "                'confidence': detected_pile.get('confidence', 0),\n", "                'analysis_timestamp': datetime.now().isoformat()\n", "            })\n", "        \n", "        # Create DataFrame and export as CSV\n", "        compliance_df = pd.DataFrame(compliance_data)\n", "        \n", "        # Sort by overall compliance (compliant first), then by spatial deviation\n", "        compliance_df = compliance_df.sort_values(\n", "            ['overall_compliant', 'spatial_deviation'], \n", "            ascending=[False, True]\n", "        ).reset_index(drop=True)\n", "        \n", "        csv_filename = f\"{project_name}_compliance_analysis.csv\"\n", "        csv_path = output_path / csv_filename\n", "        compliance_df.to_csv(csv_path, index=False)\n", "        exported_files['csv_file'] = csv_path\n", "        \n", "        print(f\"✅ Exported compliance analysis: {csv_path}\")\n", "        print(f\"   Format: .csv (sortable analysis, one row per pile)\")\n", "        print(f\"   Records: {len(compliance_df)} pile compliance records\")\n", "        \n", "        # Display compliance summary\n", "        total_piles = len(compliance_df)\n", "        compliant_piles = compliance_df['overall_compliant'].sum()\n", "        compliance_rate = (compliant_piles / total_piles) * 100 if total_piles > 0 else 0\n", "        \n", "        print(f\"\\n📊 Compliance Summary:\")\n", "        print(f\"   Overall compliant: {compliant_piles}/{total_piles} ({compliance_rate:.1f}%)\")\n", "        print(f\"   Position compliant: {compliance_df['position_compliant'].sum()}\")\n", "        print(f\"   Spacing compliant: {compliance_df['spacing_compliant'].sum()}\")\n", "        print(f\"   Verticality compliant: {compliance_df['verticality_compliant'].sum()}\")\n", "        print(f\"   Rotation compliant: {compliance_df['rotation_compliant'].sum()}\")\n", "    \n", "    else:\n", "        compliance_df = pd.DataFrame()\n", "        print(\"⚠️ No matches found - CSV export skipped\")\n", "    \n", "    return exported_files, compliance_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_compliance_json(matches, spacing_deviations, verticality_deviations, \n", "                          rotation_deviations, detected_piles, design_piles, \n", "                          compliance_df, output_path, project_name):\n", "    \"\"\"\n", "    Export compliance analysis as JSON (for nested deviations per pile).\n", "    \"\"\"\n", "    \n", "    # Create nested JSON structure\n", "    compliance_json = {\n", "        'project_info': {\n", "            'project_name': project_name,\n", "            'analysis_timestamp': datetime.now().isoformat(),\n", "            'analysis_stage': 'compliance_analysis'\n", "        },\n", "        'analysis_method': 'spatial_matching_with_deviation_calculation',\n", "        'tolerance_thresholds': {\n", "            'position_tolerance_m': 1.0,\n", "            'spacing_tolerance_m': 0.5,\n", "            'verticality_tolerance_deg': 2.0,\n", "            'rotation_tolerance_deg': 5.0\n", "        },\n", "        'overall_statistics': {\n", "            'total_detected_piles': len(detected_piles),\n", "            'total_design_piles': len(design_piles),\n", "            'total_matches': len(matches),\n", "            'match_rate_pct': (len(matches) / len(design_piles)) * 100 if len(design_piles) > 0 else 0\n", "        },\n", "        'compliance_summary': {},\n", "        'pile_deviations': {}\n", "    }\n", "    \n", "    if not compliance_df.empty:\n", "        # Overall compliance statistics\n", "        total_piles = len(compliance_df)\n", "        compliance_json['compliance_summary'] = {\n", "            'total_analyzed_piles': total_piles,\n", "            'overall_compliant': int(compliance_df['overall_compliant'].sum()),\n", "            'overall_compliance_rate_pct': float((compliance_df['overall_compliant'].sum() / total_piles) * 100),\n", "            'position_compliant': int(compliance_df['position_compliant'].sum()),\n", "            'spacing_compliant': int(compliance_df['spacing_compliant'].sum()),\n", "            'verticality_compliant': int(compliance_df['verticality_compliant'].sum()),\n", "            'rotation_compliant': int(compliance_df['rotation_compliant'].sum()),\n", "            'type_matches': int(compliance_df['type_match'].sum())\n", "        }\n", "        \n", "        # Deviation statistics\n", "        compliance_json['deviation_statistics'] = {\n", "            'spatial_deviations': {\n", "                'mean_m': float(compliance_df['spatial_deviation'].mean()),\n", "                'std_m': float(compliance_df['spatial_deviation'].std()),\n", "                'max_m': float(compliance_df['spatial_deviation'].max()),\n", "                'min_m': float(compliance_df['spatial_deviation'].min())\n", "            },\n", "            'spacing_deviations': {\n", "                'mean_m': float(compliance_df['spacing_deviation'].mean()),\n", "                'std_m': float(compliance_df['spacing_deviation'].std()),\n", "                'mean_pct': float(compliance_df['spacing_deviation_pct'].mean())\n", "            },\n", "            'verticality_deviations': {\n", "                'mean_deg': float(compliance_df['verticality_deviation'].mean()),\n", "                'std_deg': float(compliance_df['verticality_deviation'].std())\n", "            },\n", "            'rotation_deviations': {\n", "                'mean_deg': float(compliance_df['rotation_deviation'].mean()),\n", "                'std_deg': float(compliance_df['rotation_deviation'].std())\n", "            }\n", "        }\n", "        \n", "        # Nested deviations per pile\n", "        for _, row in compliance_df.iterrows():\n", "            pile_id = row['pile_id']\n", "            compliance_json['pile_deviations'][pile_id] = {\n", "                'design_reference': {\n", "                    'design_id': row['design_id'],\n", "                    'design_position': {\n", "                        'x': row['design_x'],\n", "                        'y': row['design_y'],\n", "                        'z': row['design_z']\n", "                    },\n", "                    'design_type': row['design_type']\n", "                },\n", "                'detected_data': {\n", "                    'detected_position': {\n", "                        'x': row['detected_x'],\n", "                        'y': row['detected_y'],\n", "                        'z': row['detected_z']\n", "                    },\n", "                    'detected_type': row['detected_type'],\n", "                    'confidence': row['confidence']\n", "                },\n", "                'position_deviations': {\n", "                    'x_deviation_m': row['x_deviation'],\n", "                    'y_deviation_m': row['y_deviation'],\n", "                    'z_deviation_m': row['z_deviation'],\n", "                    'spatial_deviation_m': row['spatial_deviation']\n", "                },\n", "                'spacing_deviations': {\n", "                    'actual_spacing_m': row['actual_spacing'],\n", "                    'design_spacing_m': row['design_spacing'],\n", "                    'spacing_deviation_m': row['spacing_deviation'],\n", "                    'spacing_deviation_pct': row['spacing_deviation_pct']\n", "                },\n", "                'verticality_deviations': {\n", "                    'design_verticality_deg': row['design_verticality'],\n", "                    'estimated_verticality_deg': row['estimated_verticality'],\n", "                    'verticality_deviation_deg': row['verticality_deviation']\n", "                },\n", "                'rotation_deviations': {\n", "                    'design_rotation_deg': row['design_rotation'],\n", "                    'estimated_rotation_deg': row['estimated_rotation'],\n", "                    'rotation_deviation_deg': row['rotation_deviation']\n", "                },\n", "                'compliance_flags': {\n", "                    'position_compliant': bool(row['position_compliant']),\n", "                    'spacing_compliant': bool(row['spacing_compliant']),\n", "                    'verticality_compliant': bool(row['verticality_compliant']),\n", "                    'rotation_compliant': bool(row['rotation_compliant']),\n", "                    'type_match': bool(row['type_match']),\n", "                    'overall_compliant': bool(row['overall_compliant'])\n", "                }\n", "            }\n", "    \n", "    # Export JSON\n", "    json_filename = f\"{project_name}_compliance_deviations.json\"\n", "    json_path = output_path / json_filename\n", "    \n", "    with open(json_path, 'w') as f:\n", "        json.dump(compliance_json, f, indent=2)\n", "    \n", "    print(f\"✅ Exported compliance deviations: {json_path}\")\n", "    print(f\"   Format: .json (nested deviations per pile)\")\n", "    print(f\"   Structure: Project info + statistics + per-pile deviations\")\n", "    \n", "    return json_path"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7️⃣ Execute Compliance Analysis and Export\n", "\n", "Run the complete compliance analysis workflow and export results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Execute compliance analysis and export results\n", "if matches and (spacing_deviations or verticality_deviations or rotation_deviations):\n", "    \n", "    print(f\"\\n📊 Exporting compliance analysis results...\")\n", "    \n", "    # Export CSV and get compliance DataFrame\n", "    exported_files, compliance_df = export_compliance_analysis_results(\n", "        matches=matches,\n", "        spacing_deviations=spacing_deviations,\n", "        verticality_deviations=verticality_deviations,\n", "        rotation_deviations=rotation_deviations,\n", "        detected_piles=detected_piles,\n", "        design_piles=design_piles,\n", "        output_path=compliance_path,\n", "        project_name=PROJECT_NAME\n", "    )\n", "    \n", "    # Export JSON\n", "    json_path = export_compliance_json(\n", "        matches=matches,\n", "        spacing_deviations=spacing_deviations,\n", "        verticality_deviations=verticality_deviations,\n", "        rotation_deviations=rotation_deviations,\n", "        detected_piles=detected_piles,\n", "        design_piles=design_piles,\n", "        compliance_df=compliance_df,\n", "        output_path=compliance_path,\n", "        project_name=PROJECT_NAME\n", "    )\n", "    \n", "    exported_files['json_file'] = json_path\n", "    \n", "    print(f\"\\n✅ Compliance analysis stage complete! Output files:\")\n", "    for file_type, file_path in exported_files.items():\n", "        if file_path:\n", "            print(f\"  - {file_type.replace('_', ' ').title()}: {file_path.name}\")\n", "    \n", "    print(f\"\\n🔄 Next Steps:\")\n", "    print(f\"  - Review compliance analysis results\")\n", "    print(f\"  - Identify non-compliant piles for remediation\")\n", "    print(f\"  - Generate compliance reports for stakeholders\")\n", "    \n", "else:\n", "    print(\"❌ No compliance analysis results to export.\")\n", "    print(\"   Ensure pile detection and design data are available.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}