{"cells": [{"cell_type": "markdown", "metadata": {"id": "-Sr_cKkJ7NfY"}, "source": ["# 🏗️ IFC Metadata Extraction for Preprocessing\n", "\n", "This notebook extracts metadata from Industry Foundation Classes (IFC) files as part of the preprocessing stage. It produces structured hierarchical metadata and flat tabular summaries for pile detection workflows.\n", "\n", "**Stage**: Preprocessing  \n", "**Input Data**: IFC files (.ifc)  \n", "**Output**: Extracted IFC/CAD metadata (pile specs, positions, layer info, etc.)  \n", "**Format**: .json (for structured hierarchical metadata), .csv (for flat tabular summaries like pile positions)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load IFC Model**: Import .ifc files using ifcopenshell\n", "2. **Extract Pile Elements**: Filter and identify pile/column elements\n", "3. **Coordinate Transformation**: Convert from local to global coordinates\n", "4. **Export Structured Metadata**: Save as .json (hierarchical) and .csv (tabular)"]}, {"cell_type": "markdown", "source": ["## Setup and Installation\n", "\n", "First, let's install the necessary dependencies and import required libraries."], "metadata": {"id": "gI1hbkYA7ZxR"}}, {"cell_type": "code", "source": ["# Install required packages\n", "# Uncomment and run this cell if you need to install the packages\n", "\n", "!python -m pip install ifcopenshell open3d numpy matplotlib pyproj pandas"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CfbXEDY07Wh3", "outputId": "23b65ab3-b090-46dd-ee73-b4c6629b41a5"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting ifcopenshell\n", "  Downloading ifcopenshell-0.8.2-py311-none-manylinux_2_31_x86_64.whl.metadata (11 kB)\n", "Collecting open3d\n", "  Downloading open3d-0.19.0-cp311-cp311-manylinux_2_31_x86_64.whl.metadata (4.3 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (2.0.2)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (3.10.0)\n", "Requirement already satisfied: pyproj in /usr/local/lib/python3.11/dist-packages (3.7.1)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (2.2.2)\n", "Requirement already satisfied: shapely in /usr/local/lib/python3.11/dist-packages (from ifcopenshell) (2.1.0)\n", "Collecting isodate (from ifcopenshell)\n", "  Downloading isodate-0.7.2-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: python-dateutil in /usr/local/lib/python3.11/dist-packages (from ifcopenshell) (2.9.0.post0)\n", "Collecting lark (from ifcopenshell)\n", "  Downloading lark-1.2.2-py3-none-any.whl.metadata (1.8 kB)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.11/dist-packages (from ifcopenshell) (4.13.2)\n", "Collecting dash>=2.6.0 (from open3d)\n", "  Downloading dash-3.0.4-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: werkzeug>=3.0.0 in /usr/local/lib/python3.11/dist-packages (from open3d) (3.1.3)\n", "Requirement already satisfied: flask>=3.0.0 in /usr/local/lib/python3.11/dist-packages (from open3d) (3.1.1)\n", "Requirement already satisfied: nbformat>=5.7.0 in /usr/local/lib/python3.11/dist-packages (from open3d) (5.10.4)\n", "Collecting configargparse (from open3d)\n", "  Downloading configargparse-1.7.1-py3-none-any.whl.metadata (24 kB)\n", "Collecting ipywidgets>=8.0.4 (from open3d)\n", "  Downloading ipywidgets-8.1.7-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting addict (from open3d)\n", "  Downloading addict-2.4.0-py3-none-any.whl.metadata (1.0 kB)\n", "Requirement already satisfied: pillow>=9.3.0 in /usr/local/lib/python3.11/dist-packages (from open3d) (11.2.1)\n", "Requirement already satisfied: pyyaml>=5.4.1 in /usr/local/lib/python3.11/dist-packages (from open3d) (6.0.2)\n", "Requirement already satisfied: scikit-learn>=0.21 in /usr/local/lib/python3.11/dist-packages (from open3d) (1.6.1)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from open3d) (4.67.1)\n", "Collecting pyquaternion (from open3d)\n", "  Downloading pyquaternion-0.9.9-py3-none-any.whl.metadata (1.4 kB)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (4.58.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (24.2)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from pyproj) (2025.4.26)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas) (2025.2)\n", "Collecting flask>=3.0.0 (from open3d)\n", "  Downloading flask-3.0.3-py3-none-any.whl.metadata (3.2 kB)\n", "Collecting werkzeug>=3.0.0 (from open3d)\n", "  Downloading werkzeug-3.0.6-py3-none-any.whl.metadata (3.7 kB)\n", "Requirement already satisfied: plotly>=5.0.0 in /usr/local/lib/python3.11/dist-packages (from dash>=2.6.0->open3d) (5.24.1)\n", "Requirement already satisfied: importlib-metadata in /usr/local/lib/python3.11/dist-packages (from dash>=2.6.0->open3d) (8.7.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from dash>=2.6.0->open3d) (2.32.3)\n", "Collecting retrying (from dash>=2.6.0->open3d)\n", "  Downloading retrying-1.3.4-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: nest-asyncio in /usr/local/lib/python3.11/dist-packages (from dash>=2.6.0->open3d) (1.6.0)\n", "Requirement already satisfied: setuptools in /usr/local/lib/python3.11/dist-packages (from dash>=2.6.0->open3d) (75.2.0)\n", "Requirement already satisfied: Jinja2>=3.1.2 in /usr/local/lib/python3.11/dist-packages (from flask>=3.0.0->open3d) (3.1.6)\n", "Requirement already satisfied: itsdangerous>=2.1.2 in /usr/local/lib/python3.11/dist-packages (from flask>=3.0.0->open3d) (2.2.0)\n", "Requirement already satisfied: click>=8.1.3 in /usr/local/lib/python3.11/dist-packages (from flask>=3.0.0->open3d) (8.2.0)\n", "Requirement already satisfied: blinker>=1.6.2 in /usr/local/lib/python3.11/dist-packages (from flask>=3.0.0->open3d) (1.9.0)\n", "Collecting comm>=0.1.3 (from ipywidgets>=8.0.4->open3d)\n", "  Downloading comm-0.2.2-py3-none-any.whl.metadata (3.7 kB)\n", "Requirement already satisfied: ipython>=6.1.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets>=8.0.4->open3d) (7.34.0)\n", "Requirement already satisfied: traitlets>=4.3.1 in /usr/local/lib/python3.11/dist-packages (from ipywidgets>=8.0.4->open3d) (5.7.1)\n", "Collecting widgetsnbextension~=4.0.14 (from ipywidgets>=8.0.4->open3d)\n", "  Downloading widgetsnbextension-4.0.14-py3-none-any.whl.metadata (1.6 kB)\n", "Requirement already satisfied: jupyterlab_widgets~=3.0.15 in /usr/local/lib/python3.11/dist-packages (from ipywidgets>=8.0.4->open3d) (3.0.15)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /usr/local/lib/python3.11/dist-packages (from nbformat>=5.7.0->open3d) (2.21.1)\n", "Requirement already satisfied: jsonschema>=2.6 in /usr/local/lib/python3.11/dist-packages (from nbformat>=5.7.0->open3d) (4.23.0)\n", "Requirement already satisfied: jupyter-core!=5.0.*,>=4.12 in /usr/local/lib/python3.11/dist-packages (from nbformat>=5.7.0->open3d) (5.7.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil->ifcopenshell) (1.17.0)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn>=0.21->open3d) (1.15.3)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn>=0.21->open3d) (1.5.0)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn>=0.21->open3d) (3.6.0)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from werkzeug>=3.0.0->open3d) (3.0.2)\n", "Collecting jedi>=0.16 (from ipython>=6.1.0->ipywidgets>=8.0.4->open3d)\n", "  Downloading jedi-0.19.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.11/dist-packages (from ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (4.4.2)\n", "Requirement already satisfied: pickleshare in /usr/local/lib/python3.11/dist-packages (from ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (0.7.5)\n", "Requirement already satisfied: prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (3.0.51)\n", "Requirement already satisfied: pygments in /usr/local/lib/python3.11/dist-packages (from ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (2.19.1)\n", "Requirement already satisfied: backcall in /usr/local/lib/python3.11/dist-packages (from ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (0.2.0)\n", "Requirement already satisfied: matplotlib-inline in /usr/local/lib/python3.11/dist-packages (from ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (0.1.7)\n", "Requirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.11/dist-packages (from ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (4.9.0)\n", "Requirement already satisfied: attrs>=22.2.0 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.24.0)\n", "Requirement already satisfied: platformdirs>=2.5 in /usr/local/lib/python3.11/dist-packages (from jupyter-core!=5.0.*,>=4.12->nbformat>=5.7.0->open3d) (4.3.8)\n", "Requirement already satisfied: tenacity>=6.2.0 in /usr/local/lib/python3.11/dist-packages (from plotly>=5.0.0->dash>=2.6.0->open3d) (9.1.2)\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib-metadata->dash>=2.6.0->open3d) (3.21.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->dash>=2.6.0->open3d) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->dash>=2.6.0->open3d) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->dash>=2.6.0->open3d) (2.4.0)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /usr/local/lib/python3.11/dist-packages (from jedi>=0.16->ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (0.8.4)\n", "Requirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.11/dist-packages (from pexpect>4.3->ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (0.7.0)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.11/dist-packages (from prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0->ipython>=6.1.0->ipywidgets>=8.0.4->open3d) (0.2.13)\n", "Downloading ifcopenshell-0.8.2-py311-none-manylinux_2_31_x86_64.whl (40.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m40.8/40.8 MB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading open3d-0.19.0-cp311-cp311-manylinux_2_31_x86_64.whl (447.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m447.7/447.7 MB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dash-3.0.4-py3-none-any.whl (7.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.9/7.9 MB\u001b[0m \u001b[31m83.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading flask-3.0.3-py3-none-any.whl (101 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m101.7/101.7 kB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading ipywidgets-8.1.7-py3-none-any.whl (139 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m139.8/139.8 kB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading werkzeug-3.0.6-py3-none-any.whl (227 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m228.0/228.0 kB\u001b[0m \u001b[31m16.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading addict-2.4.0-py3-none-any.whl (3.8 kB)\n", "Downloading configargparse-1.7.1-py3-none-any.whl (25 kB)\n", "Downloading isodate-0.7.2-py3-none-any.whl (22 kB)\n", "Downloading lark-1.2.2-py3-none-any.whl (111 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m111.0/111.0 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pyquaternion-0.9.9-py3-none-any.whl (14 kB)\n", "Downloading comm-0.2.2-py3-none-any.whl (7.2 kB)\n", "Downloading widgetsnbextension-4.0.14-py3-none-any.whl (2.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m59.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading retrying-1.3.4-py3-none-any.whl (11 kB)\n", "Downloading jedi-0.19.2-py2.py3-none-any.whl (1.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m56.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: addict, widgetsnbextension, werkzeug, retrying, pyquaternion, lark, jedi, isodate, configargparse, comm, ifcopenshell, flask, ipywidgets, dash, open3d\n", "  Attempting uninstall: widgetsnbextension\n", "    Found existing installation: widgetsnbextension 3.6.10\n", "    Uninstalling widgetsnbextension-3.6.10:\n", "      Successfully uninstalled widgetsnbextension-3.6.10\n", "  Attempting uninstall: werkzeug\n", "    Found existing installation: Werkzeug 3.1.3\n", "    Uninstalling Werkzeug-3.1.3:\n", "      Successfully uninstalled Werkzeug-3.1.3\n", "  Attempting uninstall: flask\n", "    Found existing installation: Flask 3.1.1\n", "    Uninstalling Flask-3.1.1:\n", "      Successfully uninstalled Flask-3.1.1\n", "  Attempting uninstall: i<PERSON><PERSON><PERSON><PERSON>\n", "    Found existing installation: ipywidgets 7.7.1\n", "    Uninstalling ipywidgets-7.7.1:\n", "      Successfully uninstalled ipywidgets-7.7.1\n", "Successfully installed addict-2.4.0 comm-0.2.2 configargparse-1.7.1 dash-3.0.4 flask-3.0.3 ifcopenshell-0.8.2 ipywidgets-8.1.7 isodate-0.7.2 jedi-0.19.2 lark-1.2.2 open3d-0.19.0 pyquaternion-0.9.9 retrying-1.3.4 werkzeug-3.0.6 widgetsnbextension-4.0.14\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "AqISY-pv7Nfa"}, "source": ["## Import Required Libraries"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "XiDO-qf-7Nfa"}, "outputs": [], "source": ["import ifcopenshell\n", "import pandas as pd\n", "import json\n", "import os\n", "from pathlib import Path\n", "from datetime import datetime\n", "from pyproj import Transformer\n", "import logging\n", "from collections import Counter\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO,\n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Project organization - adjust based on your project\n", "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "\n", "# Input and output paths following the specified organization\n", "input_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'raw'\n", "output_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'preprocessing'\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"🏗️ IFC Metadata Extraction - Ready!\")\n", "print(f\"📁 Input path: {input_path}\")\n", "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n", "print(f\"💾 Output path: {output_path}\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "uOUEA4RL7Nfb"}, "source": ["## Load IFC Model\n", "\n", "Specify the path to the IFC file and load it using ifcopenshell."]}, {"cell_type": "code", "source": ["# Load IFC file from the input directory\n", "# Expected input: IFC files (.ifc) in the raw data directory\n", "\n", "# Look for IFC files in the input directory\n", "ifc_files = list(input_path.glob('*.ifc'))\n", "\n", "if not ifc_files:\n", "    print(f\"❌ No IFC files found in {input_path}\")\n", "    print(\"Please place your .ifc files in the input directory.\")\n", "    \n", "    # For demonstration, create a fallback path\n", "    fallback_path = Path('/content/gdrive/MyDrive/pc-experiment/GRE.EEC.S.00.IT.P.14353.00.265.ifc')\n", "    if fallback_path.exists():\n", "        ifc_file_path = fallback_path\n", "        print(f\"🔧 Using fallback IFC file: {ifc_file_path}\")\n", "    else:\n", "        raise FileNotFoundError(f\"No IFC files found in {input_path}\")\n", "else:\n", "    # Use the first IFC file found\n", "    ifc_file_path = ifc_files[0]\n", "    print(f\"✅ Found IFC file: {ifc_file_path}\")\n", "\n", "print(f\"📊 Processing: {ifc_file_path.name}\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8XxD8B2-7S7K", "outputId": "fe350bab-6fc0-4901-b9a2-76d89c2029a6"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/gdrive\n"]}]}, {"cell_type": "code", "source": ["!ls -l /content/gdrive/MyDrive/pc-experiment/"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5_HbeKYG8xTH", "outputId": "bfe4d9e1-b855-4f6a-a879-1ba3a7225004"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["total 7376894\n", "drwx------ 2 <USER> <GROUP>       4096 May 12 13:36 'CAD files'\n", "-rw------- 1 <USER> <GROUP>    6497528 May  2 07:22 'GRE.EEC.D.21.IT.P.14353.00.379.02 Ramming Layout_pag 1_rev02 - <PERSON><PERSON><PERSON>.dwg'\n", "-rw------- 1 <USER> <GROUP>  196111387 May  2 07:12  GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "-rw------- 1 <USER> <GROUP>    5657870 May 13 08:18  ifc_downsampled.ply\n", "drwx------ 2 <USER> <GROUP>       4096 May 13 07:51  output\n", "drwx------ 2 <USER> <GROUP>       4096 May 27 12:20  ransac\n", "-rw------- 1 <USER> <GROUP> 7345486180 May  6  2024  Trino_Fly_2_Shifted.las\n", "-rw------- 1 <USER> <GROUP>     172832 May 27 12:24  Trino_Fly_2_Shifted.ply\n"]}]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_sknwMtZ7Nfb", "outputId": "28307661-37da-47cb-80e6-45ac600bfc5d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Successfully loaded IFC model from /content/gdrive/MyDrive/pc-experiment/GRE.EEC.S.00.IT.P.14353.00.265.ifc\n"]}], "source": ["# Load the IFC model\n", "try:\n", "    model = ifcopenshell.open(str(ifc_file_path))\n", "    print(f\"✅ Successfully loaded IFC model: {ifc_file_path.name}\")\n", "    \n", "    # Get basic model information\n", "    all_elements = list(model)\n", "    print(f\"📊 Total elements in model: {len(all_elements):,}\")\n", "    \n", "    # Analyze element types\n", "    element_types = [e.is_a() for e in all_elements]\n", "    type_counts = Counter(element_types)\n", "    \n", "    print(f\"\\n🔍 Top 10 element types:\")\n", "    for element_type, count in type_counts.most_common(10):\n", "        print(f\"  - {element_type}: {count:,}\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ Error loading IFC model: {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {"id": "MfxllFnh7Nfb"}, "source": ["## Filter Relevant Elements\n", "\n", "Extract pile elements from the IFC model."]}, {"cell_type": "code", "source": ["from collections import Counter\n", "\n", "element_types = [e.is_a() for e in model]\n", "print(Counter(element_types).most_common(30))\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "n1R1OD6z9ahb", "outputId": "f16b9038-4334-4a84-fc90-45f73dea92b7"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[('IfcPropertySingleValue', 687868), ('IfcIndexedPolygonalFace', 318849), ('IfcPropertySet', 279465), ('IfcRelDefinesByProperties', 279463), ('IfcAxis2Placement3D', 109093), ('IfcCartesianPoint', 99058), ('IfcLocalPlacement', 91933), ('IfcDirection', 63313), ('IfcDistributionPort', 51324), ('IfcShapeRepresentation', 42064), ('IfcProductDefinitionShape', 40606), ('IfcMappedItem', 33490), ('IfcRelNests', 26145), ('IfcColumn', 14460), ('IfcRelConnectsPorts', 13410), ('IfcCartesianPointList2D', 7129), ('IfcIndexedPolyCurve', 7129), ('IfcArbitraryClosedProfileDef', 7129), ('IfcExtrudedAreaSolid', 7129), ('IfcCableCarrierSegment', 7115), ('IfcCableCarrierFitting', 6700), ('IfcElectricMotor', 6192), ('IfcSolarDevice', 5682), ('IfcRelDefinesByType', 1461), ('IfcRepresentationMap', 1458), ('IfcCartesianPointList3D', 1451), ('IfcPolygonalFaceSet', 1451), ('IfcCableCarrierFittingType', 1445), ('IfcElectricDistributionBoard', 404), ('IfcTransformer', 51)]\n"]}]}, {"cell_type": "code", "source": ["# Filter and retrieve all pile elements from the model\n", "\n", "columns = model.by_type(\"IfcColumn\")\n", "pile_candidates = []\n", "\n", "for col in columns:\n", "    name = getattr(col, \"Name\", \"\")\n", "    if \"tracker pile\" in name.lower():\n", "        pile_candidates.append(col)\n", "\n", "\n", "print(f\"Found {len(pile_candidates)} pile candidates in the IFC model\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "toB9ZL3U9_Pv", "outputId": "79762e53-af47-48c7-d737-8d3ae7bb1582"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Found 14460 pile candidates in the IFC model\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "Rb41h0OP7Nfc"}, "source": ["## Configure Coordinate Transformation\n", "\n", "Set up the coordinate transformer to convert from local coordinates (typically UTM) to global coordinates (WGS84/GPS)."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mYFaFkOL7Nfc", "outputId": "1e542ab6-5e14-46fc-f134-4c83a18998ce"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Coordinate transformer configured: EPSG:32643 → EPSG:4326\n"]}], "source": ["# Define source and target coordinate reference systems\n", "# Note: Replace 'EPSG:32643' with the actual CRS of your IFC model if different\n", "source_crs = \"EPSG:32643\"  # UTM Zone 43N\n", "target_crs = \"EPSG:4326\"   # WGS84 (standard GPS coordinates)\n", "\n", "# Create the coordinate transformer\n", "transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)\n", "print(f\"Coordinate transformer configured: {source_crs} → {target_crs}\")"]}, {"cell_type": "markdown", "metadata": {"id": "SbV05RyC7Nfd"}, "source": ["## Extract <PERSON><PERSON><PERSON> from Pile Elements\n", "\n", "Iterate through the pile elements to extract relevant metadata and coordinates."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 224}, "id": "h3W9uFUZ7Nfd", "outputId": "034075e8-7eb6-475f-d79d-4cd2ae5a5da9"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Extracted metadata for 14460 pile elements\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["                   Pile No.        Tracker No. Pile Type         x         y  \\\n", "0  TRPL_Tracker Pile:952577  TRPL_Tracker Pile    COLUMN -232.5025  508.4820   \n", "1  TRPL_Tracker Pile:952578  TRPL_Tracker Pile    COLUMN -232.5025  516.7745   \n", "2  TRPL_Tracker Pile:952579  TRPL_Tracker Pile    COLUMN -232.5025  525.0670   \n", "3  TRPL_Tracker Pile:952580  TRPL_Tracker Pile    COLUMN -232.5025  500.1895   \n", "4  TRPL_Tracker Pile:952581  TRPL_Tracker Pile    COLUMN -232.5025  533.3595   \n", "\n", "            z  Longitude  Latitude  \n", "0  156.830786  70.509173  0.004586  \n", "1  156.830786  70.509173  0.004661  \n", "2  156.830786  70.509173  0.004736  \n", "3  156.830786  70.509173  0.004511  \n", "4  156.830786  70.509173  0.004811  "], "text/html": ["\n", "  <div id=\"df-c3934d8a-db27-456d-8331-551b12cdb364\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON>.</th>\n", "      <th>Tracker No.</th>\n", "      <th>Pile Type</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>z</th>\n", "      <th>Longitude</th>\n", "      <th>Latitude</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TRPL_Tracker Pile:952577</td>\n", "      <td>TRPL_Tracker Pile</td>\n", "      <td>COLUMN</td>\n", "      <td>-232.5025</td>\n", "      <td>508.4820</td>\n", "      <td>156.830786</td>\n", "      <td>70.509173</td>\n", "      <td>0.004586</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>TRPL_Tracker Pile:952578</td>\n", "      <td>TRPL_Tracker Pile</td>\n", "      <td>COLUMN</td>\n", "      <td>-232.5025</td>\n", "      <td>516.7745</td>\n", "      <td>156.830786</td>\n", "      <td>70.509173</td>\n", "      <td>0.004661</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TRPL_Tracker Pile:952579</td>\n", "      <td>TRPL_Tracker Pile</td>\n", "      <td>COLUMN</td>\n", "      <td>-232.5025</td>\n", "      <td>525.0670</td>\n", "      <td>156.830786</td>\n", "      <td>70.509173</td>\n", "      <td>0.004736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TRPL_Tracker Pile:952580</td>\n", "      <td>TRPL_Tracker Pile</td>\n", "      <td>COLUMN</td>\n", "      <td>-232.5025</td>\n", "      <td>500.1895</td>\n", "      <td>156.830786</td>\n", "      <td>70.509173</td>\n", "      <td>0.004511</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TRPL_Tracker Pile:952581</td>\n", "      <td>TRPL_Tracker Pile</td>\n", "      <td>COLUMN</td>\n", "      <td>-232.5025</td>\n", "      <td>533.3595</td>\n", "      <td>156.830786</td>\n", "      <td>70.509173</td>\n", "      <td>0.004811</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c3934d8a-db27-456d-8331-551b12cdb364')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-c3934d8a-db27-456d-8331-551b12cdb364 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-c3934d8a-db27-456d-8331-551b12cdb364');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-e5ac3452-1797-415c-be19-ffbfbef068d4\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-e5ac3452-1797-415c-be19-ffbfbef068d4')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-e5ac3452-1797-415c-be19-ffbfbef068d4 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "ifc_df", "summary": "{\n  \"name\": \"ifc_df\",\n  \"rows\": 14460,\n  \"fields\": [\n    {\n      \"column\": \"Pile No.\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 14460,\n        \"samples\": [\n          \"TRPL_Tracker Pile:1182148\",\n          \"TRPL_Tracker Pile:1005494\",\n          \"TRPL_Tracker Pile:1246716\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Tracker No.\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"TRPL_Tracker Pile\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Pile Type\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"COLUMN\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"x\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 319.6144447797344,\n        \"min\": -716.989500000025,\n        \"max\": 735.7625000000115,\n        \"num_unique_values\": 717,\n        \"samples\": [\n          440.07649999996636\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"y\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 336.38356728541964,\n        \"min\": -769.9582999995722,\n        \"max\": 791.737499999851,\n        \"num_unique_values\": 14453,\n        \"samples\": [\n          376.4065000005438\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"z\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.8678902036445253,\n        \"min\": 153.13409999999988,\n        \"max\": 157.66100000000023,\n        \"num_unique_values\": 11671,\n        \"samples\": [\n          155.3567000000002\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Longitude\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0028634223393837525,\n        \"min\": 70.50483263403912,\n        \"max\": 70.51784783803552,\n        \"num_unique_values\": 14459,\n        \"samples\": [\n          70.50559862403121\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Latitude\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0030339730667906287,\n        \"min\": -0.006944551847193244,\n        \"max\": 0.007140998442552249,\n        \"num_unique_values\": 14460,\n        \"samples\": [\n          0.0013986101557091457\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 13}], "source": ["import re\n", "\n", "# Initialize list to store metadata records\n", "records = []\n", "\n", "# Process each pile element\n", "for pile in pile_candidates:\n", "    guid = pile.GlobalId\n", "    name = pile.Name\n", "\n", "    # Improved regex: extract final numeric ID as Pile No. and first part as Tracker No.\n", "    pile_match = re.search(r':(\\d+)$', name)\n", "    tracker_match = re.search(r'^(.*?):', name)\n", "\n", "    pile_no = f\"TRPL_Tracker Pile:{pile_match.group(1)}\" if pile_match else name\n", "    tracker_no = tracker_match.group(1) if tracker_match else \"\"\n", "\n", "    pile_type = pile.PredefinedType if hasattr(pile, \"PredefinedType\") else \"\"\n", "\n", "    shape = pile.ObjectPlacement\n", "    if shape and hasattr(shape, \"RelativePlacement\"):\n", "        location = shape.RelativePlacement.Location\n", "        coords = location.Coordinates\n", "        if len(coords) >= 2:\n", "            x, y = coords[0], coords[1]\n", "            z = coords[2] if len(coords) > 2 else 0.0\n", "            lon, lat = transformer.transform(x, y)\n", "            records.append({\n", "                \"Pile No.\": pile_no,\n", "                \"Tracker No.\": tracker_no,\n", "                \"Pile Type\": pile_type,\n", "                \"x\": x,\n", "                \"y\": y,\n", "                \"z\": z,\n", "                \"Longitude\": lon,\n", "                \"Latitude\": lat\n", "            })\n", "        else:\n", "          print(f\"Warning: Unable to extract geometric information for pile {name} (GUID: {guid})\")\n", "\n", "# Convert records to DataFrame\n", "ifc_df = pd.DataFrame(records)\n", "\n", "# Display sample of the extracted data\n", "print(f\"Extracted metadata for {len(records)} pile elements\")\n", "ifc_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "S2JQazJA7Nfd"}, "source": ["## 📤 Export Metadata in Specified Formats\n", "\n", "Export the extracted IFC metadata in the required formats:\n", "- **.json**: Structured hierarchical metadata\n", "- **.csv**: Flat tabular summaries (pile positions)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "s7TRolhx7Nfe", "outputId": "3ac9268d-91d6-47b6-97d4-84241c2<PERSON><PERSON>"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["IFC metadata successfully exported to ../data/aligned_ifc_metadata.csv\n", "\n", "Summary of extracted metadata:\n", "Total pile elements: 14460\n", "Unique pile types: 1\n", "Sample coordinate ranges:\n", "  X: -716.99 to 735.76\n", "  Y: -769.96 to 791.74\n", "  Z: 153.13 to 157.66\n"]}], "source": ["# Export in specified formats\n", "if len(records) > 0:\n", "    \n", "    # 1. Export structured hierarchical metadata as JSON\n", "    ifc_metadata = {\n", "        'project_info': {\n", "            'project_name': PROJECT_NAME,\n", "            'project_type': PROJECT_TYPE,\n", "            'ifc_file': ifc_file_path.name,\n", "            'extraction_timestamp': datetime.now().isoformat(),\n", "            'total_elements': len(all_elements),\n", "            'pile_elements_found': len(records)\n", "        },\n", "        'coordinate_system': {\n", "            'local_crs': 'IFC Local Coordinates',\n", "            'global_crs': 'WGS84',\n", "            'transformation_applied': True\n", "        },\n", "        'pile_elements': [],\n", "        'summary_statistics': {\n", "            'total_piles': len(records),\n", "            'pile_types': dict(ifc_df['Pile Type'].value_counts()),\n", "            'coordinate_ranges': {\n", "                'x_range': [float(ifc_df['x'].min()), float(ifc_df['x'].max())],\n", "                'y_range': [float(ifc_df['y'].min()), float(ifc_df['y'].max())],\n", "                'z_range': [float(ifc_df['z'].min()), float(ifc_df['z'].max())]\n", "            }\n", "        }\n", "    }\n", "    \n", "    # Add detailed pile information\n", "    for _, row in ifc_df.iterrows():\n", "        pile_info = {\n", "            'GlobalId': row['<PERSON>le No.'].split(':')[-1] if ':' in str(row['<PERSON>le No.']) else str(row['Pile No.']),\n", "            'Name': row['<PERSON><PERSON> No.'],\n", "            'ObjectType': row['Pile Type'],\n", "            'TrackerGroup': row['Tracker No.'],\n", "            'LocalCoordinates': {\n", "                'X_Local': float(row['x']),\n", "                'Y_Local': float(row['y']),\n", "                'Z_Local': float(row['z'])\n", "            },\n", "            'GlobalCoordinates': {\n", "                'Longitude': float(row['Longitude']),\n", "                'Latitude': float(row['Latitude'])\n", "            }\n", "        }\n", "        ifc_metadata['pile_elements'].append(pile_info)\n", "    \n", "    # Save JSON metadata (structured hierarchical)\n", "    json_path = output_path / f'{PROJECT_NAME}_ifc_metadata.json'\n", "    with open(json_path, 'w') as f:\n", "        json.dump(ifc_metadata, f, indent=2)\n", "    print(f\"💾 Saved structured metadata: {json_path}\")\n", "    print(f\"   Format: JSON with hierarchical IFC metadata\")\n", "    \n", "    # 2. Export flat tabular summary as CSV (pile positions)\n", "    pile_positions = ifc_df[['<PERSON>le No.', 'x', 'y', 'z', 'Pile Type', 'Longitude', 'Latitude']].copy()\n", "    pile_positions = pile_positions.rename(columns={\n", "        'Pile No.': 'pile_id',\n", "        'Pile Type': 'pile_type'\n", "    })\n", "    \n", "    csv_path = output_path / f'{PROJECT_NAME}_pile_positions.csv'\n", "    pile_positions.to_csv(csv_path, index=False)\n", "    print(f\"💾 Saved pile positions: {csv_path}\")\n", "    print(f\"   Format: CSV with flat tabular pile positions\")\n", "    \n", "    # Display summary statistics\n", "    print(f\"\\n📊 Extraction Summary:\")\n", "    print(f\"  Total pile elements: {len(ifc_df):,}\")\n", "    print(f\"  Unique pile types: {ifc_df['Pile Type'].nunique()}\")\n", "    print(f\"  Coordinate ranges:\")\n", "    print(f\"    X: {ifc_df['x'].min():.2f} to {ifc_df['x'].max():.2f} m\")\n", "    print(f\"    Y: {ifc_df['y'].min():.2f} to {ifc_df['y'].max():.2f} m\")\n", "    print(f\"    Z: {ifc_df['z'].min():.2f} to {ifc_df['z'].max():.2f} m\")\n", "    \n", "    print(f\"\\n✅ Preprocessing complete! Output files:\")\n", "    print(f\"  - Structured metadata: {json_path.name}\")\n", "    print(f\"  - Pile positions: {csv_path.name}\")\n", "    \n", "else:\n", "    print(\"❌ No pile elements found to export.\")"]}, {"cell_type": "markdown", "metadata": {"id": "yvSU289e7Nfe"}, "source": ["## 📝 Summary\n", "\n", "This preprocessing notebook successfully extracted IFC metadata in the specified formats:\n", "\n", "### ✅ **What We Accomplished:**\n", "1. **Loaded IFC Files**: Processed .ifc files from the raw data directory\n", "2. **Extracted Pile Elements**: Identified and filtered pile/column elements\n", "3. **Coordinate Transformation**: Converted local to global coordinates\n", "4. **Exported Structured Metadata**: Saved hierarchical JSON format\n", "5. **Exported Tabular Summary**: Saved flat CSV format for pile positions\n", "\n", "### 📊 **Output Formats:**\n", "- **JSON**: `{PROJECT_NAME}_ifc_metadata.json` - Structured hierarchical metadata\n", "- **CSV**: `{PROJECT_NAME}_pile_positions.csv` - Flat tabular summaries\n", "\n", "### 🔄 **Next Steps:**\n", "The extracted metadata can now be used for:\n", "- **Pile Detection Validation**: Reference data for validation workflows\n", "- **Point Cloud Alignment**: Ground truth for ICP alignment\n", "- **Compliance Analysis**: Baseline for deviation calculations\n", "- **Visualization**: Pile location mapping and reporting\n", "\n", "**📧 Contact**: For questions about IFC processing, reach out to the development team."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}