{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC Metadata Extraction\n", "\n", "This notebook extracts metadata from Industry Foundation Classes (IFC) files for preprocessing workflows.\n", "\n", "**Stage**: Preprocessing  \n", "**Input Data**: IFC files (.ifc)  \n", "**Output**: Structured metadata in CSV, JSON, and Parquet formats  \n", "**Schema**: Standardized pile/element specification format  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load IFC Model**: Import .ifc files using ifcopenshell\n", "2. **Extract Pile Elements**: Filter and identify pile/column elements\n", "3. **Property Extraction**: Extract material properties and specifications\n", "4. **Coordinate Transformation**: Convert from local to global coordinates\n", "5. **Export Structured Data**: Save in multiple formats for downstream usage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Trino\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "ifc_file_path = \"\"  # Path to specific IFC file (optional)\n", "coordinate_system = \"EPSG:32643\"  # Source coordinate reference system\n", "target_crs = \"EPSG:4326\"  # Target CRS (WGS84 for GPS coordinates)\n", "\n", "# Extraction parameters\n", "pile_filter_keywords = [\"tracker pile\", \"pile\", \"column\"]  # Keywords to identify piles\n", "include_properties = True  # Extract IFC properties\n", "include_geometry = True  # Extract geometric information\n", "include_materials = True  # Extract material properties\n", "coordinate_transform = True  # Transform coordinates to target CRS\n", "\n", "# Element type filters\n", "target_element_types = [\"IfcColumn\", \"IfcPile\", \"IfcBuildingElementProxy\"]  # IFC types to extract\n", "\n", "# Output format options\n", "export_csv = True  # Export tabular data as CSV\n", "export_json = True  # Export hierarchical metadata as JSON\n", "export_parquet = True  # Export as Parquet for large datasets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import os\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import Counter\n", "import logging\n", "import re\n", "\n", "# IFC processing\n", "try:\n", "    import ifcopenshell\n", "    import ifcopenshell.geom\n", "    IFC_SUPPORT = True\n", "    print(f\"IFC support available: {ifcopenshell.version}\")\n", "except ImportError:\n", "    print(\"IFC support not available. Install ifcopenshell for IFC processing.\")\n", "    IFC_SUPPORT = False\n", "\n", "# Optional imports\n", "try:\n", "    import pyarrow as pa\n", "    import pyarrow.parquet as pq\n", "    PARQUET_SUPPORT = True\n", "    print(f\"Parquet support available: {pa.__version__}\")\n", "except ImportError:\n", "    print(\"Parquet support not available. Install pyarrow for Parquet export.\")\n", "    PARQUET_SUPPORT = False\n", "\n", "try:\n", "    from pyproj import Transformer\n", "    COORDINATE_TRANSFORM_SUPPORT = True\n", "    print(f\"Coordinate transformation support available\")\n", "except ImportError:\n", "    print(\"Coordinate transformation not available. Install pyproj for CRS transformation.\")\n", "    COORDINATE_TRANSFORM_SUPPORT = False\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Create output directory structure for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_ifc_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Input and output paths\n", "if ifc_file_path:\n", "    input_path = Path(ifc_file_path)\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "\n", "preprocessing_path = data_path / project_type / site_name / 'preprocessing'\n", "preprocessing_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"IFC Metadata Extraction - Ready!\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {preprocessing_path}\")\n", "print(f\"Current run output: {current_run_path}\")\n", "print(f\"Coordinate system: {coordinate_system} -> {target_crs}\")\n", "print(f\"Target element types: {', '.join(target_element_types)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the standardized schema\n", "import sys\n", "sys.path.append('.')\n", "from metadata_schema import ElementMetadata, MetadataSchema, MetadataExporter, create_element_from_ifc_element\n", "\n", "def find_ifc_files():\n", "    \"\"\"\n", "    Find IFC files in the input directory.\n", "    \"\"\"\n", "    if ifc_file_path and Path(ifc_file_path).exists():\n", "        return [Path(ifc_file_path)]\n", "    \n", "    search_path = input_path if input_path.is_dir() else input_path.parent\n", "    ifc_files = list(search_path.glob('*.ifc'))\n", "    \n", "    return ifc_files\n", "\n", "def load_ifc_file(file_path):\n", "    \"\"\"\n", "    Load IFC file using ifcopenshell.\n", "    \"\"\"\n", "    if not IFC_SUPPORT:\n", "        raise ImportError(\"IFC support not available. Please install ifcopenshell.\")\n", "    \n", "    try:\n", "        model = ifcopenshell.open(str(file_path))\n", "        \n", "        print(f\"Successfully loaded IFC model: {file_path.name}\")\n", "        \n", "        # Get basic model information\n", "        all_elements = list(model)\n", "        print(f\"Total elements in model: {len(all_elements):,}\")\n", "        \n", "        # Analyze element types\n", "        element_types = [e.is_a() for e in all_elements]\n", "        type_counts = Counter(element_types)\n", "        \n", "        print(f\"Top 10 element types:\")\n", "        for element_type, count in type_counts.most_common(10):\n", "            print(f\"  - {element_type}: {count:,}\")\n", "        \n", "        return model\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading IFC model: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load and Process IFC Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find and load IFC files\n", "ifc_files = find_ifc_files()\n", "\n", "if not ifc_files:\n", "    print(f\"No IFC files found in {input_path}\")\n", "    print(\"Please place your IFC files (.ifc) in the input directory.\")\n", "    raise FileNotFoundError(f\"No IFC files found in {input_path}\")\n", "\n", "print(f\"Found {len(ifc_files)} IFC file(s):\")\n", "for file_path in ifc_files:\n", "    print(f\"  - {file_path.name}\")\n", "\n", "# Process the first available IFC file\n", "ifc_file_path = ifc_files[0]\n", "model = load_ifc_file(ifc_file_path)\n", "\n", "if model is None:\n", "    raise ValueError(f\"Failed to load IFC file: {ifc_file_path}\")\n", "\n", "print(f\"\\nIFC Model Analysis:\")\n", "print(f\"File: {ifc_file_path.name}\")\n", "print(f\"Schema: {model.schema}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Pile Elements"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter and extract relevant elements\n", "pile_candidates = []\n", "\n", "# Search by element types\n", "for element_type in target_element_types:\n", "    try:\n", "        elements = model.by_type(element_type)\n", "        print(f\"Found {len(elements)} {element_type} elements\")\n", "        \n", "        for element in elements:\n", "            # Check if element name matches pile keywords\n", "            element_name = getattr(element, \"Name\", \"\")\n", "            if element_name and any(keyword.lower() in element_name.lower() for keyword in pile_filter_keywords):\n", "                pile_candidates.append(element)\n", "            elif not pile_filter_keywords:  # If no keywords specified, include all\n", "                pile_candidates.append(element)\n", "                \n", "    except Exception as e:\n", "        print(f\"Error processing {element_type}: {e}\")\n", "\n", "print(f\"\\nFound {len(pile_candidates)} pile candidates after filtering\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract <PERSON><PERSON><PERSON> from IFC Elements"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract metadata from IFC elements\n", "elements = []\n", "\n", "print(f\"Processing {len(pile_candidates)} IFC elements...\")\n", "\n", "for ifc_element in pile_candidates:\n", "    try:\n", "        # Extract basic properties\n", "        element_id = getattr(ifc_element, 'GlobalId', '')\n", "        element_name = getattr(ifc_element, 'Name', '')\n", "        element_type = ifc_element.is_a()\n", "        \n", "        # Extract coordinates from placement\n", "        x, y, z = 0.0, 0.0, 0.0\n", "        placement = getattr(ifc_element, 'ObjectPlacement', None)\n", "        \n", "        if placement and hasattr(placement, 'RelativePlacement'):\n", "            location = placement.RelativePlacement.Location\n", "            if hasattr(location, 'Coordinates'):\n", "                coords = location.Coordinates\n", "                x = coords[0] if len(coords) > 0 else 0.0\n", "                y = coords[1] if len(coords) > 1 else 0.0\n", "                z = coords[2] if len(coords) > 2 else 0.0\n", "        \n", "        # Extract properties if requested\n", "        properties = {}\n", "        material_name = None\n", "        \n", "        if include_properties:\n", "            # Get property sets\n", "            try:\n", "                property_sets = ifcopenshell.util.element.get_psets(ifc_element)\n", "                for pset_name, pset_props in property_sets.items():\n", "                    for prop_name, prop_value in pset_props.items():\n", "                        if prop_name != 'id':  # Skip internal IFC id\n", "                            properties[f\"{pset_name}.{prop_name}\"] = prop_value\n", "            except:\n", "                pass  # Property extraction failed, continue without properties\n", "        \n", "        # Extract material information if requested\n", "        if include_materials:\n", "            try:\n", "                materials = ifcopenshell.util.element.get_materials(ifc_element)\n", "                if materials:\n", "                    material_name = materials[0].Name if hasattr(materials[0], 'Name') else str(materials[0])\n", "            except:\n", "                pass  # Material extraction failed, continue without materials\n", "        \n", "        # Extract geometric information if requested\n", "        width, height, depth = None, None, None\n", "        if include_geometry:\n", "            try:\n", "                # This is a simplified approach - full geometry extraction would require more complex processing\n", "                representation = getattr(ifc_element, 'Representation', None)\n", "                if representation:\n", "                    # Extract basic dimensional information from representation\n", "                    # This would need to be expanded based on specific IFC geometry types\n", "                    pass\n", "            except:\n", "                pass  # Geometry extraction failed, continue without geometry\n", "        \n", "        # Create standardized element metadata\n", "        element = ElementMetadata(\n", "            element_id=element_id,\n", "            element_name=element_name,\n", "            element_type=element_type,\n", "            source_file=ifc_file_path.name,\n", "            source_type=\"IFC\",\n", "            x_local=x,\n", "            y_local=y,\n", "            z_local=z,\n", "            width=width,\n", "            height=height,\n", "            depth=depth,\n", "            material_name=material_name,\n", "            attributes=properties if properties else None,\n", "            extraction_timestamp=datetime.now().isoformat(),\n", "            coordinate_system=coordinate_system\n", "        )\n", "        \n", "        elements.append(element)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing element {getattr(ifc_element, 'GlobalId', 'unknown')}: {e}\")\n", "\n", "print(f\"Extracted metadata for {len(elements)} elements\")\n", "\n", "# Convert to DataFrame\n", "if elements:\n", "    df = pd.DataFrame([element.to_dict() for element in elements])\n", "    df = MetadataSchema.standardize_dataframe(df)\n", "    \n", "    print(f\"\\nDataFrame shape: {df.shape}\")\n", "    print(f\"Element types: {df['element_type'].value_counts().to_dict()}\")\n", "    \n", "    # Display sample data\n", "    print(f\"\\nSample data:\")\n", "    display(df.head())\n", "else:\n", "    df = MetadataSchema.create_empty_dataframe()\n", "    print(\"No elements extracted\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Coordinate Transformation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Transform coordinates if requested and data is available\n", "if coordinate_transform and len(df) > 0 and COORDINATE_TRANSFORM_SUPPORT:\n", "    try:\n", "        # Create coordinate transformer\n", "        transformer = Transformer.from_crs(coordinate_system, target_crs, always_xy=True)\n", "        print(f\"Transforming coordinates: {coordinate_system} -> {target_crs}\")\n", "        \n", "        # Transform coordinates for each element\n", "        transformed_coords = []\n", "        for _, row in df.iterrows():\n", "            try:\n", "                lon, lat = transformer.transform(row['x_local'], row['y_local'])\n", "                transformed_coords.append({'longitude': lon, 'latitude': lat, 'elevation': row['z_local']})\n", "            except Exception as e:\n", "                print(f\"Error transforming coordinates for element {row['element_id']}: {e}\")\n", "                transformed_coords.append({'longitude': None, 'latitude': None, 'elevation': None})\n", "        \n", "        # Add transformed coordinates to DataFrame\n", "        transform_df = pd.DataFrame(transformed_coords)\n", "        df['longitude'] = transform_df['longitude']\n", "        df['latitude'] = transform_df['latitude']\n", "        df['elevation'] = transform_df['elevation']\n", "        \n", "        print(f\"Coordinate transformation completed for {len(df)} elements\")\n", "        \n", "        # Display coordinate bounds\n", "        if df['longitude'].notna().any():\n", "            print(f\"Coordinate bounds:\")\n", "            print(f\"  Longitude: {df['longitude'].min():.6f} to {df['longitude'].max():.6f}\")\n", "            print(f\"  Latitude: {df['latitude'].min():.6f} to {df['latitude'].max():.6f}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Coordinate transformation failed: {e}\")\n", "        print(\"Continuing without coordinate transformation\")\n", "        \n", "elif coordinate_transform and not COORDINATE_TRANSFORM_SUPPORT:\n", "    print(\"Coordinate transformation requested but pyproj not available\")\n", "    print(\"Install pyproj for coordinate transformation support\")\n", "    \n", "elif len(df) == 0:\n", "    print(\"No data available for coordinate transformation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export results in multiple formats\n", "if len(df) > 0:\n", "    # Create exporter\n", "    exporter = MetadataExporter(current_run_path, site_name, \"IFC\")\n", "    \n", "    # Prepare metadata for JSON export\n", "    extraction_metadata = {\n", "        'extraction_parameters': {\n", "            'site_name': site_name,\n", "            'project_type': project_type,\n", "            'coordinate_system': coordinate_system,\n", "            'target_crs': target_crs,\n", "            'pile_filter_keywords': pile_filter_keywords,\n", "            'include_properties': include_properties,\n", "            'include_geometry': include_geometry,\n", "            'include_materials': include_materials,\n", "            'coordinate_transform': coordinate_transform,\n", "            'target_element_types': target_element_types\n", "        },\n", "        'source_file_info': {\n", "            'filename': ifc_file_path.name,\n", "            'file_size': ifc_file_path.stat().st_size,\n", "            'ifc_schema': model.schema if model else 'unknown'\n", "        },\n", "        'processing_info': {\n", "            'total_candidates': len(pile_candidates),\n", "            'extracted_elements': len(df),\n", "            'extraction_timestamp': timestamp\n", "        }\n", "    }\n", "    \n", "    # Export in all requested formats\n", "    exported_files = exporter.export_all_formats(df, extraction_metadata)\n", "    \n", "    print(f\"\\nExport completed:\")\n", "    for format_type, file_path in exported_files.items():\n", "        print(f\"  {format_type.upper()}: {file_path}\")\n", "    \n", "    # Also save to main preprocessing directory\n", "    main_csv_path = preprocessing_path / f\"{site_name}_ifc_metadata.csv\"\n", "    df.to_csv(main_csv_path, index=False)\n", "    print(f\"  Main CSV: {main_csv_path}\")\n", "    \n", "else:\n", "    print(\"No data to export\")\n", "\n", "print(f\"\\nIFC metadata extraction completed for {site_name}\")\n", "print(f\"Results saved to: {current_run_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}