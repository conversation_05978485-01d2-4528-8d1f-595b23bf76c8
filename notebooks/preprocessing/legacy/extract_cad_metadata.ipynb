{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🏗️ CAD Metadata Extraction for Preprocessing\n", "\n", "This notebook extracts metadata and geometric information from CAD files as part of the preprocessing stage. It processes DXF/DWG files and produces structured hierarchical metadata and flat tabular summaries for pile detection workflows.\n", "\n", "**Stage**: Preprocessing  \n", "**Input Data**: CAD files (.dwg, .dxf)  \n", "**Output**: Extracted IFC/CAD metadata (pile specs, positions, layer info, etc.)  \n", "**Format**: .json (for structured hierarchical metadata), .csv (for flat tabular summaries like pile positions)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load CAD Files**: Import .dwg/.dxf files using ezdxf\n", "2. **Extract Construction Elements**: Identify pile/column INSERT blocks\n", "3. **Geometric Analysis**: Process 3D geometry and measurements\n", "4. **Layer Information**: Extract layer and attribute data\n", "5. **Export Structured Metadata**: Save as .json (hierarchical) and .csv (tabular)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import ezdxf\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import os\n", "import re\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import Counter\n", "\n", "# Import CADQuery for advanced 3D processing\n", "try:\n", "    import cadquery as cq\n", "    CADQUERY_AVAILABLE = True\n", "except ImportError:\n", "    print(\"⚠️ CADQuery not available. Basic processing will be used.\")\n", "    CADQUERY_AVAILABLE = False\n", "\n", "# For coordinate transformations if needed\n", "from pyproj import Transformer\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Project organization - adjust based on your project\n", "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "PROJECT_NAME = \"Castro\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "\n", "# Input and output paths following the specified organization\n", "input_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'raw'\n", "output_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'preprocessing'\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"🏗️ CAD Metadata Extraction - Ready!\")\n", "print(f\"📁 Input path: {input_path}\")\n", "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n", "print(f\"💾 Output path: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load DXF File\n", "\n", "Specify the path to the DXF file and load it using ezdxf."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Load CAD file from the input directory\n", "# Expected input: CAD files (.dwg, .dxf) in the raw data directory\n", "\n", "# Look for CAD files in the input directory\n", "dxf_files = list(input_path.glob('*.dxf'))\n", "dwg_files = list(input_path.glob('*.dwg'))\n", "\n", "cad_file_path = None\n", "\n", "if dxf_files:\n", "    # Use the first DXF file found\n", "    cad_file_path = dxf_files[0]\n", "    print(f\"✅ Found DXF file: {cad_file_path}\")\n", "elif dwg_files:\n", "    print(f\"⚠️ Found DWG file: {dwg_files[0]}\")\n", "    print(\"DWG files need to be converted to DXF format first.\")\n", "    print(\"Please use the convert_dwg_to_dxf.sh script or convert manually.\")\n", "    # For demonstration, we'll assume conversion was done\n", "    cad_file_path = dwg_files[0].with_suffix('.dxf')\n", "    if not cad_file_path.exists():\n", "        print(f\"❌ Converted DXF file not found: {cad_file_path}\")\n", "        raise FileNotFoundError(f\"Please convert {dwg_files[0]} to DXF format\")\n", "else:\n", "    print(f\"❌ No CAD files (.dxf or .dwg) found in {input_path}\")\n", "    print(\"Please place your CAD files in the input directory.\")\n", "    raise FileNotFoundError(f\"No CAD files found in {input_path}\")\n", "\n", "# Load the DXF file\n", "try:\n", "    doc = ezdxf.readfile(str(cad_file_path))\n", "    modelspace = doc.modelspace()\n", "    print(f\"✅ Successfully loaded CAD file: {cad_file_path.name}\")\n", "    \n", "    # Get basic file information\n", "    print(f\"📊 DXF version: {doc.dxfversion}\")\n", "    \n", "except ezdxf.DXFError as e:\n", "    print(f\"❌ Error loading DXF file: {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display Basic DXF Information\n", "\n", "Examine the structure and content of the DXF file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Display DXF file version\n", "print(f\"DXF version: {doc.dxfversion}\")\n", "\n", "# Display available layers\n", "layers = doc.layers\n", "print(f\"\\nAvailable layers ({len(layers)}):\\n{', '.join([layer.dxf.name for layer in layers])}\")\n", "\n", "# Display available blocks\n", "blocks = doc.blocks\n", "print(f\"\\nAvailable blocks ({len(blocks)}):\\n{', '.join([block.name for block in blocks if not block.name.startswith('*')][:10])}{'...' if len(blocks) > 10 else ''}\")\n", "\n", "# Count entity types in modelspace\n", "entity_types = {}\n", "for entity in modelspace:\n", "    entity_type = entity.dxftype()\n", "    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1\n", "\n", "print(\"\\nEntity types in modelspace:\")\n", "for entity_type, count in entity_types.items():\n", "    print(f\"  {entity_type}: {count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import DXF into CADQuery\n", "\n", "We'll use CADQuery to process the DXF file for advanced geometric analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Import the DXF using CADQuery\n", "try:\n", "    # Import DXF file into CADQuery\n", "    cq_model = cq.importers.importDXF(dxf_file_path)\n", "    print(f\"Successfully imported DXF into CADQuery\")\n", "    \n", "    # Display the imported model\n", "    # If you're running this in CQ-editor or JupyterLab with the right extensions,\n", "    # you can visualize the model with: show(cq_model)\n", "    \n", "    # Extract wires from the model\n", "    wires = cq_model.wires()\n", "    print(f\"Found {len(wires.objects)} wires in the DXF file\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error importing DXF into CADQuery: {e}\")\n", "    print(\"Continuing with basic ezdxf processing...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create 3D Model from 2D DXF\n", "\n", "If the DXF contains 2D profiles, we can extrude them to create 3D models for analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Function to create 3D solids from 2D profiles\n", "def create_3d_from_profile(workplane, extrusion_height=10.0):\n", "    try:\n", "        # Extrude the 2D profile to create a 3D solid\n", "        solid = workplane.toPending().extrude(extrusion_height)\n", "        return solid\n", "    except Exception as e:\n", "        print(f\"Error creating 3D model: {e}\")\n", "        return None\n", "\n", "# Try to create 3D models from the DXF profiles\n", "try:\n", "    # Check if we have a valid CADQuery model\n", "    if 'cq_model' in locals() and cq_model is not None:\n", "        # Extrude to create 3D\n", "        extrusion_height = 20.0  # Default extrusion height in model units\n", "        model_3d = create_3d_from_profile(cq_model, extrusion_height)\n", "        \n", "        if model_3d is not None:\n", "            print(f\"Successfully created 3D model with extrusion height of {extrusion_height} units\")\n", "            \n", "            # Calculate volume and other properties\n", "            volume = model_3d.val().Volume()\n", "            center_of_mass = model_3d.val().CenterOfMass()\n", "            \n", "            print(f\"3D Model Properties:\")\n", "            print(f\"  Volume: {volume:.2f} cubic units\")\n", "            print(f\"  Center of Mass: X={center_of_mass.x:.2f}, Y={center_of_mass.y:.2f}, Z={center_of_mass.z:.2f}\")\n", "            \n", "            # You can save the 3D model in various formats if needed\n", "            # model_3d.val().exportStep(\"../data/3d_model.step\")\n", "    else:\n", "        print(\"No valid CADQuery model available for 3D creation\")\n", "except Exception as e:\n", "    print(f\"Error in 3D model creation: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract <PERSON><PERSON><PERSON> from INSERT Entities\n", "\n", "Find and process INSERT entities (block references) which typically represent construction elements like piles."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Initialize list to store metadata records\n", "records = []\n", "\n", "# Extract metadata from INSERT entities\n", "for entity in modelspace.query(\"INSERT\"):\n", "    try:\n", "        # Extract basic properties\n", "        block_name = entity.dxf.name\n", "        x, y, z = entity.dxf.insert\n", "        layer = entity.dxf.layer\n", "        \n", "        # Extract rotation if available\n", "        rotation = entity.dxf.rotation if hasattr(entity.dxf, \"rotation\") else 0\n", "        \n", "        # Extract scale if available\n", "        scale_x = entity.dxf.xscale if hasattr(entity.dxf, \"xscale\") else 1\n", "        scale_y = entity.dxf.yscale if hasattr(entity.dxf, \"yscale\") else 1\n", "        scale_z = entity.dxf.zscale if hasattr(entity.dxf, \"zscale\") else 1\n", "        \n", "        # Use naming convention like \"PILE_952577\" to extract Pile No.\n", "        pile_match = re.search(r\"PILE_(\\d+)\", block_name)\n", "        pile_no = f\"PILE:{pile_match.group(1)}\" if pile_match else block_name\n", "        \n", "        # Get entity handle (unique identifier)\n", "        handle = entity.dxf.handle\n", "        \n", "        # Extract attributes if available\n", "        attributes = {}\n", "        if hasattr(entity, \"attribs\") and entity.attribs:\n", "            for attrib in entity.attribs:\n", "                attributes[attrib.dxf.tag] = attrib.dxf.text\n", "        \n", "        # Create record with all extracted information\n", "        record = {\n", "            \"Handle\": handle,\n", "            \"Pile No.\": pile_no,\n", "            \"X_Local\": x,\n", "            \"Y_Local\": y,\n", "            \"Z_Local\": z,\n", "            \"Rotation\": rotation,\n", "            \"Scale_X\": scale_x,\n", "            \"Scale_Y\": scale_y,\n", "            \"Scale_Z\": scale_z,\n", "            \"Layer\": layer,\n", "            \"Block Name\": block_name\n", "        }\n", "        \n", "        # Add attributes to record\n", "        for tag, value in attributes.items():\n", "            record[f\"Attr_{tag}\"] = value\n", "            \n", "        records.append(record)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing entity: {e}\")\n", "\n", "# Convert records to DataFrame\n", "df = pd.DataFrame(records)\n", "\n", "# Display sample of the extracted data\n", "print(f\"Extracted metadata for {len(records)} INSERT entities\")\n", "if len(df) > 0:\n", "    display(df.head())\n", "else:\n", "    print(\"No INSERT entities found in the DXF file\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced Geometric Analysis with CADQuery\n", "\n", "Use CADQuery to perform advanced geometric analysis on the entities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Function to analyze block geometry using CADQuery\n", "def analyze_block_geometry(block_name):\n", "    try:\n", "        # Get the block definition\n", "        block = doc.blocks.get(block_name)\n", "        if block is None:\n", "            return {}\n", "        \n", "        # Collect all entities in the block\n", "        block_entities = list(block)\n", "        if not block_entities:\n", "            return {}\n", "        \n", "        # Count entity types in the block\n", "        entity_counts = {}\n", "        for entity in block_entities:\n", "            entity_type = entity.dxftype()\n", "            entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1\n", "        \n", "        # Try to create a CADQuery representation of the block\n", "        # This is a simplified approach - in practice, you'd need to convert\n", "        # each entity type appropriately\n", "        \n", "        # Calculate bounding box (approximate)\n", "        x_coords, y_coords, z_coords = [], [], []\n", "        \n", "        for entity in block_entities:\n", "            if entity.dxftype() == 'LINE':\n", "                x_coords.extend([entity.dxf.start[0], entity.dxf.end[0]])\n", "                y_coords.extend([entity.dxf.start[1], entity.dxf.end[1]])\n", "                z_coords.extend([entity.dxf.start[2], entity.dxf.end[2]])\n", "            elif entity.dxftype() == 'CIRCLE':\n", "                center = entity.dxf.center\n", "                radius = entity.dxf.radius\n", "                x_coords.extend([center[0] - radius, center[0] + radius])\n", "                y_coords.extend([center[1] - radius, center[1] + radius])\n", "                z_coords.append(center[2])\n", "        \n", "        # If we have coordinates, calculate the bounding box\n", "        if x_coords and y_coords:\n", "            min_x, max_x = min(x_coords), max(x_coords)\n", "            min_y, max_y = min(y_coords), max(y_coords)\n", "            width = max_x - min_x\n", "            height = max_y - min_y\n", "            \n", "            return {\n", "                \"Entity_Types\": entity_counts,\n", "                \"Width\": width,\n", "                \"Height\": height,\n", "                \"Area\": width * height,  # Approximate area of bounding box\n", "                \"Entity_Count\": len(block_entities)\n", "            }\n", "        \n", "        return {\"Entity_Count\": len(block_entities), \"Entity_Types\": entity_counts}\n", "    \n", "    except Exception as e:\n", "        print(f\"Error analyzing block {block_name}: {e}\")\n", "        return {}\n", "\n", "# Analyze geometry for each unique block in our records\n", "if len(df) > 0 and 'Block Name' in df.columns:\n", "    unique_blocks = df['Block Name'].unique()\n", "    \n", "    print(f\"Analyzing geometry for {len(unique_blocks)} unique blocks...\")\n", "    \n", "    # Store analysis results\n", "    block_analysis = {}\n", "    \n", "    for block_name in unique_blocks:\n", "        block_analysis[block_name] = analyze_block_geometry(block_name)\n", "    \n", "    # Add geometric analysis to our dataframe\n", "    for idx, row in df.iterrows():\n", "        block_name = row['Block Name']\n", "        if block_name in block_analysis and 'Width' in block_analysis[block_name]:\n", "            df.at[idx, 'Block_Width'] = block_analysis[block_name]['Width']\n", "            df.at[idx, 'Block_Height'] = block_analysis[block_name]['Height']\n", "            df.at[idx, 'Block_Area'] = block_analysis[block_name]['Area']\n", "            df.at[idx, 'Block_Entity_Count'] = block_analysis[block_name]['Entity_Count']\n", "    \n", "    # Display updated dataframe\n", "    print(\"Enhanced dataframe with geometric analysis:\")\n", "    display(df.head())\n", "else:\n", "    print(\"No blocks available for geometric analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optional: Transform Coordinates to Geographic Projection\n", "\n", "If the coordinate reference system (CRS) is known, transform local coordinates to geographic coordinates."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Uncomment and modify this section if coordinate transformation is needed\n", "\"\"\"\n", "from pyproj import Transformer\n", "\n", "# Define source and target coordinate reference systems\n", "# Replace 'EPSG:XYZ' with the actual CRS of your CAD drawing\n", "source_crs = \"EPSG:XYZ\"  # Replace with your local CRS (e.g., EPSG:32643 for UTM Zone 43N)\n", "target_crs = \"EPSG:4326\" # WGS84 (standard GPS coordinates)\n", "\n", "# Create the coordinate transformer\n", "transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)\n", "print(f\"Coordinate transformer configured: {source_crs} → {target_crs}\")\n", "\n", "# Transform coordinates\n", "if 'X_Local' in df.columns and 'Y_Local' in df.columns:\n", "    # Create new columns for transformed coordinates\n", "    df['Longitude'] = 0.0\n", "    df['Latitude'] = 0.0\n", "    \n", "    # Apply transformation to each row\n", "    for idx, row in df.iterrows():\n", "        lon, lat = transformer.transform(row['X_Local'], row['Y_Local'])\n", "        df.at[idx, 'Longitude'] = lon\n", "        df.at[idx, 'Latitude'] = lat\n", "    \n", "    print(\"Coordinate transformation completed\")\n", "else:\n", "    print(\"Coordinate transformation skipped - X_Local or Y_Local columns not found\")\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Entities in 2D\n", "\n", "Create a 2D visualization of the extracted entities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Create a 2D visualization of the entities\n", "if len(df) > 0 and 'X_Local' in df.columns and 'Y_Local' in df.columns:\n", "    plt.figure(figsize=(12, 10))\n", "    \n", "    # Plot each entity as a point\n", "    scatter = plt.scatter(df['X_Local'], df['Y_Local'], \n", "                         c=df['Layer'].astype('category').cat.codes, \n", "                         s=50, alpha=0.8, cmap='viridis')\n", "    \n", "    # Add labels if we have pile numbers\n", "    if 'Pile No.' in df.columns:\n", "        for idx, row in df.iterrows():\n", "            plt.annotate(row['<PERSON>le No.'], \n", "                         (row['X_Local'], row['Y_Local']),\n", "                         fontsize=8, alpha=0.7,\n", "                         xytext=(5, 5), textcoords='offset points')\n", "    \n", "    # Add a legend for layers\n", "    if 'Layer' in df.columns:\n", "        unique_layers = df['Layer'].unique()\n", "        handles, labels = scatter.legend_elements()\n", "        plt.legend(handles, unique_layers, title=\"Layers\")\n", "    \n", "    plt.title('2D Visualization of CAD Entities')\n", "    plt.xlabel('X Coordinate')\n", "    plt.ylabel('Y Coordinate')\n", "    plt.axis('equal')  # Equal aspect ratio\n", "    plt.grid(True, linestyle='--', alpha=0.7)\n", "    plt.show()\n", "else:\n", "    print(\"Cannot create visualization - missing coordinate data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze Extracted Metadata\n", "\n", "Perform basic analysis on the extracted data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Skip this cell if DataFrame is empty\n", "if len(df) == 0:\n", "    print(\"No data to analyze\")\n", "else:\n", "    # Display basic statistics\n", "    print(\"\\nBasic statistics of numerical columns:\")\n", "    print(df.describe())\n", "    \n", "    # Count entities by layer\n", "    print(\"\\nEntity count by layer:\")\n", "    print(df['Layer'].value_counts())\n", "    \n", "    # Count entities by block name\n", "    print(\"\\nEntity count by block name:\")\n", "    print(df['Block Name'].value_counts().head(10))\n", "    \n", "    # Coordinate ranges\n", "    print(\"\\nCoordinate ranges:\")\n", "    print(f\"  X: {df['X_Local'].min():.2f} to {df['X_Local'].max():.2f}\")\n", "    print(f\"  Y: {df['Y_Local'].min():.2f} to {df['Y_Local'].max():.2f}\")\n", "    if 'Z_Local' in df.columns and not df['Z_Local'].isna().all():\n", "        print(f\"  Z: {df['Z_Local'].min():.2f} to {df['Z_Local'].max():.2f}\")\n", "        \n", "    # Additional geometric analysis if available\n", "    if 'Block_Area' in df.columns:\n", "        print(\"\\nGeometric statistics:\")\n", "        print(f\"  Total block area: {df['Block_Area'].sum():.2f} square units\")\n", "        print(f\"  Average block area: {df['Block_Area'].mean():.2f} square units\")\n", "        print(f\"  Average entity count per block: {df['Block_Entity_Count'].mean():.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export 3D Models\n", "\n", "If CADQuery models were created, we can export them in various formats."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Export 3D models if they were created\n", "try:\n", "    if 'model_3d' in locals() and model_3d is not None:\n", "        # Define output directory\n", "        output_dir = Path(\"../data/3d_models\")\n", "        output_dir.mkdir(exist_ok=True, parents=True)\n", "        \n", "        # Export in STEP format (good for CAD interchange)\n", "        step_path = output_dir / \"cad_model.step\"\n", "        model_3d.val().exportStep(str(step_path))\n", "        print(f\"Exported 3D model to {step_path}\")\n", "        \n", "        # Export in STL format (good for 3D printing)\n", "        stl_path = output_dir / \"cad_model.stl\"\n", "        model_3d.val().exportStl(str(stl_path))\n", "        print(f\"Exported 3D model to {stl_path}\")\n", "        \n", "        # You can also export other formats like 3MF, AMF, etc.\n", "    else:\n", "        print(\"No 3D models available for export\")\n", "except Exception as e:\n", "    print(f\"Error exporting 3D models: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📤 Export Metadata in Specified Formats\n", "\n", "Export the extracted CAD metadata in the required formats:\n", "- **.json**: Structured hierarchical metadata\n", "- **.csv**: Flat tabular summaries (pile positions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Export in specified formats\n", "if len(df) > 0:\n", "    \n", "    # 1. Export structured hierarchical metadata as JSON\n", "    cad_metadata = {\n", "        'project_info': {\n", "            'project_name': PROJECT_NAME,\n", "            'project_type': PROJECT_TYPE,\n", "            'cad_file': cad_file_path.name,\n", "            'extraction_timestamp': datetime.now().isoformat(),\n", "            'dxf_version': doc.dxfversion if 'doc' in locals() else 'unknown',\n", "            'total_entities': len(list(modelspace)) if 'modelspace' in locals() else 0,\n", "            'pile_elements_found': len(df)\n", "        },\n", "        'layer_information': {},\n", "        'pile_elements': [],\n", "        'summary_statistics': {\n", "            'total_piles': len(df),\n", "            'coordinate_ranges': {\n", "                'x_range': [float(df['x'].min()), float(df['x'].max())] if 'x' in df.columns else [0, 0],\n", "                'y_range': [float(df['y'].min()), float(df['y'].max())] if 'y' in df.columns else [0, 0],\n", "                'z_range': [float(df['z'].min()), float(df['z'].max())] if 'z' in df.columns else [0, 0]\n", "            }\n", "        }\n", "    }\n", "    \n", "    # Extract layer information if available\n", "    if 'doc' in locals():\n", "        layers = {}\n", "        for layer in doc.layers:\n", "            layers[layer.dxf.name] = {\n", "                'color': layer.dxf.color,\n", "                'linetype': layer.dxf.linetype,\n", "                'lineweight': getattr(layer.dxf, 'lineweight', 'default')\n", "            }\n", "        cad_metadata['layer_information'] = layers\n", "    \n", "    # Add detailed pile information\n", "    for _, row in df.iterrows():\n", "        pile_info = {\n", "            'block_name': row.get('block_name', 'unknown'),\n", "            'layer': row.get('layer', 'unknown'),\n", "            'coordinates': {\n", "                'x': float(row.get('x', 0)),\n", "                'y': float(row.get('y', 0)),\n", "                'z': float(row.get('z', 0))\n", "            },\n", "            'attributes': {}\n", "        }\n", "        \n", "        # Add any additional attributes\n", "        for col in df.columns:\n", "            if col not in ['block_name', 'layer', 'x', 'y', 'z']:\n", "                pile_info['attributes'][col] = row[col]\n", "        \n", "        cad_metadata['pile_elements'].append(pile_info)\n", "    \n", "    # Save JSON metadata (structured hierarchical)\n", "    json_path = output_path / f'{PROJECT_NAME}_cad_metadata.json'\n", "    with open(json_path, 'w') as f:\n", "        json.dump(cad_metadata, f, indent=2)\n", "    print(f\"💾 Saved structured metadata: {json_path}\")\n", "    print(f\"   Format: JSON with hierarchical CAD metadata\")\n", "    \n", "    # 2. Export flat tabular summary as CSV (pile positions)\n", "    pile_positions = df.copy()\n", "    \n", "    # Standardize column names\n", "    column_mapping = {\n", "        'block_name': 'pile_id',\n", "        'layer': 'pile_type'\n", "    }\n", "    \n", "    for old_col, new_col in column_mapping.items():\n", "        if old_col in pile_positions.columns:\n", "            pile_positions = pile_positions.rename(columns={old_col: new_col})\n", "    \n", "    # Ensure required columns exist\n", "    required_cols = ['pile_id', 'x', 'y', 'z']\n", "    for col in required_cols:\n", "        if col not in pile_positions.columns:\n", "            pile_positions[col] = 'unknown' if col == 'pile_id' else 0\n", "    \n", "    csv_path = output_path / f'{PROJECT_NAME}_pile_positions.csv'\n", "    pile_positions.to_csv(csv_path, index=False)\n", "    print(f\"💾 Saved pile positions: {csv_path}\")\n", "    print(f\"   Format: CSV with flat tabular pile positions\")\n", "    \n", "    # Display summary statistics\n", "    print(f\"\\n📊 Extraction Summary:\")\n", "    print(f\"  Total pile elements: {len(df):,}\")\n", "    if 'x' in df.columns and 'y' in df.columns and 'z' in df.columns:\n", "        print(f\"  Coordinate ranges:\")\n", "        print(f\"    X: {df['x'].min():.2f} to {df['x'].max():.2f}\")\n", "        print(f\"    Y: {df['y'].min():.2f} to {df['y'].max():.2f}\")\n", "        print(f\"    Z: {df['z'].min():.2f} to {df['z'].max():.2f}\")\n", "    \n", "    print(f\"\\n✅ Preprocessing complete! Output files:\")\n", "    print(f\"  - Structured metadata: {json_path.name}\")\n", "    print(f\"  - Pile positions: {csv_path.name}\")\n", "    \n", "else:\n", "    print(\"❌ No pile elements found to export.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📝 Summary\n", "\n", "This preprocessing notebook successfully extracted CAD metadata in the specified formats:\n", "\n", "### ✅ **What We Accomplished:**\n", "1. **Loaded CAD Files**: Processed .dwg/.dxf files from the raw data directory\n", "2. **Extracted Construction Elements**: Identified pile/column INSERT blocks\n", "3. **Geometric Analysis**: Processed 3D geometry and measurements\n", "4. **Layer Information**: Extracted layer and attribute data\n", "5. **Exported Structured Metadata**: Saved hierarchical JSON format\n", "6. **Exported Tabular Summary**: Saved flat CSV format for pile positions\n", "\n", "### 📊 **Output Formats:**\n", "- **JSON**: `{PROJECT_NAME}_cad_metadata.json` - Structured hierarchical metadata\n", "- **CSV**: `{PROJECT_NAME}_pile_positions.csv` - Flat tabular summaries\n", "\n", "### 🔄 **Next Steps:**\n", "The extracted metadata can now be used for:\n", "- **Pile Detection Validation**: Reference data for CAD-only sites (Castro, Mudjar, Giorgio)\n", "- **Rule-Based Detection**: Input for geometric constraint algorithms\n", "- **Point Cloud Alignment**: Ground truth for alignment workflows\n", "- **Compliance Analysis**: Baseline for deviation calculations\n", "- **Advanced Geometric Analysis**: Detailed measurements and spatial relationships\n", "\n", "**📧 Contact**: For questions about CAD processing, reach out to the development team."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}