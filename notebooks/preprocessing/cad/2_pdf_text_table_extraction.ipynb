{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD PDF Text and Table Extraction\n", "\n", "This notebook extracts text and tabular data from CAD PDF files using pdfplumber and PyMuPDF libraries.\n", "\n", "**Stage**: Preprocessing - CAD Pipeline  \n", "**Input Data**: PDF files discovered from previous stage  \n", "**Output**: Extracted text content and structured table data  \n", "**Format**: Text strings and pandas DataFrames  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Extract all text and tabular data from CAD PDF files to enable downstream analysis and information retrieval. This includes:\n", "- Raw text extraction for content analysis\n", "- Table detection and structured data extraction\n", "- Text cleaning and formatting\n", "- Data validation and quality checks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Use `pdfplumber` as primary extraction tool for text and tables\n", "2. Fallback to `PyMuPDF` for problematic files\n", "3. Detect and extract tables as pandas DataFrames\n", "4. Clean and normalize extracted text\n", "5. Implement comprehensive error handling and logging\n", "6. Generate extraction quality metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import re\n", "import warnings\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import defaultdict\n", "from typing import List, Dict, Tuple, Optional\n", "\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF processing libraries\n", "try:\n", "    import pdfplumber\n", "    PDFPLUMBER_AVAILABLE = True\n", "    print(\"pdfplumber library loaded successfully\")\n", "except ImportError:\n", "    PDFPLUMBER_AVAILABLE = False\n", "    print(\"Warning: pdfplumber not available\")\n", "\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "    print(\"PyMuPDF library loaded successfully\")\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"Warning: PyMuPDF not available\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore', category=UserWarning)\n", "\n", "print(\"CAD PDF Text and Table Extraction - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for required libraries\n", "if not PDFPLUMBER_AVAILABLE and not PYMUPDF_AVAILABLE:\n", "    raise ImportError(\"Neither pdfplumber nor PyMuPDF is available. Please install at least one: pip install pdfplumber pymupdf\")\n", "\n", "# Load discovered PDF files from previous notebook\n", "# If running standalone, uncomment and modify the path below\n", "try:\n", "    # Try to use results from previous notebook\n", "    pdf_files = globals().get('discovered_pdf_files', [])\n", "    if not pdf_files:\n", "        # Fallback: load from data directory\n", "        project_root = Path('../../../')\n", "        cad_data_path = project_root / 'data' / 'cad'\n", "        pdf_files = [str(p) for p in cad_data_path.rglob('*.pdf')]\n", "        logger.info(f\"Loaded {len(pdf_files)} PDF files from directory scan\")\n", "    else:\n", "        logger.info(f\"Using {len(pdf_files)} PDF files from previous notebook\")\n", "        \n", "except Exception as e:\n", "    logger.error(f\"Error loading PDF file list: {e}\")\n", "    pdf_files = []\n", "\n", "print(f\"Processing {len(pdf_files)} PDF files for text and table extraction\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Text cleaning utilities\n", "def clean_text(text: str) -> str:\n", "    \"\"\"Clean and normalize extracted text.\"\"\"\n", "    if not text:\n", "        return \"\"\n", "    \n", "    # Remove excessive whitespace\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Remove special characters that might interfere with processing\n", "    text = re.sub(r'[\\x00-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f-\\xff]', '', text)\n", "    \n", "    # Strip leading/trailing whitespace\n", "    text = text.strip()\n", "    \n", "    return text\n", "\n", "def extract_text_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"Extract text and tables using pdfplumber.\"\"\"\n", "    full_text = \"\"\n", "    tables = []\n", "    \n", "    try:\n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            logger.debug(f\"Processing {len(pdf.pages)} pages with pdfplumber\")\n", "            \n", "            for page_num, page in enumerate(pdf.pages, 1):\n", "                # Extract text\n", "                page_text = page.extract_text()\n", "                if page_text:\n", "                    full_text += f\"\\n--- Page {page_num} ---\\n{page_text}\\n\"\n", "                \n", "                # Extract tables\n", "                page_tables = page.extract_tables()\n", "                for table_num, table in enumerate(page_tables, 1):\n", "                    if table and len(table) > 1:  # Ensure table has content\n", "                        try:\n", "                            df = pd.DataFrame(table[1:], columns=table[0])\n", "                            df.name = f\"Page_{page_num}_Table_{table_num}\"\n", "                            tables.append(df)\n", "                            logger.debug(f\"Extracted table {table_num} from page {page_num}: {df.shape}\")\n", "                        except Exception as e:\n", "                            logger.warning(f\"Error creating DataFrame from table on page {page_num}: {e}\")\n", "    \n", "    except Exception as e:\n", "        logger.error(f\"Error processing {pdf_path} with pdfplumber: {e}\")\n", "        raise\n", "    \n", "    return clean_text(full_text), tables\n", "\n", "def extract_text_pymupdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"Extract text using PyMuPDF (fallback method).\"\"\"\n", "    full_text = \"\"\n", "    tables = []  # PyMuPDF doesn't have built-in table extraction\n", "    \n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        logger.debug(f\"Processing {len(doc)} pages with PyMuPDF\")\n", "        \n", "        for page_num in range(len(doc)):\n", "            page = doc.load_page(page_num)\n", "            page_text = page.get_text()\n", "            if page_text:\n", "                full_text += f\"\\n--- Page {page_num + 1} ---\\n{page_text}\\n\"\n", "        \n", "        doc.close()\n", "    \n", "    except Exception as e:\n", "        logger.error(f\"Error processing {pdf_path} with PyMuPDF: {e}\")\n", "        raise\n", "    \n", "    return clean_text(full_text), tables\n", "\n", "print(\"Text extraction utilities defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize containers for extraction results\n", "extraction_results = {}\n", "extraction_errors = []\n", "extraction_stats = {\n", "    'total_files': len(pdf_files),\n", "    'successful_extractions': 0,\n", "    'failed_extractions': 0,\n", "    'total_tables': 0,\n", "    'total_text_length': 0,\n", "    'files_with_tables': 0\n", "}\n", "\n", "print(\"Starting text and table extraction...\")\n", "logger.info(f\"Beginning extraction from {len(pdf_files)} PDF files\")\n", "\n", "# Process each PDF file\n", "for i, pdf_path in enumerate(pdf_files, 1):\n", "    pdf_path_obj = Path(pdf_path)\n", "    file_name = pdf_path_obj.name\n", "    \n", "    print(f\"\\nProcessing {i}/{len(pdf_files)}: {file_name}\")\n", "    logger.info(f\"Processing file {i}: {file_name}\")\n", "    \n", "    try:\n", "        # Try pdfplumber first (better table extraction)\n", "        if PDFPLUMBER_AVAILABLE:\n", "            try:\n", "                text, tables = extract_text_pdfplumber(pdf_path)\n", "                extraction_method = \"pdfplumber\"\n", "            except Exception as e:\n", "                logger.warning(f\"pdfplumber failed for {file_name}: {e}\")\n", "                if PYMUPDF_AVAILABLE:\n", "                    text, tables = extract_text_pymupdf(pdf_path)\n", "                    extraction_method = \"pymupdf\"\n", "                else:\n", "                    raise e\n", "        else:\n", "            text, tables = extract_text_pymupdf(pdf_path)\n", "            extraction_method = \"pymupdf\"\n", "        \n", "        # Store results\n", "        extraction_results[file_name] = {\n", "            'file_path': pdf_path,\n", "            'text': text,\n", "            'tables': tables,\n", "            'extraction_method': extraction_method,\n", "            'text_length': len(text),\n", "            'table_count': len(tables),\n", "            'extraction_timestamp': datetime.now().isoformat()\n", "        }\n", "        \n", "        # Update statistics\n", "        extraction_stats['successful_extractions'] += 1\n", "        extraction_stats['total_tables'] += len(tables)\n", "        extraction_stats['total_text_length'] += len(text)\n", "        if tables:\n", "            extraction_stats['files_with_tables'] += 1\n", "        \n", "        print(f\"  Success: {len(text)} chars, {len(tables)} tables ({extraction_method})\")\n", "        \n", "    except Exception as e:\n", "        error_msg = str(e)\n", "        extraction_errors.append((file_name, error_msg))\n", "        extraction_stats['failed_extractions'] += 1\n", "        \n", "        logger.error(f\"Failed to extract from {file_name}: {error_msg}\")\n", "        print(f\"  Error: {error_msg}\")\n", "\n", "logger.info(f\"Extraction completed. Success: {extraction_stats['successful_extractions']}, Failed: {extraction_stats['failed_extractions']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display extraction summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF TEXT AND TABLE EXTRACTION SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nTotal files processed: {extraction_stats['total_files']}\")\n", "print(f\"Successful extractions: {extraction_stats['successful_extractions']}\")\n", "print(f\"Failed extractions: {extraction_stats['failed_extractions']}\")\n", "print(f\"Success rate: {extraction_stats['successful_extractions']/extraction_stats['total_files']*100:.1f}%\")\n", "\n", "print(f\"\\nTotal tables extracted: {extraction_stats['total_tables']}\")\n", "print(f\"Files with tables: {extraction_stats['files_with_tables']}\")\n", "print(f\"Total text length: {extraction_stats['total_text_length']:,} characters\")\n", "\n", "if extraction_errors:\n", "    print(\"\\nFiles with extraction errors:\")\n", "    for file_name, error_msg in extraction_errors:\n", "        print(f\"  {file_name}: {error_msg}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Preview extracted tables\n", "print(\"\\nTABLE PREVIEW:\")\n", "print(\"-\" * 40)\n", "\n", "table_count = 0\n", "for file_name, result in extraction_results.items():\n", "    if result['tables']:\n", "        print(f\"\\nFile: {file_name}\")\n", "        for i, table in enumerate(result['tables']):\n", "            table_count += 1\n", "            print(f\"\\nTable {i+1} ({table.name}): {table.shape}\")\n", "            print(\"Columns:\", list(table.columns))\n", "            \n", "            # Display first few rows\n", "            if not table.empty:\n", "                print(\"Preview:\")\n", "                display(table.head(3))\n", "            else:\n", "                print(\"Table is empty\")\n", "            \n", "            # Limit preview to first 5 tables\n", "            if table_count >= 5:\n", "                print(\"\\n... (showing first 5 tables only)\")\n", "                break\n", "        if table_count >= 5:\n", "            break\n", "\n", "if table_count == 0:\n", "    print(\"No tables were extracted from the PDF files.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Preview extracted text (sample)\n", "print(\"\\nTEXT EXTRACTION PREVIEW:\")\n", "print(\"-\" * 40)\n", "\n", "text_previews = 0\n", "for file_name, result in extraction_results.items():\n", "    if result['text'] and len(result['text'].strip()) > 50:\n", "        text_previews += 1\n", "        print(f\"\\nFile: {file_name}\")\n", "        print(f\"Text length: {result['text_length']:,} characters\")\n", "        print(f\"Extraction method: {result['extraction_method']}\")\n", "        \n", "        # Show first 300 characters\n", "        preview_text = result['text'][:300].strip()\n", "        print(f\"Preview (first 300 chars):\")\n", "        print(f\"\\\"{preview_text}...\\\"\")\n", "        \n", "        # Limit to first 3 files\n", "        if text_previews >= 3:\n", "            print(\"\\n... (showing first 3 files only)\")\n", "            break\n", "\n", "if text_previews == 0:\n", "    print(\"No substantial text content was extracted from the PDF files.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export results for downstream processing\n", "print(\"\\nEXPORTING EXTRACTION RESULTS...\")\n", "\n", "# Store results in global variables for use by subsequent notebooks\n", "globals()['pdf_extraction_results'] = extraction_results\n", "globals()['pdf_extraction_stats'] = extraction_stats\n", "globals()['pdf_extraction_errors'] = extraction_errors\n", "\n", "# Create summary DataFrame for easy analysis\n", "summary_data = []\n", "for file_name, result in extraction_results.items():\n", "    summary_data.append({\n", "        'file_name': file_name,\n", "        'file_path': result['file_path'],\n", "        'text_length': result['text_length'],\n", "        'table_count': result['table_count'],\n", "        'extraction_method': result['extraction_method'],\n", "        'extraction_timestamp': result['extraction_timestamp']\n", "    })\n", "\n", "extraction_summary_df = pd.DataFrame(summary_data)\n", "globals()['pdf_extraction_summary'] = extraction_summary_df\n", "\n", "print(f\"Results exported to variables:\")\n", "print(f\"  - pdf_extraction_results: {len(extraction_results)} file results\")\n", "print(f\"  - pdf_extraction_stats: Summary statistics\")\n", "print(f\"  - pdf_extraction_summary: DataFrame with {len(extraction_summary_df)} rows\")\n", "\n", "logger.info(\"PDF extraction results exported successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF TEXT AND TABLE EXTRACTION COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if extraction_stats['successful_extractions'] > 0:\n", "    print(f\"Successfully processed {extraction_stats['successful_extractions']} PDF files\")\n", "    print(f\"Extracted {extraction_stats['total_tables']} tables and {extraction_stats['total_text_length']:,} characters of text\")\n", "    print(\"Ready for next stage of CAD PDF processing pipeline\")\n", "else:\n", "    print(\"No PDF files were successfully processed\")\n", "    print(\"Please check file accessibility and library installations\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"PDF text and table extraction notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}