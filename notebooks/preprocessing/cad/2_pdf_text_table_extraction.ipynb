{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD PDF Text and Table Extraction\n", "\n", "This notebook extracts text and tabular data from CAD PDF files using pdfplumber and PyMuPDF libraries.\n", "\n", "**Stage**: Preprocessing - CAD Pipeline  \n", "**Input Data**: PDF files discovered from previous stage  \n", "**Output**: Extracted text content and structured table data  \n", "**Format**: Text strings and pandas DataFrames  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Extract all text and tabular data from CAD PDF files to enable downstream analysis and information retrieval. This includes:\n", "- Raw text extraction for content analysis\n", "- Table detection and structured data extraction\n", "- Text cleaning and formatting\n", "- Data validation and quality checks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Use `pdfplumber` as primary extraction tool for text and tables\n", "2. Fallback to `PyMuPDF` for problematic files\n", "3. Detect and extract tables as pandas DataFrames\n", "4. Clean and normalize extracted text\n", "5. Implement comprehensive error handling and logging\n", "6. Generate extraction quality metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pdfplumber\n", "  Using cached pdfplumber-0.11.7-py3-none-any.whl.metadata (42 kB)\n", "Collecting pymupdf\n", "  Using cached pymupdf-1.26.1-cp39-abi3-macosx_11_0_arm64.whl.metadata (3.4 kB)\n", "Collecting pdfminer.six==20250506 (from pdfplumber)\n", "  Using cached pdfminer_six-20250506-py3-none-any.whl.metadata (4.2 kB)\n", "Requirement already satisfied: Pillow>=9.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from pdfplumber) (11.0.0)\n", "Collecting pypdfium2>=4.18.0 (from pdfplumber)\n", "  Using cached pypdfium2-4.30.1-py3-none-macosx_11_0_arm64.whl.metadata (48 kB)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from pdfminer.six==20250506->pdfplumber) (3.4.0)\n", "Collecting cryptography>=36.0.0 (from pdfminer.six==20250506->pdfplumber)\n", "  Using cached cryptography-45.0.4-cp37-abi3-macosx_10_9_universal2.whl.metadata (5.7 kB)\n", "Collecting cffi>=1.14 (from cryptography>=36.0.0->pdfminer.six==20250506->pdfplumber)\n", "  Using cached cffi-1.17.1-cp39-cp39-macosx_11_0_arm64.whl.metadata (1.5 kB)\n", "Collecting pycparser (from cffi>=1.14->cryptography>=36.0.0->pdfminer.six==20250506->pdfplumber)\n", "  Using cached pycparser-2.22-py3-none-any.whl.metadata (943 bytes)\n", "Using cached pdfplumber-0.11.7-py3-none-any.whl (60 kB)\n", "Using cached pdfminer_six-20250506-py3-none-any.whl (5.6 MB)\n", "Using cached pymupdf-1.26.1-cp39-abi3-macosx_11_0_arm64.whl (22.4 MB)\n", "Using cached pypdfium2-4.30.1-py3-none-macosx_11_0_arm64.whl (2.8 MB)\n", "Using cached cryptography-45.0.4-cp37-abi3-macosx_10_9_universal2.whl (7.0 MB)\n", "Using cached cffi-1.17.1-cp39-cp39-macosx_11_0_arm64.whl (178 kB)\n", "Using cached pycparser-2.22-py3-none-any.whl (117 kB)\n", "Installing collected packages: pypdfium2, pymupdf, pycparser, cffi, cryptography, pdfminer.six, pdfplumber\n", "Successfully installed cffi-1.17.1 cryptography-45.0.4 pdfminer.six-20250506 pdfplumber-0.11.7 pycparser-2.22 pymupdf-1.26.1 pypdfium2-4.30.1\n"]}], "source": ["!python -m pip install pdfplumber pymupdf"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pdfplumber library loaded successfully\n", "PyMuPDF library loaded successfully\n", "CAD PDF Text and Table Extraction - Starting...\n", "Timestamp: 2025-06-17 12:17:14\n"]}], "source": ["import logging\n", "import re\n", "import warnings\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import defaultdict\n", "from typing import List, Dict, Tuple, Optional\n", "\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF processing libraries\n", "try:\n", "    import pdfplumber\n", "    PDFPLUMBER_AVAILABLE = True\n", "    print(\"pdfplumber library loaded successfully\")\n", "except ImportError:\n", "    PDFPLUMBER_AVAILABLE = False\n", "    print(\"Warning: pdfplumber not available\")\n", "\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "    print(\"PyMuPDF library loaded successfully\")\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"Warning: PyMuPDF not available\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore', category=UserWarning)\n", "\n", "print(\"CAD PDF Text and Table Extraction - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:17:14,870 - INFO - Loaded 33 PDF files from directory scan\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 33 PDF files for text and table extraction\n"]}], "source": ["# Check for required libraries\n", "if not PDFPLUMBER_AVAILABLE and not PYMUPDF_AVAILABLE:\n", "    raise ImportError(\"Neither pdfplumber nor PyMuPDF is available. Please install at least one: pip install pdfplumber pymupdf\")\n", "\n", "# Load discovered PDF files from previous notebook\n", "# If running standalone, uncomment and modify the path below\n", "try:\n", "    # Try to use results from previous notebook\n", "    pdf_files = globals().get('discovered_pdf_files', [])\n", "    if not pdf_files:\n", "        # Fallback: load from data directory\n", "        project_root = Path('../../../')\n", "        cad_data_path = project_root / 'data' / 'cad'\n", "        pdf_files = [str(p) for p in cad_data_path.rglob('*.pdf')]\n", "        logger.info(f\"Loaded {len(pdf_files)} PDF files from directory scan\")\n", "    else:\n", "        logger.info(f\"Using {len(pdf_files)} PDF files from previous notebook\")\n", "        \n", "except Exception as e:\n", "    logger.error(f\"Error loading PDF file list: {e}\")\n", "    pdf_files = []\n", "\n", "print(f\"Processing {len(pdf_files)} PDF files for text and table extraction\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Text extraction utilities defined\n"]}], "source": ["# Text cleaning utilities\n", "def clean_text(text: str) -> str:\n", "    \"\"\"Clean and normalize extracted text.\"\"\"\n", "    if not text:\n", "        return \"\"\n", "    \n", "    # Remove excessive whitespace\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Remove special characters that might interfere with processing\n", "    text = re.sub(r'[\\x00-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f-\\xff]', '', text)\n", "    \n", "    # Strip leading/trailing whitespace\n", "    text = text.strip()\n", "    \n", "    return text\n", "\n", "def extract_text_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"Extract text and tables using pdfplumber.\"\"\"\n", "    full_text = \"\"\n", "    tables = []\n", "    \n", "    try:\n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            logger.debug(f\"Processing {len(pdf.pages)} pages with pdfplumber\")\n", "            \n", "            for page_num, page in enumerate(pdf.pages, 1):\n", "                # Extract text\n", "                page_text = page.extract_text()\n", "                if page_text:\n", "                    full_text += f\"\\n--- Page {page_num} ---\\n{page_text}\\n\"\n", "                \n", "                # Extract tables\n", "                page_tables = page.extract_tables()\n", "                for table_num, table in enumerate(page_tables, 1):\n", "                    if table and len(table) > 1:  # Ensure table has content\n", "                        try:\n", "                            df = pd.DataFrame(table[1:], columns=table[0])\n", "                            df.name = f\"Page_{page_num}_Table_{table_num}\"\n", "                            tables.append(df)\n", "                            logger.debug(f\"Extracted table {table_num} from page {page_num}: {df.shape}\")\n", "                        except Exception as e:\n", "                            logger.warning(f\"Error creating DataFrame from table on page {page_num}: {e}\")\n", "    \n", "    except Exception as e:\n", "        logger.error(f\"Error processing {pdf_path} with pdfplumber: {e}\")\n", "        raise\n", "    \n", "    return clean_text(full_text), tables\n", "\n", "def extract_text_pymupdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"Extract text using PyMuPDF (fallback method).\"\"\"\n", "    full_text = \"\"\n", "    tables = []  # PyMuPDF doesn't have built-in table extraction\n", "    \n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        logger.debug(f\"Processing {len(doc)} pages with PyMuPDF\")\n", "        \n", "        for page_num in range(len(doc)):\n", "            page = doc.load_page(page_num)\n", "            page_text = page.get_text()\n", "            if page_text:\n", "                full_text += f\"\\n--- Page {page_num + 1} ---\\n{page_text}\\n\"\n", "        \n", "        doc.close()\n", "    \n", "    except Exception as e:\n", "        logger.error(f\"Error processing {pdf_path} with PyMuPDF: {e}\")\n", "        raise\n", "    \n", "    return clean_text(full_text), tables\n", "\n", "print(\"Text extraction utilities defined\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:17:14,885 - INFO - Beginning extraction from 33 PDF files\n", "2025-06-17 12:17:14,885 - INFO - Processing file 1: ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Starting text and table extraction...\n", "\n", "Processing 1/33: ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:17:16,607 - INFO - Processing file 2: Forum_AltheaI&II_CA_NXH_ENG_L3_03-11-2025_Rev 4.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 15946 chars, 5 tables (pdfplumber)\n", "\n", "Processing 2/33: Forum_AltheaI&II_CA_NXH_ENG_L3_03-11-2025_Rev 4.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:18:36,666 - INFO - Processing file 3: GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 52484 chars, 92 tables (pdfplumber)\n", "\n", "Processing 3/33: GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:19:45,451 - INFO - Processing file 4: GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 16024 chars, 13 tables (pdfplumber)\n", "\n", "Processing 4/33: GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:20:38,114 - INFO - Processing file 5: GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 60115 chars, 161 tables (pdfplumber)\n", "\n", "Processing 5/33: GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:20:57,211 - INFO - Processing file 6: GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 8115 chars, 25 tables (pdfplumber)\n", "\n", "Processing 6/33: GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:21:48,418 - INFO - Processing file 7: GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 91817 chars, 218 tables (pdfplumber)\n", "\n", "Processing 7/33: GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:21:57,360 - INFO - Processing file 8: GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 1794 chars, 4 tables (pdfplumber)\n", "\n", "Processing 8/33: GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:22:07,867 - INFO - Processing file 9: GRE.EEC.D.00.IT.P.12645.00.134.00.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 11635 chars, 34 tables (pdfplumber)\n", "\n", "Processing 9/33: GRE.EEC.D.00.IT.P.12645.00.134.00.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:22:31,083 - INFO - Processing file 10: Plinto Area - Alzamento a 700mm.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 25318 chars, 31 tables (pdfplumber)\n", "\n", "Processing 10/33: Plinto Area - Alzamento a 700mm.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:22:41,111 - INFO - Processing file 11: GRE.EEC.D.00.IT.P.12645.00.327.00.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 1766 chars, 6 tables (pdfplumber)\n", "\n", "Processing 11/33: GRE.EEC.D.00.IT.P.12645.00.327.00.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:22:41,453 - INFO - Processing file 12: Plinto - Alzamento a 700mm.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 6004 chars, 12 tables (pdfplumber)\n", "\n", "Processing 12/33: Plinto - Alzamento a 700mm.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:22:41,785 - INFO - Processing file 13: GRE.EEC.D.00.IT.P.12645.00.322.00.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 54 chars, 1 tables (pdfplumber)\n", "\n", "Processing 13/33: GRE.EEC.D.00.IT.P.12645.00.322.00.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:23:32,905 - INFO - Processing file 14: GRE.EEC.R.00.IT.P.12645.00.133.00.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 108034 chars, 231 tables (pdfplumber)\n", "\n", "Processing 14/33: GRE.EEC.R.00.IT.P.12645.00.133.00.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:24:30,903 - INFO - Processing file 15: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 48421 chars, 142 tables (pdfplumber)\n", "\n", "Processing 15/33: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:24:53,939 - INFO - Processing file 16: GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 2386 chars, 13 tables (pdfplumber)\n", "\n", "Processing 16/33: GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:25:00,726 - INFO - Processing file 17: GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 1809 chars, 6 tables (pdfplumber)\n", "\n", "Processing 17/33: GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:25:01,124 - INFO - Processing file 18: GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 1545 chars, 8 tables (pdfplumber)\n", "\n", "Processing 18/33: GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:26:44,817 - INFO - Processing file 19: GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 20765 chars, 90 tables (pdfplumber)\n", "\n", "Processing 19/33: GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:30:15,616 - INFO - Processing file 20: GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 21998 chars, 40 tables (pdfplumber)\n", "\n", "Processing 20/33: GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:30:17,641 - INFO - Processing file 21: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 2103 chars, 8 tables (pdfplumber)\n", "\n", "Processing 21/33: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:31:31,652 - INFO - Processing file 22: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Post.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 6858 chars, 28 tables (pdfplumber)\n", "\n", "Processing 22/33: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Post.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:31:46,642 - INFO - Processing file 23: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Ante.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 4145 chars, 5 tables (pdfplumber)\n", "\n", "Processing 23/33: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Ante.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:32:06,459 - INFO - Processing file 24: GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 1298 chars, 7 tables (pdfplumber)\n", "\n", "Processing 24/33: GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:02:28,920 - INFO - Processing file 25: GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 2156267 chars, 1024 tables (pdfplumber)\n", "\n", "Processing 25/33: GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:03:03,377 - INFO - Processing file 26: GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 7614 chars, 10 tables (pdfplumber)\n", "\n", "Processing 26/33: GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:03:04,568 - INFO - Processing file 27: GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 20331 chars, 25 tables (pdfplumber)\n", "\n", "Processing 27/33: GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:03:13,597 - INFO - Processing file 28: GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 169686 chars, 48 tables (pdfplumber)\n", "\n", "Processing 28/33: GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:18:01,780 - INFO - Processing file 29: PB01_-_POWERBLOCK_PLAN.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 13631 chars, 46 tables (pdfplumber)\n", "\n", "Processing 29/33: PB01_-_POWERBLOCK_PLAN.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:18:39,442 - INFO - Processing file 30: S-501_ FSLR S6+ Pier Tolerances  Rev.2 markup (1).pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 8534 chars, 7 tables (pdfplumber)\n", "\n", "Processing 30/33: S-501_ FSLR S6+ Pier Tolerances  Rev.2 markup (1).pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:18:43,966 - INFO - Processing file 31: PB03_-_POWERBLOCK_PLAN.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 3677 chars, 2 tables (pdfplumber)\n", "\n", "Processing 31/33: PB03_-_POWERBLOCK_PLAN.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:19:22,305 - INFO - Processing file 32: S-203_ PIER PLAN BLOCK--3 Rev.2.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 8632 chars, 12 tables (pdfplumber)\n", "\n", "Processing 32/33: S-203_ PIER PLAN BLOCK--3 Rev.2.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:19:27,015 - INFO - Processing file 33: PB02_-_POWERBLOCK_PLAN.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 2082 chars, 13 tables (pdfplumber)\n", "\n", "Processing 33/33: PB02_-_POWERBLOCK_PLAN.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:20:05,278 - INFO - Extraction completed. Success: 33, Failed: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 8535 chars, 8 tables (pdfplumber)\n"]}], "source": ["# Initialize containers for extraction results\n", "extraction_results = {}\n", "extraction_errors = []\n", "extraction_stats = {\n", "    'total_files': len(pdf_files),\n", "    'successful_extractions': 0,\n", "    'failed_extractions': 0,\n", "    'total_tables': 0,\n", "    'total_text_length': 0,\n", "    'files_with_tables': 0\n", "}\n", "\n", "print(\"Starting text and table extraction...\")\n", "logger.info(f\"Beginning extraction from {len(pdf_files)} PDF files\")\n", "\n", "# Process each PDF file\n", "for i, pdf_path in enumerate(pdf_files, 1):\n", "    pdf_path_obj = Path(pdf_path)\n", "    file_name = pdf_path_obj.name\n", "    \n", "    print(f\"\\nProcessing {i}/{len(pdf_files)}: {file_name}\")\n", "    logger.info(f\"Processing file {i}: {file_name}\")\n", "    \n", "    try:\n", "        # Try pdfplumber first (better table extraction)\n", "        if PDFPLUMBER_AVAILABLE:\n", "            try:\n", "                text, tables = extract_text_pdfplumber(pdf_path)\n", "                extraction_method = \"pdfplumber\"\n", "            except Exception as e:\n", "                logger.warning(f\"pdfplumber failed for {file_name}: {e}\")\n", "                if PYMUPDF_AVAILABLE:\n", "                    text, tables = extract_text_pymupdf(pdf_path)\n", "                    extraction_method = \"pymupdf\"\n", "                else:\n", "                    raise e\n", "        else:\n", "            text, tables = extract_text_pymupdf(pdf_path)\n", "            extraction_method = \"pymupdf\"\n", "        \n", "        # Store results\n", "        extraction_results[file_name] = {\n", "            'file_path': pdf_path,\n", "            'text': text,\n", "            'tables': tables,\n", "            'extraction_method': extraction_method,\n", "            'text_length': len(text),\n", "            'table_count': len(tables),\n", "            'extraction_timestamp': datetime.now().isoformat()\n", "        }\n", "        \n", "        # Update statistics\n", "        extraction_stats['successful_extractions'] += 1\n", "        extraction_stats['total_tables'] += len(tables)\n", "        extraction_stats['total_text_length'] += len(text)\n", "        if tables:\n", "            extraction_stats['files_with_tables'] += 1\n", "        \n", "        print(f\"  Success: {len(text)} chars, {len(tables)} tables ({extraction_method})\")\n", "        \n", "    except Exception as e:\n", "        error_msg = str(e)\n", "        extraction_errors.append((file_name, error_msg))\n", "        extraction_stats['failed_extractions'] += 1\n", "        \n", "        logger.error(f\"Failed to extract from {file_name}: {error_msg}\")\n", "        print(f\"  Error: {error_msg}\")\n", "\n", "logger.info(f\"Extraction completed. Success: {extraction_stats['successful_extractions']}, Failed: {extraction_stats['failed_extractions']}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "PDF TEXT AND TABLE EXTRACTION SUMMARY\n", "============================================================\n", "\n", "Total files processed: 33\n", "Successful extractions: 33\n", "Failed extractions: 0\n", "Success rate: 100.0%\n", "\n", "Total tables extracted: 2375\n", "Files with tables: 33\n", "Total text length: 2,909,423 characters\n", "\n", "============================================================\n"]}], "source": ["# Display extraction summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF TEXT AND TABLE EXTRACTION SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nTotal files processed: {extraction_stats['total_files']}\")\n", "print(f\"Successful extractions: {extraction_stats['successful_extractions']}\")\n", "print(f\"Failed extractions: {extraction_stats['failed_extractions']}\")\n", "print(f\"Success rate: {extraction_stats['successful_extractions']/extraction_stats['total_files']*100:.1f}%\")\n", "\n", "print(f\"\\nTotal tables extracted: {extraction_stats['total_tables']}\")\n", "print(f\"Files with tables: {extraction_stats['files_with_tables']}\")\n", "print(f\"Total text length: {extraction_stats['total_text_length']:,} characters\")\n", "\n", "if extraction_errors:\n", "    print(\"\\nFiles with extraction errors:\")\n", "    for file_name, error_msg in extraction_errors:\n", "        print(f\"  {file_name}: {error_msg}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "TABLE PREVIEW:\n", "----------------------------------------\n", "\n", "File: ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf\n", "\n", "Table 1 (Page_1_Table_1): (2, 2)\n", "Columns: ['KEY\\nSYMBOL COUNT PILE SIZE ToP (FT) MAX REVEAL EMBED\\n176 W6x12X13 4 - 5 60\" 8\\'-0\"\\n1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 7 3 3 3 3 3 3 3 3 3 3 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 8 3 3 3 3 3 3 3 3 3 4 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 2 2 2 2 2 2 2 2 2 5 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 6 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 3 3 3 3 3 3 3 3 3 3 3 3 3 7 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 8 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2 2 2 2 2 2 9 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 2 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 0 1 2 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 5 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 7 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 0 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 2 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 3 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 5 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 6 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 7 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 9 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 8 4 0 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 9 5 1 1 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 0 6 2 1 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 1 7 3 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 5 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 6 2 a N 1 2 3 4 5 6 7 8 9 1 1 P p . . . . . . . . . 0 1 O p W I T M A E I D R P P F A M N E . . L P r N T S A E O A O L R I P I I E L o E R I O I A F L L O E S Y O T E D C L L F P O x U A E E N S C S T R H M A V E D E . H L R J G W W A E N M : U O T A S E U R I I U . T m B P C C N L R H T T U M D S I M A Y a I T L O A N S L T A O A I T L - A R O L S P x O E B A L I S M B X G L D E M T O R V T O E T O E N F D L n I I A Q L E U E I N E T O D O I N P E o O U S I T 3 5 5 5 5 5 5 Y N I o N N U M r E M N R N G E T I S N D B C C t P N T E I 2 D T H E D T C E A h 3 2 O O A E A S I W G A M N E A 1 T ( 1 N / A E L A N T N 3 0 R T F U N M R s U G 6 T T L S 6 A R T 4 O S E A o T D D 4 7 P N X O S E E I B R H U I N u O - 2 C L A ) N O T 9 R P T E A W t I D R S A E U K N R N M N G M L E L h M A T R H L R A I A U A D R A S T N C L I A W A I s B E M M L N T N E C S N A G l K N S F I I B L E G N S 6 T A N G E o E A E R N O I T I S E W W C D E X T N I p S E D S M N T T E W W H A A E O H C T D U e A E 1 A P H . T T I T P G F G D F A C T O N S N B 0 R E A O U A R N 6 6 N E U a R E S M 6 6 E . . N Y S O E C A A D W A R 4 N L n D P x x O A M S S D H F x x S I A N V E S g F N A R D A A E 1 1 N A V I D M W E A H 9 9 O A l T R N T C D D T Y L T O S A R R G P 5 5 Y e A H R A L Y A 6 X X I R U I H I C : F S N R R D O I M B N L * X X T X N P L R D I A M M L D O F 1 1 E O U N 0 T P D A O G 1 L N A B E E A 1 1 D C E R M . H O I R E S A 2 2 W 0 A S A T 2 L E B S C S E A 3 2 N R D E T Y S . F E E E T ° E P R 5 T S P E T O N M O I A R O D I D A E O A N V N I U Z O L S B R A E R L P T X M W Y T U A ( G E Y E S P Y O R R E R A D I P E E O T E S N N R S N T 6 E E E O R Y P R N A D E A S H A P T Y R G X R T R D L A P W E E C L N O 1 L R T G T ’ F 4 4 4 4 I A T A C S P M L R C I 2 I H R O W D T O C V E C R K E B A I O - - - - . S I O N I E L D W I O E W Y O N L B N S E T E N M R 5 5 5 5 T S V O L N 6 R R C M I P R G P A E T S L E I X D O U O W W W E A U D A S I C L E S W W W D 1 A L B R R T H N C W L D N H T E B 5 F E E L 6 6 6 O E A L T H D 6 6 6 O C M N L S E O S S A X X X W D U W P S A E X X X T I W P I U S T A P S N G 1 1 1 L I C R T S R M 9 9 9 I 6 I H Z I U O N R A N L N G 2 5 5 O N . A A U X W E O E V R N I U E G L L . 2 N C N E O E V W I N A I 0 F R T C T A N 6 6 6 6 T A Y E M A R R I H N E T . A C W O Y N O S 0 0 0 0 C ) P A C A L H T E R D W N 8 T N A P \" \" \" \" O C L O A N M X U O N T O L U U I L R I N D U 1 T N R R D I L I S L T B 3 H N A R A T N A E A S 8 8 6 6 7 6 I A A E N I A P L I P T R C T T - W T N N \\' \\' \\' \\' \\' \\' F C D - - - - - - W R S P A I ’ G K A I S O 0 0 6 6 0 6 I T 8 O T . O L S L E M E N I E X \" \" \" \" \" \" T N I Y L N L N R T V E N D 1 H M A D E H . G D I S 5 R D T 7 8 6 6 D E N B E S O F I T E E W \\' \\' \\' \\' O O . T O - - - - I D W D L N 0 0 6 6 8 L R . T * . X \" \" \" \" S 1 . 8\\n*NOTE: THE ONE RED HIGHLIGHTED PILE STANDARD ARRAY\\nPIER (W6X9X12) IS ROUGHLY 1\" ABOVE THE MAX (5\\') PIER\\nHEIGHT DESIGN FOR THIS SITE. THAT ONE PILE WILL NEED\\nTO BE SPOT GRADED TO BE IN THE 5\\' PIER HEIGHT\\nWINDOW.\\nrev date by checked approved description\\nALTHEA 1 & 2\\n¯\\n0 08/19/24 RP FOUNDATION HEIGHT MAP\\nFOUNDATION HEIGHT MAP\\nFIREBAUGH, CA\\nFORUM ENERGY PARTNERS\\nCOORDINATES 0 65 130 260 Feet REV\\n0\\n120.6579173°W 36.8944292°N', None]\n", "Preview:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>KEY\\nSYMBOL COUNT PILE SIZE ToP (FT) MAX REVEAL EMBED\\n176 W6x12X13 4 - 5 60\" 8'-0\"\\n1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 7 3 3 3 3 3 3 3 3 3 3 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 8 3 3 3 3 3 3 3 3 3 4 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 2 2 2 2 2 2 2 2 2 5 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 6 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 3 3 3 3 3 3 3 3 3 3 3 3 3 7 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 8 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2 2 2 2 2 2 9 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 2 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 0 1 2 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 5 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 7 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 0 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 2 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 3 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 5 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 6 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 7 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 9 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 8 4 0 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 9 5 1 1 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 0 6 2 1 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 1 7 3 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 5 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 6 2 a N 1 2 3 4 5 6 7 8 9 1 1 P p . . . . . . . . . 0 1 O p W I T M A E I D R P P F A M N E . . L P r N T S A E O A O L R I P I I E L o E R I O I A F L L O E S Y O T E D C L L F P O x U A E E N S C S T R H M A V E D E . H L R J G W W A E N M : U O T A S E U R I I U . T m B P C C N L R H T T U M D S I M A Y a I T L O A N S L T A O A I T L - A R O L S P x O E B A L I S M B X G L D E M T O R V T O E T O E N F D L n I I A Q L E U E I N E T O D O I N P E o O U S I T 3 5 5 5 5 5 5 Y N I o N N U M r E M N R N G E T I S N D B C C t P N T E I 2 D T H E D T C E A h 3 2 O O A E A S I W G A M N E A 1 T ( 1 N / A E L A N T N 3 0 R T F U N M R s U G 6 T T L S 6 A R T 4 O S E A o T D D 4 7 P N X O S E E I B R H U I N u O - 2 C L A ) N O T 9 R P T E A W t I D R S A E U K N R N M N G M L E L h M A T R H L R A I A U A D R A S T N C L I A W A I s B E M M L N T N E C S N A G l K N S F I I B L E G N S 6 T A N G E o E A E R N O I T I S E W W C D E X T N I p S E D S M N T T E W W H A A E O H C T D U e A E 1 A P H . T T I T P G F G D F A C T O N S N B 0 R E A O U A R N 6 6 N E U a R E S M 6 6 E . . N Y S O E C A A D W A R 4 N L n D P x x O A M S S D H F x x S I A N V E S g F N A R D A A E 1 1 N A V I D M W E A H 9 9 O A l T R N T C D D T Y L T O S A R R G P 5 5 Y e A H R A L Y A 6 X X I R U I H I C : F S N R R D O I M B N L * X X T X N P L R D I A M M L D O F 1 1 E O U N 0 T P D A O G 1 L N A B E E A 1 1 D C E R M . H O I R E S A 2 2 W 0 A S A T 2 L E B S C S E A 3 2 N R D E T Y S . F E E E T ° E P R 5 T S P E T O N M O I A R O D I D A E O A N V N I U Z O L S B R A E R L P T X M W Y T U A ( G E Y E S P Y O R R E R A D I P E E O T E S N N R S N T 6 E E E O R Y P R N A D E A S H A P T Y R G X R T R D L A P W E E C L N O 1 L R T G T ’ F 4 4 4 4 I A T A C S P M L R C I 2 I H R O W D T O C V E C R K E B A I O - - - - . S I O N I E L D W I O E W Y O N L B N S E T E N M R 5 5 5 5 T S V O L N 6 R R C M I P R G P A E T S L E I X D O U O W W W E A U D A S I C L E S W W W D 1 A L B R R T H N C W L D N H T E B 5 F E E L 6 6 6 O E A L T H D 6 6 6 O C M N L S E O S S A X X X W D U W P S A E X X X T I W P I U S T A P S N G 1 1 1 L I C R T S R M 9 9 9 I 6 I H Z I U O N R A N L N G 2 5 5 O N . A A U X W E O E V R N I U E G L L . 2 N C N E O E V W I N A I 0 F R T C T A N 6 6 6 6 T A Y E M A R R I H N E T . A C W O Y N O S 0 0 0 0 C ) P A C A L H T E R D W N 8 T N A P \" \" \" \" O C L O A N M X U O N T O L U U I L R I N D U 1 T N R R D I L I S L T B 3 H N A R A T N A E A S 8 8 6 6 7 6 I A A E N I A P L I P T R C T T - W T N N ' ' ' ' ' ' F C D - - - - - - W R S P A I ’ G K A I S O 0 0 6 6 0 6 I T 8 O T . O L S L E M E N I E X \" \" \" \" \" \" T N I Y L N L N R T V E N D 1 H M A D E H . G D I S 5 R D T 7 8 6 6 D E N B E S O F I T E E W ' ' ' ' O O . T O - - - - I D W D L N 0 0 6 6 8 L R . T * . X \" \" \" \" S 1 . 8\\n*NOTE: THE ONE RED HIGHLIGHTED PILE STANDARD ARRAY\\nPIER (W6X9X12) IS ROUGHLY 1\" ABOVE THE MAX (5') PIER\\nHEIGHT DESIGN FOR THIS SITE. THAT ONE PILE WILL NEED\\nTO BE SPOT GRADED TO BE IN THE 5' PIER HEIGHT\\nWINDOW.\\nrev date by checked approved description\\nALTHEA 1 &amp; 2\\n¯\\n0 08/19/24 RP FOUNDATION HEIGHT MAP\\nFOUNDATION HEIGHT MAP\\nFIREBAUGH, CA\\nFORUM ENERGY PARTNERS\\nCOORDINATES 0 65 130 260 Feet REV\\n0\\n120.6579173°W 36.8944292°N</th>\n", "      <th>None</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>None</td>\n", "      <td>REV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>None</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  KEY\\nSYMBOL COUNT PILE SIZE ToP (FT) MAX REVEAL EMBED\\n176 W6x12X13 4 - 5 60\" 8'-0\"\\n1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 7 3 3 3 3 3 3 3 3 3 3 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 8 3 3 3 3 3 3 3 3 3 4 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 2 2 2 2 2 2 2 2 2 5 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 6 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 3 3 3 3 3 3 3 3 3 3 3 3 3 7 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 8 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2 2 2 2 2 2 9 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 8 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 1 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 2 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 0 1 2 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 5 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 7 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 0 1 2 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 2 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 3 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 5 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 6 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 7 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 8 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 9 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 8 4 0 1 2 4 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 3 3 3 3 9 5 1 1 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 0 6 2 1 2 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 3 3 1 7 3 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 5 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 4 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 6 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 5 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 6 2 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 7 3 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 4 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9 5 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0 6 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 1 7 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 6 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 3 9 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 4 0 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 5 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 6 2 a N 1 2 3 4 5 6 7 8 9 1 1 P p . . . . . . . . . 0 1 O p W I T M A E I D R P P F A M N E . . L P r N T S A E O A O L R I P I I E L o E R I O I A F L L O E S Y O T E D C L L F P O x U A E E N S C S T R H M A V E D E . H L R J G W W A E N M : U O T A S E U R I I U . T m B P C C N L R H T T U M D S I M A Y a I T L O A N S L T A O A I T L - A R O L S P x O E B A L I S M B X G L D E M T O R V T O E T O E N F D L n I I A Q L E U E I N E T O D O I N P E o O U S I T 3 5 5 5 5 5 5 Y N I o N N U M r E M N R N G E T I S N D B C C t P N T E I 2 D T H E D T C E A h 3 2 O O A E A S I W G A M N E A 1 T ( 1 N / A E L A N T N 3 0 R T F U N M R s U G 6 T T L S 6 A R T 4 O S E A o T D D 4 7 P N X O S E E I B R H U I N u O - 2 C L A ) N O T 9 R P T E A W t I D R S A E U K N R N M N G M L E L h M A T R H L R A I A U A D R A S T N C L I A W A I s B E M M L N T N E C S N A G l K N S F I I B L E G N S 6 T A N G E o E A E R N O I T I S E W W C D E X T N I p S E D S M N T T E W W H A A E O H C T D U e A E 1 A P H . T T I T P G F G D F A C T O N S N B 0 R E A O U A R N 6 6 N E U a R E S M 6 6 E . . N Y S O E C A A D W A R 4 N L n D P x x O A M S S D H F x x S I A N V E S g F N A R D A A E 1 1 N A V I D M W E A H 9 9 O A l T R N T C D D T Y L T O S A R R G P 5 5 Y e A H R A L Y A 6 X X I R U I H I C : F S N R R D O I M B N L * X X T X N P L R D I A M M L D O F 1 1 E O U N 0 T P D A O G 1 L N A B E E A 1 1 D C E R M . H O I R E S A 2 2 W 0 A S A T 2 L E B S C S E A 3 2 N R D E T Y S . F E E E T ° E P R 5 T S P E T O N M O I A R O D I D A E O A N V N I U Z O L S B R A E R L P T X M W Y T U A ( G E Y E S P Y O R R E R A D I P E E O T E S N N R S N T 6 E E E O R Y P R N A D E A S H A P T Y R G X R T R D L A P W E E C L N O 1 L R T G T ’ F 4 4 4 4 I A T A C S P M L R C I 2 I H R O W D T O C V E C R K E B A I O - - - - . S I O N I E L D W I O E W Y O N L B N S E T E N M R 5 5 5 5 T S V O L N 6 R R C M I P R G P A E T S L E I X D O U O W W W E A U D A S I C L E S W W W D 1 A L B R R T H N C W L D N H T E B 5 F E E L 6 6 6 O E A L T H D 6 6 6 O C M N L S E O S S A X X X W D U W P S A E X X X T I W P I U S T A P S N G 1 1 1 L I C R T S R M 9 9 9 I 6 I H Z I U O N R A N L N G 2 5 5 O N . A A U X W E O E V R N I U E G L L . 2 N C N E O E V W I N A I 0 F R T C T A N 6 6 6 6 T A Y E M A R R I H N E T . A C W O Y N O S 0 0 0 0 C ) P A C A L H T E R D W N 8 T N A P \" \" \" \" O C L O A N M X U O N T O L U U I L R I N D U 1 T N R R D I L I S L T B 3 H N A R A T N A E A S 8 8 6 6 7 6 I A A E N I A P L I P T R C T T - W T N N ' ' ' ' ' ' F C D - - - - - - W R S P A I ’ G K A I S O 0 0 6 6 0 6 I T 8 O T . O L S L E M E N I E X \" \" \" \" \" \" T N I Y L N L N R T V E N D 1 H M A D E H . G D I S 5 R D T 7 8 6 6 D E N B E S O F I T E E W ' ' ' ' O O . T O - - - - I D W D L N 0 0 6 6 8 L R . T * . X \" \" \" \" S 1 . 8\\n*NOTE: THE ONE RED HIGHLIGHTED PILE STANDARD ARRAY\\nPIER (W6X9X12) IS ROUGHLY 1\" ABOVE THE MAX (5') PIER\\nHEIGHT DESIGN FOR THIS SITE. THAT ONE PILE WILL NEED\\nTO BE SPOT GRADED TO BE IN THE 5' PIER HEIGHT\\nWINDOW.\\nrev date by checked approved description\\nALTHEA 1 & 2\\n¯\\n0 08/19/24 RP FOUNDATION HEIGHT MAP\\nFOUNDATION HEIGHT MAP\\nFIREBAUGH, CA\\nFORUM ENERGY PARTNERS\\nCOORDINATES 0 65 130 260 Feet REV\\n0\\n120.6579173°W 36.8944292°N  \\\n", "0                                               None                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  \n", "1                                               None                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  \n", "\n", "  None  \n", "0  REV  \n", "1    0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Table 2 (Page_1_Table_2): (4, 6)\n", "Columns: ['SYMBOL', 'COUNT', 'PILE SIZE', 'ToP (FT)', 'MAX REVEAL', 'EMBED']\n", "Preview:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SYMBOL</th>\n", "      <th>COUNT</th>\n", "      <th>PILE SIZE</th>\n", "      <th>ToP (FT)</th>\n", "      <th>MAX REVEAL</th>\n", "      <th>EMBED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>176</td>\n", "      <td>W6x12X13</td>\n", "      <td>4 - 5</td>\n", "      <td>60\"</td>\n", "      <td>8'-0\"</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td>2142</td>\n", "      <td>W6x9X12</td>\n", "      <td>4 - 5</td>\n", "      <td>60\"</td>\n", "      <td>6'-6\"</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td>334\\n16</td>\n", "      <td>W6x9X12\\nW6x15X13</td>\n", "      <td>4 - 5\\n4 - 5</td>\n", "      <td>60\"\\n60\"</td>\n", "      <td>7'-0\"\\n8'-0\"</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  SYMBOL    COUNT          PILE SIZE      ToP (FT) MAX REVEAL         EMBED\n", "0             176           W6x12X13         4 - 5        60\"         8'-0\"\n", "1            2142            W6x9X12         4 - 5        60\"         6'-6\"\n", "2         334\\n16  W6x9X12\\nW6x15X13  4 - 5\\n4 - 5   60\"\\n60\"  7'-0\"\\n8'-0\""]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Table 3 (Page_1_Table_3): (3, 4)\n", "Columns: ['<PERSON>NIMU<PERSON> PILE SIZE AND EMBEDMENT\\nMAX ToP (FT) TYPE PILE SIZE EMBEDMENT\\n5 HEAVY ARRAY W6X12 8\\'-0\"', None, None, None]\n", "Preview:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MINIMUM PILE SIZE AND EMBEDMENT\\nMAX ToP (FT) TYPE PILE SIZE EMBEDMENT\\n5 HEAVY ARRAY W6X12 8'-0\"</th>\n", "      <th>None</th>\n", "      <th>None</th>\n", "      <th>None</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5\\n5\\n5</td>\n", "      <td>HEAVY MOTOR\\nSTANDARD ARRAY\\nSTANDARD END</td>\n", "      <td>W6X15\\nW6X9\\nW6X9</td>\n", "      <td>8'-0\"\\n6'-6\"\\n6'-6\"</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>STANDARD EDGE</td>\n", "      <td>W6X9</td>\n", "      <td>7'-0\"</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>STANDARD MOTOR</td>\n", "      <td>W6X15</td>\n", "      <td>6'-6\"</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  MINIMUM PILE SIZE AND EMBEDMENT\\nMAX ToP (FT) TYPE PILE SIZE EMBEDMENT\\n5 HEAVY ARRAY W6X12 8'-0\"  \\\n", "0                                            5\\n5\\n5                                                  \n", "1                                                  5                                                  \n", "2                                                  5                                                  \n", "\n", "                                        None               None  \\\n", "0  HEAVY MOTOR\\nSTANDARD ARRAY\\nSTANDARD END  W6X15\\nW6X9\\nW6X9   \n", "1                              STANDARD EDGE               W6X9   \n", "2                             STANDARD MOTOR              W6X15   \n", "\n", "                  None  \n", "0  8'-0\"\\n6'-6\"\\n6'-6\"  \n", "1                7'-0\"  \n", "2                6'-6\"  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Table 4 (Page_1_Table_4): (6, 6)\n", "Columns: ['rev', 'date', 'by', 'checked', 'approved', 'description']\n", "Preview:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rev</th>\n", "      <th>date</th>\n", "      <th>by</th>\n", "      <th>checked</th>\n", "      <th>approved</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>08/19/24</td>\n", "      <td>RP</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>FOUNDATION HEIGHT MAP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  rev      date  by checked approved            description\n", "0   0  08/19/24  RP                   FOUNDATION HEIGHT MAP\n", "1                                                          \n", "2                                                          "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Table 5 (Page_1_Table_5): (1, 1)\n", "Columns: ['ALTHEA 1 & 2\\nFIREBAUGH, CA']\n", "Preview:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ALTHEA 1 &amp; 2\\nFIREBAUGH, CA</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COORDINATES\\n120.6579173°W 36.8944292°N</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               ALTHEA 1 & 2\\nFIREBAUGH, CA\n", "0  COORDINATES\\n120.6579173°W 36.8944292°N"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "... (showing first 5 tables only)\n"]}], "source": ["# Preview extracted tables\n", "print(\"\\nTABLE PREVIEW:\")\n", "print(\"-\" * 40)\n", "\n", "table_count = 0\n", "for file_name, result in extraction_results.items():\n", "    if result['tables']:\n", "        print(f\"\\nFile: {file_name}\")\n", "        for i, table in enumerate(result['tables']):\n", "            table_count += 1\n", "            print(f\"\\nTable {i+1} ({table.name}): {table.shape}\")\n", "            print(\"Columns:\", list(table.columns))\n", "            \n", "            # Display first few rows\n", "            if not table.empty:\n", "                print(\"Preview:\")\n", "                display(table.head(3))\n", "            else:\n", "                print(\"Table is empty\")\n", "            \n", "            # Limit preview to first 5 tables\n", "            if table_count >= 5:\n", "                print(\"\\n... (showing first 5 tables only)\")\n", "                break\n", "        if table_count >= 5:\n", "            break\n", "\n", "if table_count == 0:\n", "    print(\"No tables were extracted from the PDF files.\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "TEXT EXTRACTION PREVIEW:\n", "----------------------------------------\n", "\n", "File: ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf\n", "Text length: 15,946 characters\n", "Extraction method: pdfplumber\n", "Preview (first 300 chars):\n", "\"--- Page 1 --- KEY SYMBOL COUNT PILE SIZE ToP (FT) MAX REVEAL EMBED 176 W6x12X13 4 - 5 60\" 8'-0\" 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 7 3 3 3 3 3 3 3 3 3 3 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1...\"\n", "\n", "File: Forum_AltheaI&II_CA_NXH_ENG_L3_03-11-2025_Rev 4.pdf\n", "Text length: 52,484 characters\n", "Extraction method: pdfplumber\n", "Preview (first 300 chars):\n", "\"--- Page 1 --- X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X X...\"\n", "\n", "File: GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf\n", "Text length: 16,024 characters\n", "Extraction method: pdfplumber\n", "Preview (first 300 chars):\n", "\"--- <PERSON> 1 --- 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 INQUADRAMENTO SU ORTOFOTO A SCALA 1:2000 A N NO NL O L C.CONS. SO SL B C.UTENTE S B 4 C C 1 TC 2 TC 1 2 D D LEGENDA SCS STRADE ESISTENTI DA MANTENERE E E SCS STRADE ESISTENTI DA RIMUOVERE VIABILIT INTERNA IMPIANTO (4m) RECINZIONE IMPIANTO CANCELL...\"\n", "\n", "... (showing first 3 files only)\n"]}], "source": ["# Preview extracted text (sample)\n", "print(\"\\nTEXT EXTRACTION PREVIEW:\")\n", "print(\"-\" * 40)\n", "\n", "text_previews = 0\n", "for file_name, result in extraction_results.items():\n", "    if result['text'] and len(result['text'].strip()) > 50:\n", "        text_previews += 1\n", "        print(f\"\\nFile: {file_name}\")\n", "        print(f\"Text length: {result['text_length']:,} characters\")\n", "        print(f\"Extraction method: {result['extraction_method']}\")\n", "        \n", "        # Show first 300 characters\n", "        preview_text = result['text'][:300].strip()\n", "        print(f\"Preview (first 300 chars):\")\n", "        print(f\"\\\"{preview_text}...\\\"\")\n", "        \n", "        # Limit to first 3 files\n", "        if text_previews >= 3:\n", "            print(\"\\n... (showing first 3 files only)\")\n", "            break\n", "\n", "if text_previews == 0:\n", "    print(\"No substantial text content was extracted from the PDF files.\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:20:05,347 - INFO - PDF extraction results exported successfully\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "EXPORTING EXTRACTION RESULTS...\n", "Results exported to variables:\n", "  - pdf_extraction_results: 33 file results\n", "  - pdf_extraction_stats: Summary statistics\n", "  - pdf_extraction_summary: DataFrame with 33 rows\n"]}], "source": ["# Export results for downstream processing\n", "print(\"\\nEXPORTING EXTRACTION RESULTS...\")\n", "\n", "# Store results in global variables for use by subsequent notebooks\n", "globals()['pdf_extraction_results'] = extraction_results\n", "globals()['pdf_extraction_stats'] = extraction_stats\n", "globals()['pdf_extraction_errors'] = extraction_errors\n", "\n", "# Create summary DataFrame for easy analysis\n", "summary_data = []\n", "for file_name, result in extraction_results.items():\n", "    summary_data.append({\n", "        'file_name': file_name,\n", "        'file_path': result['file_path'],\n", "        'text_length': result['text_length'],\n", "        'table_count': result['table_count'],\n", "        'extraction_method': result['extraction_method'],\n", "        'extraction_timestamp': result['extraction_timestamp']\n", "    })\n", "\n", "extraction_summary_df = pd.DataFrame(summary_data)\n", "globals()['pdf_extraction_summary'] = extraction_summary_df\n", "\n", "print(f\"Results exported to variables:\")\n", "print(f\"  - pdf_extraction_results: {len(extraction_results)} file results\")\n", "print(f\"  - pdf_extraction_stats: Summary statistics\")\n", "print(f\"  - pdf_extraction_summary: DataFrame with {len(extraction_summary_df)} rows\")\n", "\n", "logger.info(\"PDF extraction results exported successfully\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:20:05,354 - INFO - PDF text and table extraction notebook execution completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "PDF TEXT AND TABLE EXTRACTION COMPLETED\n", "============================================================\n", "Successfully processed 33 PDF files\n", "Extracted 2375 tables and 2,909,423 characters of text\n", "Ready for next stage of CAD PDF processing pipeline\n", "\n", "Completion time: 2025-06-17 13:20:05\n"]}], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF TEXT AND TABLE EXTRACTION COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if extraction_stats['successful_extractions'] > 0:\n", "    print(f\"Successfully processed {extraction_stats['successful_extractions']} PDF files\")\n", "    print(f\"Extracted {extraction_stats['total_tables']} tables and {extraction_stats['total_text_length']:,} characters of text\")\n", "    print(\"Ready for next stage of CAD PDF processing pipeline\")\n", "else:\n", "    print(\"No PDF files were successfully processed\")\n", "    print(\"Please check file accessibility and library installations\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"PDF text and table extraction notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}