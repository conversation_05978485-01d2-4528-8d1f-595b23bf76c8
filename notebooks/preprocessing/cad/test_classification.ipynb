{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PDF Classification Test\n", "\n", "This notebook tests the PDF classification functionality with sample filenames to verify the classification logic works correctly for the foundation analysis use case."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PDF Classification Function (same as in discovery notebook)\n", "def classify_pdf(path):\n", "    \"\"\"Classify PDF files based on filename patterns for foundation analysis use case.\"\"\"\n", "    path_lower = str(path).lower()\n", "    \n", "    # Foundation and structural analysis patterns\n", "    if \"tolerance\" in path_lower or \"pier\" in path_lower:\n", "        return \"foundation_tolerance\"\n", "    \n", "    # Layout and planning patterns\n", "    if \"plan\" in path_lower and \"powerblock\" in path_lower:\n", "        return \"layout_plan\"\n", "    \n", "    # Environmental and site analysis patterns\n", "    if \"flood\" in path_lower:\n", "        return \"flood_map\"\n", "    \n", "    # Electrical system patterns\n", "    if \"sld\" in path_lower or \"schema elettrico\" in path_lower or \"electrical\" in path_lower:\n", "        return \"electrical\"\n", "    \n", "    # Elevation and detail patterns\n", "    if \"alzamento\" in path_lower or \"elevation\" in path_lower:\n", "        return \"elevation_detail\"\n", "    \n", "    # Additional foundation-specific patterns\n", "    if \"foundation\" in path_lower or \"footing\" in path_lower:\n", "        return \"foundation_detail\"\n", "    \n", "    # Layout and general planning\n", "    if \"layout\" in path_lower or \"general\" in path_lower:\n", "        return \"general_layout\"\n", "    \n", "    # Site and earthworks\n", "    if \"earthwork\" in path_lower or \"civil\" in path_lower or \"site\" in path_lower:\n", "        return \"site_civil\"\n", "    \n", "    # Tracker and mounting systems\n", "    if \"tracker\" in path_lower or \"plinto\" in path_lower:\n", "        return \"tracker_mounting\"\n", "    \n", "    # Default classification\n", "    return \"other\"\n", "\n", "print(\"Classification function defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test cases based on actual filenames from your project\n", "test_files = [\n", "    \"ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf\",\n", "    \"GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf\",\n", "    \"GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.pdf\",\n", "    \"GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks.pdf\",\n", "    \"GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf\",\n", "    \"Plinto Area - Alzamento a 700mm.pdf\",\n", "    \"Plinto - Alzamento a 700mm.pdf\",\n", "    \"Foundation_Tolerance_Analysis.pdf\",\n", "    \"Pier_Foundation_Details.pdf\",\n", "    \"Powerblock_Layout_Plan.pdf\",\n", "    \"Site_Flood_Map.pdf\",\n", "    \"Electrical_Schema_Elettrico.pdf\",\n", "    \"General_Site_Layout.pdf\",\n", "    \"Tracker_Mounting_System.pdf\",\n", "    \"Random_Document.pdf\"\n", "]\n", "\n", "print(\"Testing PDF classification on sample filenames:\")\n", "print(\"=\" * 60)\n", "\n", "classification_results = {}\n", "for filename in test_files:\n", "    classification = classify_pdf(filename)\n", "    classification_results[filename] = classification\n", "    print(f\"{filename:<50} → {classification}\")\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary of classifications\n", "from collections import Counter\n", "\n", "classification_counts = Counter(classification_results.values())\n", "\n", "print(\"\\nClassification Summary:\")\n", "print(\"-\" * 30)\n", "for classification, count in sorted(classification_counts.items()):\n", "    print(f\"{classification:<20}: {count} files\")\n", "\n", "print(f\"\\nTotal files classified: {len(test_files)}\")\n", "print(f\"Unique classifications: {len(classification_counts)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify specific patterns work correctly\n", "print(\"\\nPattern Verification:\")\n", "print(\"-\" * 30)\n", "\n", "# Test foundation patterns\n", "foundation_files = [f for f, c in classification_results.items() if 'foundation' in c]\n", "print(f\"Foundation-related files: {len(foundation_files)}\")\n", "for f in foundation_files:\n", "    print(f\"  {f} → {classification_results[f]}\")\n", "\n", "# Test electrical patterns\n", "electrical_files = [f for f, c in classification_results.items() if c == 'electrical']\n", "print(f\"\\nElectrical files: {len(electrical_files)}\")\n", "for f in electrical_files:\n", "    print(f\"  {f} → {classification_results[f]}\")\n", "\n", "# Test elevation patterns\n", "elevation_files = [f for f, c in classification_results.items() if c == 'elevation_detail']\n", "print(f\"\\nElevation detail files: {len(elevation_files)}\")\n", "for f in elevation_files:\n", "    print(f\"  {f} → {classification_results[f]}\")\n", "\n", "# Test layout patterns\n", "layout_files = [f for f, c in classification_results.items() if 'layout' in c]\n", "print(f\"\\nLayout-related files: {len(layout_files)}\")\n", "for f in layout_files:\n", "    print(f\"  {f} → {classification_results[f]}\")\n", "\n", "print(\"\\nClassification test completed successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}