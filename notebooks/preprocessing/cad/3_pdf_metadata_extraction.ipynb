import logging
import re
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Optional, Any

import pandas as pd
import numpy as np

# PDF processing libraries
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
    print("pdfplumber library loaded successfully")
except ImportError:
    PDFPLUMBER_AVAILABLE = False
    print("Warning: pdfplumber not available")

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
    print("PyMuPDF library loaded successfully")
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("Warning: PyMuPDF not available")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

print("CAD PDF Metadata Extraction - Starting...")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Check for required libraries
if not PDFPLUMBER_AVAILABLE and not PYMUPDF_AVAILABLE:
    raise ImportError("Neither pdfplumber nor PyMuPDF is available. Please install at least one: pip install pdfplumber pymupdf")

# Load discovered PDF files from previous notebooks
try:
    # Try to use results from previous notebooks
    pdf_files = globals().get('discovered_pdf_files', [])
    extraction_results = globals().get('pdf_extraction_results', {})
    
    if not pdf_files:
        # Fallback: load from data directory
        project_root = Path('../../../')
        cad_data_path = project_root / 'data' / 'cad'
        pdf_files = [str(p) for p in cad_data_path.rglob('*.pdf')]
        logger.info(f"Loaded {len(pdf_files)} PDF files from directory scan")
    else:
        logger.info(f"Using {len(pdf_files)} PDF files from previous notebooks")
        
except Exception as e:
    logger.error(f"Error loading PDF file list: {e}")
    pdf_files = []
    extraction_results = {}

print(f"Processing {len(pdf_files)} PDF files for metadata extraction")
print(f"Text extraction results available for {len(extraction_results)} files")

# Metadata extraction utilities
def extract_pdf_metadata_pdfplumber(pdf_path: str) -> Dict[str, Any]:
    """Extract metadata using pdfplumber."""
    metadata = {}
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            # Basic document metadata
            metadata.update({
                'page_count': len(pdf.pages),
                'pdf_metadata': pdf.metadata or {},
                'extraction_method': 'pdfplumber'
            })
            
            # Page-level information
            if pdf.pages:
                first_page = pdf.pages[0]
                metadata.update({
                    'page_width': first_page.width,
                    'page_height': first_page.height,
                    'page_rotation': getattr(first_page, 'rotation', 0)
                })
                
    except Exception as e:
        logger.error(f"Error extracting metadata with pdfplumber from {pdf_path}: {e}")
        raise
    
    return metadata

def extract_pdf_metadata_pymupdf(pdf_path: str) -> Dict[str, Any]:
    """Extract metadata using PyMuPDF."""
    metadata = {}
    
    try:
        doc = fitz.open(pdf_path)
        
        # Basic document metadata
        metadata.update({
            'page_count': len(doc),
            'pdf_metadata': doc.metadata,
            'extraction_method': 'pymupdf'
        })
        
        # Page-level information
        if len(doc) > 0:
            first_page = doc.load_page(0)
            rect = first_page.rect
            metadata.update({
                'page_width': rect.width,
                'page_height': rect.height,
                'page_rotation': first_page.rotation
            })
        
        doc.close()
        
    except Exception as e:
        logger.error(f"Error extracting metadata with PyMuPDF from {pdf_path}: {e}")
        raise
    
    return metadata

print("Metadata extraction utilities defined")

# CAD-specific metadata extraction patterns
CAD_PATTERNS = {
    'drawing_number': [
        r'(?i)drawing\s*(?:no\.?|number)\s*:?\s*([A-Z0-9\-\.]+)',
        r'(?i)dwg\s*(?:no\.?|#)\s*:?\s*([A-Z0-9\-\.]+)',
        r'([A-Z]{2,}\.[A-Z]{2,}\.[A-Z]\.[0-9]{2}\.[A-Z]{2}\.[A-Z]\.[0-9]{5}\.[0-9]{2}\.[0-9]{3}\.[0-9]{2})',  # ENEL pattern
        r'([A-Z]\-[0-9]{3})',  # Simple pattern like S-203
    ],
    'revision': [
        r'(?i)rev\.?\s*:?\s*([A-Z0-9]+)',
        r'(?i)revision\s*:?\s*([A-Z0-9]+)',
    ],
    'scale': [
        r'(?i)scale\s*:?\s*(1:[0-9,]+)',
        r'(?i)scale\s*:?\s*([0-9]+\s*=\s*[0-9]+)',
    ],
    'date': [
        r'(?i)date\s*:?\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
        r'([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
    ],
    'project_name': [
        r'(?i)project\s*:?\s*([A-Za-z0-9\s\-\.]+?)(?:\n|$)',
    ],
    'sheet_title': [
        r'(?i)(?:sheet\s*title|title)\s*:?\s*([A-Za-z0-9\s\-\.]+?)(?:\n|$)',
    ]
}

def extract_cad_metadata_from_text(text: str) -> Dict[str, Any]:
    """Extract CAD-specific metadata from text content."""
    cad_metadata = {}
    
    if not text:
        return cad_metadata
    
    # Apply each pattern category
    for category, patterns in CAD_PATTERNS.items():
        matches = []
        for pattern in patterns:
            found = re.findall(pattern, text)
            matches.extend(found)
        
        if matches:
            # Take the first match or most common if multiple
            if len(matches) == 1:
                cad_metadata[category] = matches[0].strip()
            else:
                # Use most common match
                from collections import Counter
                most_common = Counter(matches).most_common(1)
                cad_metadata[category] = most_common[0][0].strip() if most_common else matches[0].strip()
                cad_metadata[f'{category}_alternatives'] = list(set(matches))
    
    return cad_metadata

print("CAD-specific metadata extraction patterns defined")

# Initialize containers for metadata results
metadata_results = {}
metadata_errors = []
metadata_stats = {
    'total_files': len(pdf_files),
    'successful_extractions': 0,
    'failed_extractions': 0,
    'files_with_cad_metadata': 0,
    'files_with_drawing_numbers': 0,
    'files_with_revisions': 0
}

print("Starting metadata extraction...")
logger.info(f"Beginning metadata extraction from {len(pdf_files)} PDF files")

# Process each PDF file
for i, pdf_path in enumerate(pdf_files, 1):
    pdf_path_obj = Path(pdf_path)
    file_name = pdf_path_obj.name
    
    print(f"\nProcessing {i}/{len(pdf_files)}: {file_name}")
    logger.info(f"Processing metadata for file {i}: {file_name}")
    
    try:
        # Extract basic PDF metadata
        if PDFPLUMBER_AVAILABLE:
            try:
                pdf_metadata = extract_pdf_metadata_pdfplumber(pdf_path)
                extraction_method = "pdfplumber"
            except Exception as e:
                logger.warning(f"pdfplumber failed for {file_name}: {e}")
                if PYMUPDF_AVAILABLE:
                    pdf_metadata = extract_pdf_metadata_pymupdf(pdf_path)
                    extraction_method = "pymupdf"
                else:
                    raise e
        else:
            pdf_metadata = extract_pdf_metadata_pymupdf(pdf_path)
            extraction_method = "pymupdf"
        
        # Extract CAD-specific metadata from text if available
        cad_metadata = {}
        if file_name in extraction_results and extraction_results[file_name].get('text'):
            text_content = extraction_results[file_name]['text']
            cad_metadata = extract_cad_metadata_from_text(text_content)
        
        # Combine all metadata
        combined_metadata = {
            'file_path': pdf_path,
            'file_name': file_name,
            'file_size_bytes': pdf_path_obj.stat().st_size,
            'extraction_timestamp': datetime.now().isoformat(),
            'extraction_method': extraction_method,
            **pdf_metadata,
            'cad_metadata': cad_metadata
        }
        
        # Store results
        metadata_results[file_name] = combined_metadata
        
        # Update statistics
        metadata_stats['successful_extractions'] += 1
        if cad_metadata:
            metadata_stats['files_with_cad_metadata'] += 1
        if cad_metadata.get('drawing_number'):
            metadata_stats['files_with_drawing_numbers'] += 1
        if cad_metadata.get('revision'):
            metadata_stats['files_with_revisions'] += 1
        
        print(f"  Success: {len(cad_metadata)} CAD fields extracted ({extraction_method})")
        
    except Exception as e:
        error_msg = str(e)
        metadata_errors.append((file_name, error_msg))
        metadata_stats['failed_extractions'] += 1
        
        logger.error(f"Failed to extract metadata from {file_name}: {error_msg}")
        print(f"  Error: {error_msg}")

logger.info(f"Metadata extraction completed. Success: {metadata_stats['successful_extractions']}, Failed: {metadata_stats['failed_extractions']}")

# Display metadata extraction summary
print("\n" + "="*60)
print("PDF METADATA EXTRACTION SUMMARY")
print("="*60)

print(f"\nTotal files processed: {metadata_stats['total_files']}")
print(f"Successful extractions: {metadata_stats['successful_extractions']}")
print(f"Failed extractions: {metadata_stats['failed_extractions']}")
print(f"Success rate: {metadata_stats['successful_extractions']/metadata_stats['total_files']*100:.1f}%")

print(f"\nCAD-specific metadata:")
print(f"Files with CAD metadata: {metadata_stats['files_with_cad_metadata']}")
print(f"Files with drawing numbers: {metadata_stats['files_with_drawing_numbers']}")
print(f"Files with revision info: {metadata_stats['files_with_revisions']}")

if metadata_errors:
    print("\nFiles with extraction errors:")
    for file_name, error_msg in metadata_errors:
        print(f"  {file_name}: {error_msg}")

print("\n" + "="*60)

# Create structured metadata DataFrame
print("\nCREATING STRUCTURED METADATA DATAFRAME...")

metadata_records = []
for file_name, metadata in metadata_results.items():
    # Flatten the metadata structure
    record = {
        'file_name': file_name,
        'file_path': metadata['file_path'],
        'file_size_mb': metadata['file_size_bytes'] / (1024 * 1024),
        'page_count': metadata.get('page_count', 0),
        'page_width': metadata.get('page_width', 0),
        'page_height': metadata.get('page_height', 0),
        'page_rotation': metadata.get('page_rotation', 0),
        'extraction_method': metadata['extraction_method'],
        'extraction_timestamp': metadata['extraction_timestamp']
    }
    
    # Add PDF metadata fields
    pdf_meta = metadata.get('pdf_metadata', {})
    if pdf_meta:
        record.update({
            'pdf_title': pdf_meta.get('title', ''),
            'pdf_author': pdf_meta.get('author', ''),
            'pdf_subject': pdf_meta.get('subject', ''),
            'pdf_creator': pdf_meta.get('creator', ''),
            'pdf_producer': pdf_meta.get('producer', ''),
            'pdf_creation_date': pdf_meta.get('creationDate', ''),
            'pdf_modification_date': pdf_meta.get('modDate', '')
        })
    
    # Add CAD-specific metadata
    cad_meta = metadata.get('cad_metadata', {})
    record.update({
        'drawing_number': cad_meta.get('drawing_number', ''),
        'revision': cad_meta.get('revision', ''),
        'scale': cad_meta.get('scale', ''),
        'cad_date': cad_meta.get('date', ''),
        'project_name': cad_meta.get('project_name', ''),
        'sheet_title': cad_meta.get('sheet_title', '')
    })
    
    metadata_records.append(record)

# Create DataFrame
metadata_df = pd.DataFrame(metadata_records)

print(f"Created metadata DataFrame with {len(metadata_df)} rows and {len(metadata_df.columns)} columns")
print(f"Columns: {list(metadata_df.columns)}")

# Display metadata preview
print("\nMETADATA PREVIEW:")
print("-" * 40)

if not metadata_df.empty:
    # Show basic statistics
    print(f"\nDataFrame shape: {metadata_df.shape}")
    print(f"\nFile size statistics (MB):")
    print(metadata_df['file_size_mb'].describe())
    
    print(f"\nPage count statistics:")
    print(metadata_df['page_count'].describe())
    
    # Show files with drawing numbers
    files_with_drawings = metadata_df[metadata_df['drawing_number'] != '']
    if not files_with_drawings.empty:
        print(f"\nFiles with drawing numbers ({len(files_with_drawings)}):")
        for _, row in files_with_drawings.head(5).iterrows():
            print(f"  {row['file_name']}: {row['drawing_number']}")
        if len(files_with_drawings) > 5:
            print(f"  ... and {len(files_with_drawings) - 5} more")
    
    # Show sample of the DataFrame
    print(f"\nSample metadata records:")
    display(metadata_df.head(3))
    
else:
    print("No metadata records were created.")

# Export results for downstream processing
print("\nEXPORTING METADATA RESULTS...")

# Store results in global variables for use by subsequent notebooks
globals()['pdf_metadata_results'] = metadata_results
globals()['pdf_metadata_stats'] = metadata_stats
globals()['pdf_metadata_errors'] = metadata_errors
globals()['pdf_metadata_dataframe'] = metadata_df

# Save to CSV for external use
output_dir = Path('../../../data/cad')
csv_path = output_dir / 'cad_pdf_metadata.csv'
try:
    metadata_df.to_csv(csv_path, index=False)
    print(f"Metadata DataFrame saved to: {csv_path}")
except Exception as e:
    logger.warning(f"Could not save CSV file: {e}")

# Save detailed metadata as JSON
json_path = output_dir / 'cad_pdf_metadata_detailed.json'
try:
    with open(json_path, 'w') as f:
        json.dump(metadata_results, f, indent=2, default=str)
    print(f"Detailed metadata saved to: {json_path}")
except Exception as e:
    logger.warning(f"Could not save JSON file: {e}")

print(f"\nResults exported to variables:")
print(f"  - pdf_metadata_results: {len(metadata_results)} file results")
print(f"  - pdf_metadata_dataframe: DataFrame with {len(metadata_df)} rows")
print(f"  - pdf_metadata_stats: Summary statistics")

logger.info("PDF metadata extraction results exported successfully")

# Final status report
print("\n" + "="*60)
print("PDF METADATA EXTRACTION COMPLETED")
print("="*60)

if metadata_stats['successful_extractions'] > 0:
    print(f"Successfully processed {metadata_stats['successful_extractions']} PDF files")
    print(f"Extracted metadata for {len(metadata_df)} files")
    print(f"Found {metadata_stats['files_with_drawing_numbers']} files with drawing numbers")
    print(f"Found {metadata_stats['files_with_revisions']} files with revision information")
    print("Ready for next stage of CAD PDF processing pipeline")
else:
    print("No PDF files were successfully processed")
    print("Please check file accessibility and library installations")

print(f"\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
logger.info("PDF metadata extraction notebook execution completed")