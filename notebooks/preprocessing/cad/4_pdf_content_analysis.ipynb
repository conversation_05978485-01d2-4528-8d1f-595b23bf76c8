import logging
import re
import json
from pathlib import Path
from datetime import datetime
from collections import Counter, defaultdict
from typing import Dict, List, Optional, Any, Tuple

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Text processing libraries
try:
    from wordcloud import WordCloud
    WORDCLOUD_AVAILABLE = True
    print("WordCloud library loaded successfully")
except ImportError:
    WORDCLOUD_AVAILABLE = False
    print("Warning: WordCloud not available")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("CAD PDF Content Analysis and Summarization - Starting...")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Load data from previous pipeline stages
print("Loading data from previous pipeline stages...")

# Initialize data containers
pdf_files = globals().get('discovered_pdf_files', [])
extraction_results = globals().get('pdf_extraction_results', {})
metadata_results = globals().get('pdf_metadata_results', {})
metadata_df = globals().get('pdf_metadata_dataframe', pd.DataFrame())

# Fallback: load from files if variables not available
if not pdf_files or not extraction_results:
    project_root = Path('../../../')
    cad_data_path = project_root / 'data' / 'cad'
    
    # Load PDF files
    if not pdf_files:
        pdf_files = [str(p) for p in cad_data_path.rglob('*.pdf')]
        logger.info(f"Loaded {len(pdf_files)} PDF files from directory scan")
    
    # Try to load metadata from CSV if available
    if metadata_df.empty:
        csv_path = cad_data_path / 'cad_pdf_metadata.csv'
        if csv_path.exists():
            metadata_df = pd.read_csv(csv_path)
            logger.info(f"Loaded metadata from CSV: {len(metadata_df)} records")

print(f"Data loaded:")
print(f"  - PDF files: {len(pdf_files)}")
print(f"  - Extraction results: {len(extraction_results)}")
print(f"  - Metadata results: {len(metadata_results)}")
print(f"  - Metadata DataFrame: {len(metadata_df)} rows")

# Content analysis utilities
def extract_keywords(text: str, min_length: int = 3, top_n: int = 20) -> List[Tuple[str, int]]:
    """Extract keywords from text content."""
    if not text:
        return []
    
    # Clean and tokenize text
    text = re.sub(r'[^a-zA-Z\s]', ' ', text.lower())
    words = text.split()
    
    # Filter words by length and common stop words
    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'shall', 'this', 'that', 'these', 'those', 'a', 'an'}
    filtered_words = [word for word in words if len(word) >= min_length and word not in stop_words]
    
    # Count word frequencies
    word_counts = Counter(filtered_words)
    
    return word_counts.most_common(top_n)

def categorize_document(text: str, metadata: Dict[str, Any]) -> str:
    """Categorize document based on content and metadata."""
    text_lower = text.lower() if text else ''
    file_name = metadata.get('file_name', '').lower()
    
    # Define category patterns
    categories = {
        'foundation_plan': ['foundation', 'pile', 'pier', 'footing', 'concrete'],
        'electrical_plan': ['electrical', 'cable', 'conduit', 'power', 'voltage', 'circuit'],
        'site_plan': ['site', 'layout', 'general', 'overall', 'location'],
        'structural_plan': ['structural', 'beam', 'column', 'steel', 'frame'],
        'detail_drawing': ['detail', 'section', 'elevation', 'cross'],
        'specification': ['specification', 'spec', 'requirement', 'standard']
    }
    
    # Score each category
    category_scores = {}
    for category, keywords in categories.items():
        score = 0
        for keyword in keywords:
            score += text_lower.count(keyword) + file_name.count(keyword) * 2
        category_scores[category] = score
    
    # Return category with highest score, or 'unknown' if no matches
    if max(category_scores.values()) > 0:
        return max(category_scores, key=category_scores.get)
    else:
        return 'unknown'

def extract_measurements(text: str) -> List[str]:
    """Extract measurement values from text."""
    if not text:
        return []
    
    # Patterns for common measurements
    measurement_patterns = [
        r'\d+\.?\d*\s*(?:mm|cm|m|ft|in|inch|inches)',
        r'\d+\.?\d*\s*x\s*\d+\.?\d*',  # dimensions
        r'\d+\.?\d*\s*°',  # angles
        r'\d+\.?\d*\s*%',  # percentages
    ]
    
    measurements = []
    for pattern in measurement_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        measurements.extend(matches)
    
    return list(set(measurements))  # Remove duplicates

print("Content analysis utilities defined")

# Perform content analysis
print("\nPerforming content analysis...")

analysis_results = {}
all_keywords = Counter()
document_categories = Counter()
all_measurements = []

for file_name, extraction_data in extraction_results.items():
    print(f"Analyzing: {file_name}")
    
    text_content = extraction_data.get('text', '')
    tables = extraction_data.get('tables', [])
    metadata = metadata_results.get(file_name, {})
    
    # Extract keywords
    keywords = extract_keywords(text_content)
    all_keywords.update(dict(keywords))
    
    # Categorize document
    category = categorize_document(text_content, metadata)
    document_categories[category] += 1
    
    # Extract measurements
    measurements = extract_measurements(text_content)
    all_measurements.extend(measurements)
    
    # Analyze tables
    table_analysis = []
    for i, table in enumerate(tables):
        if not table.empty:
            table_info = {
                'table_index': i,
                'shape': table.shape,
                'columns': list(table.columns),
                'numeric_columns': list(table.select_dtypes(include=[np.number]).columns),
                'has_numeric_data': len(table.select_dtypes(include=[np.number]).columns) > 0
            }
            table_analysis.append(table_info)
    
    # Store analysis results
    analysis_results[file_name] = {
        'keywords': keywords,
        'category': category,
        'measurements': measurements,
        'table_analysis': table_analysis,
        'text_length': len(text_content),
        'table_count': len(tables),
        'has_numeric_tables': any(t['has_numeric_data'] for t in table_analysis)
    }

print(f"\nContent analysis completed for {len(analysis_results)} files")
logger.info(f"Content analysis completed for {len(analysis_results)} files")

# Generate analysis summary and visualizations
print("\n" + "="*60)
print("CAD PDF CONTENT ANALYSIS SUMMARY")
print("="*60)

print(f"\nDocument Categories:")
for category, count in document_categories.most_common():
    print(f"  {category}: {count} documents")

print(f"\nTop Keywords (across all documents):")
for keyword, count in all_keywords.most_common(15):
    print(f"  {keyword}: {count} occurrences")

print(f"\nMeasurement Analysis:")
print(f"  Total measurements found: {len(all_measurements)}")
print(f"  Unique measurements: {len(set(all_measurements))}")
if all_measurements:
    measurement_counter = Counter(all_measurements)
    print(f"  Most common measurements:")
    for measurement, count in measurement_counter.most_common(10):
        print(f"    {measurement}: {count} times")

print("\n" + "="*60)

# Create visualizations
print("\nCreating analysis visualizations...")

# Set up the plotting area
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('CAD PDF Content Analysis Dashboard', fontsize=16, fontweight='bold')

# 1. Document categories pie chart
if document_categories:
    categories, counts = zip(*document_categories.most_common())
    axes[0, 0].pie(counts, labels=categories, autopct='%1.1f%%', startangle=90)
    axes[0, 0].set_title('Document Categories Distribution')
else:
    axes[0, 0].text(0.5, 0.5, 'No category data', ha='center', va='center')
    axes[0, 0].set_title('Document Categories Distribution')

# 2. Top keywords bar chart
if all_keywords:
    top_keywords = all_keywords.most_common(10)
    keywords, keyword_counts = zip(*top_keywords)
    axes[0, 1].barh(range(len(keywords)), keyword_counts)
    axes[0, 1].set_yticks(range(len(keywords)))
    axes[0, 1].set_yticklabels(keywords)
    axes[0, 1].set_xlabel('Frequency')
    axes[0, 1].set_title('Top 10 Keywords')
    axes[0, 1].invert_yaxis()
else:
    axes[0, 1].text(0.5, 0.5, 'No keyword data', ha='center', va='center')
    axes[0, 1].set_title('Top 10 Keywords')

# 3. File size vs text length scatter plot
if not metadata_df.empty and analysis_results:
    file_sizes = []
    text_lengths = []
    
    for file_name in analysis_results.keys():
        if file_name in metadata_df['file_name'].values:
            file_size = metadata_df[metadata_df['file_name'] == file_name]['file_size_mb'].iloc[0]
            text_length = analysis_results[file_name]['text_length']
            file_sizes.append(file_size)
            text_lengths.append(text_length)
    
    if file_sizes and text_lengths:
        axes[1, 0].scatter(file_sizes, text_lengths, alpha=0.6)
        axes[1, 0].set_xlabel('File Size (MB)')
        axes[1, 0].set_ylabel('Text Length (characters)')
        axes[1, 0].set_title('File Size vs Text Content')
    else:
        axes[1, 0].text(0.5, 0.5, 'No size/text data', ha='center', va='center')
        axes[1, 0].set_title('File Size vs Text Content')
else:
    axes[1, 0].text(0.5, 0.5, 'No metadata available', ha='center', va='center')
    axes[1, 0].set_title('File Size vs Text Content')

# 4. Table analysis
files_with_tables = sum(1 for result in analysis_results.values() if result['table_count'] > 0)
files_with_numeric_tables = sum(1 for result in analysis_results.values() if result['has_numeric_tables'])
files_without_tables = len(analysis_results) - files_with_tables

table_data = ['Files with Tables', 'Files with Numeric Tables', 'Files without Tables']
table_counts = [files_with_tables, files_with_numeric_tables, files_without_tables]

axes[1, 1].bar(table_data, table_counts, color=['skyblue', 'lightgreen', 'lightcoral'])
axes[1, 1].set_ylabel('Number of Files')
axes[1, 1].set_title('Table Content Analysis')
axes[1, 1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

print("Visualizations created successfully")

# Generate word cloud if available
if WORDCLOUD_AVAILABLE and all_keywords:
    print("\nGenerating word cloud...")
    
    # Create word cloud from keywords
    wordcloud = WordCloud(
        width=800, 
        height=400, 
        background_color='white',
        max_words=50,
        colormap='viridis'
    ).generate_from_frequencies(all_keywords)
    
    plt.figure(figsize=(12, 6))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.title('CAD PDF Content Word Cloud', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.show()
    
    print("Word cloud generated successfully")
else:
    print("\nWord cloud generation skipped (WordCloud library not available or no keywords)")

# Create comprehensive analysis DataFrame
print("\nCreating comprehensive analysis DataFrame...")

analysis_records = []
for file_name, analysis_data in analysis_results.items():
    # Get metadata if available
    metadata = metadata_results.get(file_name, {})
    
    record = {
        'file_name': file_name,
        'category': analysis_data['category'],
        'text_length': analysis_data['text_length'],
        'table_count': analysis_data['table_count'],
        'has_numeric_tables': analysis_data['has_numeric_tables'],
        'measurement_count': len(analysis_data['measurements']),
        'keyword_count': len(analysis_data['keywords']),
        'top_keywords': ', '.join([kw[0] for kw in analysis_data['keywords'][:5]]),
        'measurements_sample': ', '.join(analysis_data['measurements'][:5]),
        'drawing_number': metadata.get('cad_metadata', {}).get('drawing_number', ''),
        'revision': metadata.get('cad_metadata', {}).get('revision', ''),
        'page_count': metadata.get('page_count', 0),
        'file_size_mb': metadata.get('file_size_bytes', 0) / (1024 * 1024) if metadata.get('file_size_bytes') else 0
    }
    
    analysis_records.append(record)

analysis_df = pd.DataFrame(analysis_records)

print(f"Created analysis DataFrame with {len(analysis_df)} rows and {len(analysis_df.columns)} columns")

# Display sample of analysis results
if not analysis_df.empty:
    print("\nSample analysis results:")
    display(analysis_df.head(3))
    
    print("\nAnalysis statistics:")
    print(f"  Average text length: {analysis_df['text_length'].mean():.0f} characters")
    print(f"  Files with tables: {analysis_df['table_count'].gt(0).sum()}")
    print(f"  Files with numeric tables: {analysis_df['has_numeric_tables'].sum()}")
    print(f"  Files with measurements: {analysis_df['measurement_count'].gt(0).sum()}")
else:
    print("No analysis data available")

# Export results for downstream processing
print("\nEXPORTING ANALYSIS RESULTS...")

# Store results in global variables
globals()['pdf_content_analysis'] = analysis_results
globals()['pdf_analysis_dataframe'] = analysis_df
globals()['pdf_document_categories'] = dict(document_categories)
globals()['pdf_all_keywords'] = dict(all_keywords)
globals()['pdf_all_measurements'] = all_measurements

# Save analysis results to files
output_dir = Path('../../../data/cad')

# Save analysis DataFrame
analysis_csv_path = output_dir / 'cad_pdf_content_analysis.csv'
try:
    analysis_df.to_csv(analysis_csv_path, index=False)
    print(f"Analysis DataFrame saved to: {analysis_csv_path}")
except Exception as e:
    logger.warning(f"Could not save analysis CSV: {e}")

# Save detailed analysis as JSON
analysis_json_path = output_dir / 'cad_pdf_content_analysis_detailed.json'
try:
    analysis_export = {
        'analysis_results': analysis_results,
        'document_categories': dict(document_categories),
        'top_keywords': dict(all_keywords.most_common(50)),
        'measurements': all_measurements,
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    with open(analysis_json_path, 'w') as f:
        json.dump(analysis_export, f, indent=2, default=str)
    print(f"Detailed analysis saved to: {analysis_json_path}")
except Exception as e:
    logger.warning(f"Could not save analysis JSON: {e}")

# Create summary report
summary_path = output_dir / 'cad_pdf_analysis_summary.txt'
try:
    with open(summary_path, 'w') as f:
        f.write("CAD PDF Content Analysis Summary\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Files Analyzed: {len(analysis_results)}\n\n")
        
        f.write("Document Categories:\n")
        for category, count in document_categories.most_common():
            f.write(f"  {category}: {count} documents\n")
        
        f.write("\nTop Keywords:\n")
        for keyword, count in all_keywords.most_common(20):
            f.write(f"  {keyword}: {count} occurrences\n")
        
        f.write(f"\nMeasurements Found: {len(all_measurements)} total, {len(set(all_measurements))} unique\n")
    
    print(f"Summary report saved to: {summary_path}")
except Exception as e:
    logger.warning(f"Could not save summary report: {e}")

print(f"\nResults exported to variables:")
print(f"  - pdf_content_analysis: {len(analysis_results)} file analyses")
print(f"  - pdf_analysis_dataframe: DataFrame with {len(analysis_df)} rows")
print(f"  - pdf_document_categories: {len(document_categories)} categories")
print(f"  - pdf_all_keywords: {len(all_keywords)} unique keywords")

logger.info("PDF content analysis results exported successfully")

# Final status report
print("\n" + "="*60)
print("CAD PDF CONTENT ANALYSIS COMPLETED")
print("="*60)

if analysis_results:
    print(f"Successfully analyzed {len(analysis_results)} PDF files")
    print(f"Identified {len(document_categories)} document categories")
    print(f"Extracted {len(all_keywords)} unique keywords")
    print(f"Found {len(all_measurements)} measurements")
    print(f"Generated comprehensive analysis dataset")
    print("\nCAD PDF processing pipeline completed successfully!")
    print("All stages: Discovery → Text/Table Extraction → Metadata Extraction → Content Analysis")
else:
    print("No content analysis was performed")
    print("Please check previous pipeline stages")

print(f"\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
logger.info("CAD PDF content analysis notebook execution completed")