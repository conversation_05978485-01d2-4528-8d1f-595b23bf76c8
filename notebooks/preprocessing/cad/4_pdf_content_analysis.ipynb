{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD PDF Content Analysis and Summarization\n", "\n", "This notebook performs content analysis and summarization of extracted CAD PDF data, generating insights and preparing data for downstream analysis.\n", "\n", "**Stage**: Preprocessing - CAD Pipeline  \n", "**Input Data**: Extracted text, tables, and metadata from previous stages  \n", "**Output**: Content analysis, summaries, and structured insights  \n", "**Format**: Analysis reports, summary statistics, and processed datasets  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Analyze and summarize extracted CAD PDF content to:\n", "- Generate comprehensive content summaries\n", "- Identify key technical information and specifications\n", "- Categorize documents by type and content\n", "- Extract quantitative data and measurements\n", "- Prepare structured datasets for downstream analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Load and consolidate data from previous pipeline stages\n", "2. Perform text analysis and keyword extraction\n", "3. Analyze table content and extract quantitative data\n", "4. Generate document categorization and classification\n", "5. Create comprehensive summaries and reports\n", "6. Export structured datasets for downstream use"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Warning: WordCloud not available\n", "CAD PDF Content Analysis and Summarization - Starting...\n", "Timestamp: 2025-06-17 13:22:31\n"]}], "source": ["import logging\n", "import re\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "from typing import Dict, List, Optional, Any, Tuple\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Text processing libraries\n", "try:\n", "    from wordcloud import WordCloud\n", "    WORDCLOUD_AVAILABLE = True\n", "    print(\"WordCloud library loaded successfully\")\n", "except ImportError:\n", "    WORDCLOUD_AVAILABLE = False\n", "    print(\"Warning: WordCloud not available\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"CAD PDF Content Analysis and Summarization - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:31,254 - INFO - Loaded 33 PDF files from directory scan\n", "2025-06-17 13:22:31,257 - INFO - Loaded metadata from CSV: 33 records\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading data from previous pipeline stages...\n", "Data loaded:\n", "  - PDF files: 33\n", "  - Extraction results: 0\n", "  - Metadata results: 0\n", "  - Metadata DataFrame: 33 rows\n"]}], "source": ["# Load data from previous pipeline stages\n", "print(\"Loading data from previous pipeline stages...\")\n", "\n", "# Initialize data containers\n", "pdf_files = globals().get('discovered_pdf_files', [])\n", "extraction_results = globals().get('pdf_extraction_results', {})\n", "metadata_results = globals().get('pdf_metadata_results', {})\n", "metadata_df = globals().get('pdf_metadata_dataframe', pd.DataFrame())\n", "pdf_classifications = globals().get('pdf_classifications', {})\n", "\n", "# Fallback: load from files if variables not available\n", "if not pdf_files or not extraction_results:\n", "    project_root = Path('../../../')\n", "    cad_data_path = project_root / 'data' / 'cad'\n", "    \n", "    # Load PDF files\n", "    if not pdf_files:\n", "        pdf_files = [str(p) for p in cad_data_path.rglob('*.pdf')]\n", "        logger.info(f\"Loaded {len(pdf_files)} PDF files from directory scan\")\n", "    \n", "    # Try to load metadata from CSV if available\n", "    if metadata_df.empty:\n", "        csv_path = cad_data_path / 'cad_pdf_metadata.csv'\n", "        if csv_path.exists():\n", "            metadata_df = pd.read_csv(csv_path)\n", "            logger.info(f\"Loaded metadata from CSV: {len(metadata_df)} records\")\n", "\n", "print(f\"Data loaded:\")\n", "print(f\"  - PDF files: {len(pdf_files)}\")\n", "print(f\"  - Extraction results: {len(extraction_results)}\")\n", "print(f\"  - Metadata results: {len(metadata_results)}\")\n", "print(f\"  - Metadata DataFrame: {len(metadata_df)} rows\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Content analysis utilities defined\n"]}], "source": ["# Content analysis utilities\n", "def extract_keywords(text: str, min_length: int = 3, top_n: int = 20) -> List[Tuple[str, int]]:\n", "    \"\"\"Extract keywords from text content.\"\"\"\n", "    if not text:\n", "        return []\n", "    \n", "    # Clean and tokenize text\n", "    text = re.sub(r'[^a-zA-Z\\s]', ' ', text.lower())\n", "    words = text.split()\n", "    \n", "    # Filter words by length and common stop words\n", "    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'shall', 'this', 'that', 'these', 'those', 'a', 'an'}\n", "    filtered_words = [word for word in words if len(word) >= min_length and word not in stop_words]\n", "    \n", "    # Count word frequencies\n", "    word_counts = Counter(filtered_words)\n", "    \n", "    return word_counts.most_common(top_n)\n", "\n", "def categorize_document(text: str, metadata: Dict[str, Any]) -> str:\n", "    \"\"\"Categorize document based on content and metadata.\"\"\"\n", "    text_lower = text.lower() if text else ''\n", "    file_name = metadata.get('file_name', '').lower()\n", "    \n", "    # Define category patterns\n", "    categories = {\n", "        'foundation_plan': ['foundation', 'pile', 'pier', 'footing', 'concrete'],\n", "        'electrical_plan': ['electrical', 'cable', 'conduit', 'power', 'voltage', 'circuit'],\n", "        'site_plan': ['site', 'layout', 'general', 'overall', 'location'],\n", "        'structural_plan': ['structural', 'beam', 'column', 'steel', 'frame'],\n", "        'detail_drawing': ['detail', 'section', 'elevation', 'cross'],\n", "        'specification': ['specification', 'spec', 'requirement', 'standard']\n", "    }\n", "    \n", "    # Score each category\n", "    category_scores = {}\n", "    for category, keywords in categories.items():\n", "        score = 0\n", "        for keyword in keywords:\n", "            score += text_lower.count(keyword) + file_name.count(keyword) * 2\n", "        category_scores[category] = score\n", "    \n", "    # Return category with highest score, or 'unknown' if no matches\n", "    if max(category_scores.values()) > 0:\n", "        return max(category_scores, key=category_scores.get)\n", "    else:\n", "        return 'unknown'\n", "\n", "def extract_measurements(text: str) -> List[str]:\n", "    \"\"\"Extract measurement values from text.\"\"\"\n", "    if not text:\n", "        return []\n", "    \n", "    # Patterns for common measurements\n", "    measurement_patterns = [\n", "        r'\\d+\\.?\\d*\\s*(?:mm|cm|m|ft|in|inch|inches)',\n", "        r'\\d+\\.?\\d*\\s*x\\s*\\d+\\.?\\d*',  # dimensions\n", "        r'\\d+\\.?\\d*\\s*°',  # angles\n", "        r'\\d+\\.?\\d*\\s*%',  # percentages\n", "    ]\n", "    \n", "    measurements = []\n", "    for pattern in measurement_patterns:\n", "        matches = re.findall(pattern, text, re.IGNORECASE)\n", "        measurements.extend(matches)\n", "    \n", "    return list(set(measurements))  # Remove duplicates\n", "\n", "print(\"Content analysis utilities defined\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:31,270 - INFO - Content analysis completed for 0 files\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Performing content analysis...\n", "\n", "Content analysis completed for 0 files\n"]}], "source": ["# Perform content analysis\n", "print(\"\\nPerforming content analysis...\")\n", "\n", "analysis_results = {}\n", "all_keywords = Counter()\n", "document_categories = Counter()\n", "all_measurements = []\n", "\n", "for file_name, extraction_data in extraction_results.items():\n", "    print(f\"Analyzing: {file_name}\")\n", "    \n", "    text_content = extraction_data.get('text', '')\n", "    tables = extraction_data.get('tables', [])\n", "    metadata = metadata_results.get(file_name, {})\n", "    \n", "    # Extract keywords\n", "    keywords = extract_keywords(text_content)\n", "    all_keywords.update(dict(keywords))\n", "    \n", "    # Categorize document\n", "    category = categorize_document(text_content, metadata)\n", "    document_categories[category] += 1\n", "    \n", "    # Extract measurements\n", "    measurements = extract_measurements(text_content)\n", "    all_measurements.extend(measurements)\n", "    \n", "    # Analyze tables\n", "    table_analysis = []\n", "    for i, table in enumerate(tables):\n", "        if not table.empty:\n", "            table_info = {\n", "                'table_index': i,\n", "                'shape': table.shape,\n", "                'columns': list(table.columns),\n", "                'numeric_columns': list(table.select_dtypes(include=[np.number]).columns),\n", "                'has_numeric_data': len(table.select_dtypes(include=[np.number]).columns) > 0\n", "            }\n", "            table_analysis.append(table_info)\n", "    \n", "    # Store analysis results\n", "    analysis_results[file_name] = {\n", "        'keywords': keywords,\n", "        'category': category,\n", "        'measurements': measurements,\n", "        'table_analysis': table_analysis,\n", "        'text_length': len(text_content),\n", "        'table_count': len(tables),\n", "        'has_numeric_tables': any(t['has_numeric_data'] for t in table_analysis)\n", "    }\n", "\n", "print(f\"\\nContent analysis completed for {len(analysis_results)} files\")\n", "logger.info(f\"Content analysis completed for {len(analysis_results)} files\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "CAD PDF CONTENT ANALYSIS SUMMARY\n", "============================================================\n", "\n", "Document Categories:\n", "\n", "Top Keywords (across all documents):\n", "\n", "Measurement Analysis:\n", "  Total measurements found: 0\n", "  Unique measurements: 0\n", "\n", "============================================================\n"]}], "source": ["# Generate analysis summary and visualizations\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"CAD PDF CONTENT ANALYSIS SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nDocument Categories:\")\n", "for category, count in document_categories.most_common():\n", "    print(f\"  {category}: {count} documents\")\n", "\n", "print(f\"\\nTop Keywords (across all documents):\")\n", "for keyword, count in all_keywords.most_common(15):\n", "    print(f\"  {keyword}: {count} occurrences\")\n", "\n", "print(f\"\\nMeasurement Analysis:\")\n", "print(f\"  Total measurements found: {len(all_measurements)}\")\n", "print(f\"  Unique measurements: {len(set(all_measurements))}\")\n", "if all_measurements:\n", "    measurement_counter = Counter(all_measurements)\n", "    print(f\"  Most common measurements:\")\n", "    for measurement, count in measurement_counter.most_common(10):\n", "        print(f\"    {measurement}: {count} times\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Creating analysis visualizations...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Visualizations created successfully\n"]}], "source": ["# Create visualizations\n", "print(\"\\nCreating analysis visualizations...\")\n", "\n", "# Set up the plotting area\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('CAD PDF Content Analysis Dashboard', fontsize=16, fontweight='bold')\n", "\n", "# 1. Document categories pie chart\n", "if document_categories:\n", "    categories, counts = zip(*document_categories.most_common())\n", "    axes[0, 0].pie(counts, labels=categories, autopct='%1.1f%%', startangle=90)\n", "    axes[0, 0].set_title('Document Categories Distribution')\n", "else:\n", "    axes[0, 0].text(0.5, 0.5, 'No category data', ha='center', va='center')\n", "    axes[0, 0].set_title('Document Categories Distribution')\n", "\n", "# 2. Top keywords bar chart\n", "if all_keywords:\n", "    top_keywords = all_keywords.most_common(10)\n", "    keywords, keyword_counts = zip(*top_keywords)\n", "    axes[0, 1].barh(range(len(keywords)), keyword_counts)\n", "    axes[0, 1].set_yticks(range(len(keywords)))\n", "    axes[0, 1].set_yticklabels(keywords)\n", "    axes[0, 1].set_xlabel('Frequency')\n", "    axes[0, 1].set_title('Top 10 Keywords')\n", "    axes[0, 1].invert_yaxis()\n", "else:\n", "    axes[0, 1].text(0.5, 0.5, 'No keyword data', ha='center', va='center')\n", "    axes[0, 1].set_title('Top 10 Keywords')\n", "\n", "# 3. File size vs text length scatter plot\n", "if not metadata_df.empty and analysis_results:\n", "    file_sizes = []\n", "    text_lengths = []\n", "    \n", "    for file_name in analysis_results.keys():\n", "        if file_name in metadata_df['file_name'].values:\n", "            file_size = metadata_df[metadata_df['file_name'] == file_name]['file_size_mb'].iloc[0]\n", "            text_length = analysis_results[file_name]['text_length']\n", "            file_sizes.append(file_size)\n", "            text_lengths.append(text_length)\n", "    \n", "    if file_sizes and text_lengths:\n", "        axes[1, 0].scatter(file_sizes, text_lengths, alpha=0.6)\n", "        axes[1, 0].set_xlabel('File Size (MB)')\n", "        axes[1, 0].set_ylabel('Text Length (characters)')\n", "        axes[1, 0].set_title('File Size vs Text Content')\n", "    else:\n", "        axes[1, 0].text(0.5, 0.5, 'No size/text data', ha='center', va='center')\n", "        axes[1, 0].set_title('File Size vs Text Content')\n", "else:\n", "    axes[1, 0].text(0.5, 0.5, 'No metadata available', ha='center', va='center')\n", "    axes[1, 0].set_title('File Size vs Text Content')\n", "\n", "# 4. Table analysis\n", "files_with_tables = sum(1 for result in analysis_results.values() if result['table_count'] > 0)\n", "files_with_numeric_tables = sum(1 for result in analysis_results.values() if result['has_numeric_tables'])\n", "files_without_tables = len(analysis_results) - files_with_tables\n", "\n", "table_data = ['Files with Tables', 'Files with Numeric Tables', 'Files without Tables']\n", "table_counts = [files_with_tables, files_with_numeric_tables, files_without_tables]\n", "\n", "axes[1, 1].bar(table_data, table_counts, color=['skyblue', 'lightgreen', 'lightcoral'])\n", "axes[1, 1].set_ylabel('Number of Files')\n", "axes[1, 1].set_title('Table Content Analysis')\n", "axes[1, 1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Visualizations created successfully\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Word cloud generation skipped (WordCloud library not available or no keywords)\n"]}], "source": ["# Generate word cloud if available\n", "if WORDCLOUD_AVAILABLE and all_keywords:\n", "    print(\"\\nGenerating word cloud...\")\n", "    \n", "    # Create word cloud from keywords\n", "    wordcloud = WordCloud(\n", "        width=800, \n", "        height=400, \n", "        background_color='white',\n", "        max_words=50,\n", "        colormap='viridis'\n", "    ).generate_from_frequencies(all_keywords)\n", "    \n", "    plt.figure(figsize=(12, 6))\n", "    plt.imshow(wordcloud, interpolation='bilinear')\n", "    plt.axis('off')\n", "    plt.title('CAD PDF Content Word Cloud', fontsize=16, fontweight='bold', pad=20)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"Word cloud generated successfully\")\n", "else:\n", "    print(\"\\nWord cloud generation skipped (WordCloud library not available or no keywords)\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Creating comprehensive analysis DataFrame...\n", "Created analysis DataFrame with 0 rows and 0 columns\n", "No analysis data available\n"]}], "source": ["# Create comprehensive analysis DataFrame\n", "print(\"\\nCreating comprehensive analysis DataFrame...\")\n", "\n", "analysis_records = []\n", "for file_name, analysis_data in analysis_results.items():\n", "    # Get metadata if available\n", "    metadata = metadata_results.get(file_name, {})\n", "    \n", "    record = {\n", "        'file_name': file_name,\n", "        'category': analysis_data['category'],\n", "        'text_length': analysis_data['text_length'],\n", "        'table_count': analysis_data['table_count'],\n", "        'has_numeric_tables': analysis_data['has_numeric_tables'],\n", "        'measurement_count': len(analysis_data['measurements']),\n", "        'keyword_count': len(analysis_data['keywords']),\n", "        'top_keywords': ', '.join([kw[0] for kw in analysis_data['keywords'][:5]]),\n", "        'measurements_sample': ', '.join(analysis_data['measurements'][:5]),\n", "        'drawing_number': metadata.get('cad_metadata', {}).get('drawing_number', ''),\n", "        'revision': metadata.get('cad_metadata', {}).get('revision', ''),\n", "        'page_count': metadata.get('page_count', 0),\n", "        'file_size_mb': metadata.get('file_size_bytes', 0) / (1024 * 1024) if metadata.get('file_size_bytes') else 0\n", "    }\n", "    \n", "    analysis_records.append(record)\n", "\n", "analysis_df = pd.DataFrame(analysis_records)\n", "\n", "print(f\"Created analysis DataFrame with {len(analysis_df)} rows and {len(analysis_df.columns)} columns\")\n", "\n", "# Display sample of analysis results\n", "if not analysis_df.empty:\n", "    print(\"\\nSample analysis results:\")\n", "    display(analysis_df.head(3))\n", "    \n", "    print(\"\\nAnalysis statistics:\")\n", "    print(f\"  Average text length: {analysis_df['text_length'].mean():.0f} characters\")\n", "    print(f\"  Files with tables: {analysis_df['table_count'].gt(0).sum()}\")\n", "    print(f\"  Files with numeric tables: {analysis_df['has_numeric_tables'].sum()}\")\n", "    print(f\"  Files with measurements: {analysis_df['measurement_count'].gt(0).sum()}\")\n", "else:\n", "    print(\"No analysis data available\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:31,595 - INFO - PDF content analysis results exported successfully\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "EXPORTING ANALYSIS RESULTS...\n", "Analysis DataFrame saved to: ../../../data/cad/cad_pdf_content_analysis.csv\n", "Detailed analysis saved to: ../../../data/cad/cad_pdf_content_analysis_detailed.json\n", "Summary report saved to: ../../../data/cad/cad_pdf_analysis_summary.txt\n", "\n", "Results exported to variables:\n", "  - pdf_content_analysis: 0 file analyses\n", "  - pdf_analysis_dataframe: DataFrame with 0 rows\n", "  - pdf_document_categories: 0 categories\n", "  - pdf_all_keywords: 0 unique keywords\n"]}], "source": ["# Export results for downstream processing\n", "print(\"\\nEXPORTING ANALYSIS RESULTS...\")\n", "\n", "# Store results in global variables\n", "globals()['pdf_content_analysis'] = analysis_results\n", "globals()['pdf_analysis_dataframe'] = analysis_df\n", "globals()['pdf_document_categories'] = dict(document_categories)\n", "globals()['pdf_all_keywords'] = dict(all_keywords)\n", "globals()['pdf_all_measurements'] = all_measurements\n", "\n", "# Save analysis results to files\n", "output_dir = Path('../../../data/cad')\n", "\n", "# Save analysis DataFrame\n", "analysis_csv_path = output_dir / 'cad_pdf_content_analysis.csv'\n", "try:\n", "    analysis_df.to_csv(analysis_csv_path, index=False)\n", "    print(f\"Analysis DataFrame saved to: {analysis_csv_path}\")\n", "except Exception as e:\n", "    logger.warning(f\"Could not save analysis CSV: {e}\")\n", "\n", "# Save detailed analysis as JSON\n", "analysis_json_path = output_dir / 'cad_pdf_content_analysis_detailed.json'\n", "try:\n", "    analysis_export = {\n", "        'analysis_results': analysis_results,\n", "        'document_categories': dict(document_categories),\n", "        'top_keywords': dict(all_keywords.most_common(50)),\n", "        'measurements': all_measurements,\n", "        'analysis_timestamp': datetime.now().isoformat()\n", "    }\n", "    \n", "    with open(analysis_json_path, 'w') as f:\n", "        json.dump(analysis_export, f, indent=2, default=str)\n", "    print(f\"Detailed analysis saved to: {analysis_json_path}\")\n", "except Exception as e:\n", "    logger.warning(f\"Could not save analysis JSON: {e}\")\n", "\n", "# Create summary report\n", "summary_path = output_dir / 'cad_pdf_analysis_summary.txt'\n", "try:\n", "    with open(summary_path, 'w') as f:\n", "        f.write(\"CAD PDF Content Analysis Summary\\n\")\n", "        f.write(\"=\" * 40 + \"\\n\\n\")\n", "        f.write(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "        f.write(f\"Total Files Analyzed: {len(analysis_results)}\\n\\n\")\n", "        \n", "        f.write(\"Document Categories:\\n\")\n", "        for category, count in document_categories.most_common():\n", "            f.write(f\"  {category}: {count} documents\\n\")\n", "        \n", "        f.write(\"\\nTop Keywords:\\n\")\n", "        for keyword, count in all_keywords.most_common(20):\n", "            f.write(f\"  {keyword}: {count} occurrences\\n\")\n", "        \n", "        f.write(f\"\\nMeasurements Found: {len(all_measurements)} total, {len(set(all_measurements))} unique\\n\")\n", "    \n", "    print(f\"Summary report saved to: {summary_path}\")\n", "except Exception as e:\n", "    logger.warning(f\"Could not save summary report: {e}\")\n", "\n", "print(f\"\\nResults exported to variables:\")\n", "print(f\"  - pdf_content_analysis: {len(analysis_results)} file analyses\")\n", "print(f\"  - pdf_analysis_dataframe: DataFrame with {len(analysis_df)} rows\")\n", "print(f\"  - pdf_document_categories: {len(document_categories)} categories\")\n", "print(f\"  - pdf_all_keywords: {len(all_keywords)} unique keywords\")\n", "\n", "logger.info(\"PDF content analysis results exported successfully\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:31,600 - INFO - CAD PDF content analysis notebook execution completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "CAD PDF CONTENT ANALYSIS COMPLETED\n", "============================================================\n", "No content analysis was performed\n", "Please check previous pipeline stages\n", "\n", "Completion time: 2025-06-17 13:22:31\n"]}], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"CAD PDF CONTENT ANALYSIS COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if analysis_results:\n", "    print(f\"Successfully analyzed {len(analysis_results)} PDF files\")\n", "    print(f\"Identified {len(document_categories)} document categories\")\n", "    print(f\"Extracted {len(all_keywords)} unique keywords\")\n", "    print(f\"Found {len(all_measurements)} measurements\")\n", "    print(f\"Generated comprehensive analysis dataset\")\n", "    print(\"\\nCAD PDF processing pipeline completed successfully!\")\n", "    print(\"All stages: Discovery → Text/Table Extraction → Metadata Extraction → Content Analysis\")\n", "else:\n", "    print(\"No content analysis was performed\")\n", "    print(\"Please check previous pipeline stages\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"CAD PDF content analysis notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}