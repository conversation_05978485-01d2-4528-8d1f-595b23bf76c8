{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD PDF Content Analysis and Summarization\n", "\n", "This notebook performs content analysis and summarization of extracted CAD PDF data, generating insights and preparing data for downstream analysis.\n", "\n", "**Stage**: Preprocessing - CAD Pipeline  \n", "**Input Data**: Extracted text, tables, and metadata from previous stages  \n", "**Output**: Content analysis, summaries, and structured insights  \n", "**Format**: Analysis reports, summary statistics, and processed datasets  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Analyze and summarize extracted CAD PDF content to:\n", "- Generate comprehensive content summaries\n", "- Identify key technical information and specifications\n", "- Categorize documents by type and content\n", "- Extract quantitative data and measurements\n", "- Prepare structured datasets for downstream analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Load and consolidate data from previous pipeline stages\n", "2. Perform text analysis and keyword extraction\n", "3. Analyze table content and extract quantitative data\n", "4. Generate document categorization and classification\n", "5. Create comprehensive summaries and reports\n", "6. Export structured datasets for downstream use"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import re\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "from typing import Dict, List, Optional, Any, Tuple\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Text processing libraries\n", "try:\n", "    from wordcloud import WordCloud\n", "    WORDCLOUD_AVAILABLE = True\n", "    print(\"WordCloud library loaded successfully\")\n", "except ImportError:\n", "    WORDCLOUD_AVAILABLE = False\n", "    print(\"Warning: WordCloud not available\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"CAD PDF Content Analysis and Summarization - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data from previous pipeline stages\n", "print(\"Loading data from previous pipeline stages...\")\n", "\n", "# Initialize data containers\n", "pdf_files = globals().get('discovered_pdf_files', [])\n", "extraction_results = globals().get('pdf_extraction_results', {})\n", "metadata_results = globals().get('pdf_metadata_results', {})\n", "metadata_df = globals().get('pdf_metadata_dataframe', pd.DataFrame())\n", "\n", "# Fallback: load from files if variables not available\n", "if not pdf_files or not extraction_results:\n", "    project_root = Path('../../../')\n", "    cad_data_path = project_root / 'data' / 'cad'\n", "    \n", "    # Load PDF files\n", "    if not pdf_files:\n", "        pdf_files = [str(p) for p in cad_data_path.rglob('*.pdf')]\n", "        logger.info(f\"Loaded {len(pdf_files)} PDF files from directory scan\")\n", "    \n", "    # Try to load metadata from CSV if available\n", "    if metadata_df.empty:\n", "        csv_path = cad_data_path / 'cad_pdf_metadata.csv'\n", "        if csv_path.exists():\n", "            metadata_df = pd.read_csv(csv_path)\n", "            logger.info(f\"Loaded metadata from CSV: {len(metadata_df)} records\")\n", "\n", "print(f\"Data loaded:\")\n", "print(f\"  - PDF files: {len(pdf_files)}\")\n", "print(f\"  - Extraction results: {len(extraction_results)}\")\n", "print(f\"  - Metadata results: {len(metadata_results)}\")\n", "print(f\"  - Metadata DataFrame: {len(metadata_df)} rows\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Content analysis utilities\n", "def extract_keywords(text: str, min_length: int = 3, top_n: int = 20) -> List[Tuple[str, int]]:\n", "    \"\"\"Extract keywords from text content.\"\"\"\n", "    if not text:\n", "        return []\n", "    \n", "    # Clean and tokenize text\n", "    text = re.sub(r'[^a-zA-Z\\s]', ' ', text.lower())\n", "    words = text.split()\n", "    \n", "    # Filter words by length and common stop words\n", "    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'shall', 'this', 'that', 'these', 'those', 'a', 'an'}\n", "    filtered_words = [word for word in words if len(word) >= min_length and word not in stop_words]\n", "    \n", "    # Count word frequencies\n", "    word_counts = Counter(filtered_words)\n", "    \n", "    return word_counts.most_common(top_n)\n", "\n", "def categorize_document(text: str, metadata: Dict[str, Any]) -> str:\n", "    \"\"\"Categorize document based on content and metadata.\"\"\"\n", "    text_lower = text.lower() if text else ''\n", "    file_name = metadata.get('file_name', '').lower()\n", "    \n", "    # Define category patterns\n", "    categories = {\n", "        'foundation_plan': ['foundation', 'pile', 'pier', 'footing', 'concrete'],\n", "        'electrical_plan': ['electrical', 'cable', 'conduit', 'power', 'voltage', 'circuit'],\n", "        'site_plan': ['site', 'layout', 'general', 'overall', 'location'],\n", "        'structural_plan': ['structural', 'beam', 'column', 'steel', 'frame'],\n", "        'detail_drawing': ['detail', 'section', 'elevation', 'cross'],\n", "        'specification': ['specification', 'spec', 'requirement', 'standard']\n", "    }\n", "    \n", "    # Score each category\n", "    category_scores = {}\n", "    for category, keywords in categories.items():\n", "        score = 0\n", "        for keyword in keywords:\n", "            score += text_lower.count(keyword) + file_name.count(keyword) * 2\n", "        category_scores[category] = score\n", "    \n", "    # Return category with highest score, or 'unknown' if no matches\n", "    if max(category_scores.values()) > 0:\n", "        return max(category_scores, key=category_scores.get)\n", "    else:\n", "        return 'unknown'\n", "\n", "def extract_measurements(text: str) -> List[str]:\n", "    \"\"\"Extract measurement values from text.\"\"\"\n", "    if not text:\n", "        return []\n", "    \n", "    # Patterns for common measurements\n", "    measurement_patterns = [\n", "        r'\\d+\\.?\\d*\\s*(?:mm|cm|m|ft|in|inch|inches)',\n", "        r'\\d+\\.?\\d*\\s*x\\s*\\d+\\.?\\d*',  # dimensions\n", "        r'\\d+\\.?\\d*\\s*°',  # angles\n", "        r'\\d+\\.?\\d*\\s*%',  # percentages\n", "    ]\n", "    \n", "    measurements = []\n", "    for pattern in measurement_patterns:\n", "        matches = re.findall(pattern, text, re.IGNORECASE)\n", "        measurements.extend(matches)\n", "    \n", "    return list(set(measurements))  # Remove duplicates\n", "\n", "print(\"Content analysis utilities defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform content analysis\n", "print(\"\\nPerforming content analysis...\")\n", "\n", "analysis_results = {}\n", "all_keywords = Counter()\n", "document_categories = Counter()\n", "all_measurements = []\n", "\n", "for file_name, extraction_data in extraction_results.items():\n", "    print(f\"Analyzing: {file_name}\")\n", "    \n", "    text_content = extraction_data.get('text', '')\n", "    tables = extraction_data.get('tables', [])\n", "    metadata = metadata_results.get(file_name, {})\n", "    \n", "    # Extract keywords\n", "    keywords = extract_keywords(text_content)\n", "    all_keywords.update(dict(keywords))\n", "    \n", "    # Categorize document\n", "    category = categorize_document(text_content, metadata)\n", "    document_categories[category] += 1\n", "    \n", "    # Extract measurements\n", "    measurements = extract_measurements(text_content)\n", "    all_measurements.extend(measurements)\n", "    \n", "    # Analyze tables\n", "    table_analysis = []\n", "    for i, table in enumerate(tables):\n", "        if not table.empty:\n", "            table_info = {\n", "                'table_index': i,\n", "                'shape': table.shape,\n", "                'columns': list(table.columns),\n", "                'numeric_columns': list(table.select_dtypes(include=[np.number]).columns),\n", "                'has_numeric_data': len(table.select_dtypes(include=[np.number]).columns) > 0\n", "            }\n", "            table_analysis.append(table_info)\n", "    \n", "    # Store analysis results\n", "    analysis_results[file_name] = {\n", "        'keywords': keywords,\n", "        'category': category,\n", "        'measurements': measurements,\n", "        'table_analysis': table_analysis,\n", "        'text_length': len(text_content),\n", "        'table_count': len(tables),\n", "        'has_numeric_tables': any(t['has_numeric_data'] for t in table_analysis)\n", "    }\n", "\n", "print(f\"\\nContent analysis completed for {len(analysis_results)} files\")\n", "logger.info(f\"Content analysis completed for {len(analysis_results)} files\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate analysis summary and visualizations\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"CAD PDF CONTENT ANALYSIS SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nDocument Categories:\")\n", "for category, count in document_categories.most_common():\n", "    print(f\"  {category}: {count} documents\")\n", "\n", "print(f\"\\nTop Keywords (across all documents):\")\n", "for keyword, count in all_keywords.most_common(15):\n", "    print(f\"  {keyword}: {count} occurrences\")\n", "\n", "print(f\"\\nMeasurement Analysis:\")\n", "print(f\"  Total measurements found: {len(all_measurements)}\")\n", "print(f\"  Unique measurements: {len(set(all_measurements))}\")\n", "if all_measurements:\n", "    measurement_counter = Counter(all_measurements)\n", "    print(f\"  Most common measurements:\")\n", "    for measurement, count in measurement_counter.most_common(10):\n", "        print(f\"    {measurement}: {count} times\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualizations\n", "print(\"\\nCreating analysis visualizations...\")\n", "\n", "# Set up the plotting area\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('CAD PDF Content Analysis Dashboard', fontsize=16, fontweight='bold')\n", "\n", "# 1. Document categories pie chart\n", "if document_categories:\n", "    categories, counts = zip(*document_categories.most_common())\n", "    axes[0, 0].pie(counts, labels=categories, autopct='%1.1f%%', startangle=90)\n", "    axes[0, 0].set_title('Document Categories Distribution')\n", "else:\n", "    axes[0, 0].text(0.5, 0.5, 'No category data', ha='center', va='center')\n", "    axes[0, 0].set_title('Document Categories Distribution')\n", "\n", "# 2. Top keywords bar chart\n", "if all_keywords:\n", "    top_keywords = all_keywords.most_common(10)\n", "    keywords, keyword_counts = zip(*top_keywords)\n", "    axes[0, 1].barh(range(len(keywords)), keyword_counts)\n", "    axes[0, 1].set_yticks(range(len(keywords)))\n", "    axes[0, 1].set_yticklabels(keywords)\n", "    axes[0, 1].set_xlabel('Frequency')\n", "    axes[0, 1].set_title('Top 10 Keywords')\n", "    axes[0, 1].invert_yaxis()\n", "else:\n", "    axes[0, 1].text(0.5, 0.5, 'No keyword data', ha='center', va='center')\n", "    axes[0, 1].set_title('Top 10 Keywords')\n", "\n", "# 3. File size vs text length scatter plot\n", "if not metadata_df.empty and analysis_results:\n", "    file_sizes = []\n", "    text_lengths = []\n", "    \n", "    for file_name in analysis_results.keys():\n", "        if file_name in metadata_df['file_name'].values:\n", "            file_size = metadata_df[metadata_df['file_name'] == file_name]['file_size_mb'].iloc[0]\n", "            text_length = analysis_results[file_name]['text_length']\n", "            file_sizes.append(file_size)\n", "            text_lengths.append(text_length)\n", "    \n", "    if file_sizes and text_lengths:\n", "        axes[1, 0].scatter(file_sizes, text_lengths, alpha=0.6)\n", "        axes[1, 0].set_xlabel('File Size (MB)')\n", "        axes[1, 0].set_ylabel('Text Length (characters)')\n", "        axes[1, 0].set_title('File Size vs Text Content')\n", "    else:\n", "        axes[1, 0].text(0.5, 0.5, 'No size/text data', ha='center', va='center')\n", "        axes[1, 0].set_title('File Size vs Text Content')\n", "else:\n", "    axes[1, 0].text(0.5, 0.5, 'No metadata available', ha='center', va='center')\n", "    axes[1, 0].set_title('File Size vs Text Content')\n", "\n", "# 4. Table analysis\n", "files_with_tables = sum(1 for result in analysis_results.values() if result['table_count'] > 0)\n", "files_with_numeric_tables = sum(1 for result in analysis_results.values() if result['has_numeric_tables'])\n", "files_without_tables = len(analysis_results) - files_with_tables\n", "\n", "table_data = ['Files with Tables', 'Files with Numeric Tables', 'Files without Tables']\n", "table_counts = [files_with_tables, files_with_numeric_tables, files_without_tables]\n", "\n", "axes[1, 1].bar(table_data, table_counts, color=['skyblue', 'lightgreen', 'lightcoral'])\n", "axes[1, 1].set_ylabel('Number of Files')\n", "axes[1, 1].set_title('Table Content Analysis')\n", "axes[1, 1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Visualizations created successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate word cloud if available\n", "if WORDCLOUD_AVAILABLE and all_keywords:\n", "    print(\"\\nGenerating word cloud...\")\n", "    \n", "    # Create word cloud from keywords\n", "    wordcloud = WordCloud(\n", "        width=800, \n", "        height=400, \n", "        background_color='white',\n", "        max_words=50,\n", "        colormap='viridis'\n", "    ).generate_from_frequencies(all_keywords)\n", "    \n", "    plt.figure(figsize=(12, 6))\n", "    plt.imshow(wordcloud, interpolation='bilinear')\n", "    plt.axis('off')\n", "    plt.title('CAD PDF Content Word Cloud', fontsize=16, fontweight='bold', pad=20)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"Word cloud generated successfully\")\n", "else:\n", "    print(\"\\nWord cloud generation skipped (WordCloud library not available or no keywords)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive analysis DataFrame\n", "print(\"\\nCreating comprehensive analysis DataFrame...\")\n", "\n", "analysis_records = []\n", "for file_name, analysis_data in analysis_results.items():\n", "    # Get metadata if available\n", "    metadata = metadata_results.get(file_name, {})\n", "    \n", "    record = {\n", "        'file_name': file_name,\n", "        'category': analysis_data['category'],\n", "        'text_length': analysis_data['text_length'],\n", "        'table_count': analysis_data['table_count'],\n", "        'has_numeric_tables': analysis_data['has_numeric_tables'],\n", "        'measurement_count': len(analysis_data['measurements']),\n", "        'keyword_count': len(analysis_data['keywords']),\n", "        'top_keywords': ', '.join([kw[0] for kw in analysis_data['keywords'][:5]]),\n", "        'measurements_sample': ', '.join(analysis_data['measurements'][:5]),\n", "        'drawing_number': metadata.get('cad_metadata', {}).get('drawing_number', ''),\n", "        'revision': metadata.get('cad_metadata', {}).get('revision', ''),\n", "        'page_count': metadata.get('page_count', 0),\n", "        'file_size_mb': metadata.get('file_size_bytes', 0) / (1024 * 1024) if metadata.get('file_size_bytes') else 0\n", "    }\n", "    \n", "    analysis_records.append(record)\n", "\n", "analysis_df = pd.DataFrame(analysis_records)\n", "\n", "print(f\"Created analysis DataFrame with {len(analysis_df)} rows and {len(analysis_df.columns)} columns\")\n", "\n", "# Display sample of analysis results\n", "if not analysis_df.empty:\n", "    print(\"\\nSample analysis results:\")\n", "    display(analysis_df.head(3))\n", "    \n", "    print(\"\\nAnalysis statistics:\")\n", "    print(f\"  Average text length: {analysis_df['text_length'].mean():.0f} characters\")\n", "    print(f\"  Files with tables: {analysis_df['table_count'].gt(0).sum()}\")\n", "    print(f\"  Files with numeric tables: {analysis_df['has_numeric_tables'].sum()}\")\n", "    print(f\"  Files with measurements: {analysis_df['measurement_count'].gt(0).sum()}\")\n", "else:\n", "    print(\"No analysis data available\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export results for downstream processing\n", "print(\"\\nEXPORTING ANALYSIS RESULTS...\")\n", "\n", "# Store results in global variables\n", "globals()['pdf_content_analysis'] = analysis_results\n", "globals()['pdf_analysis_dataframe'] = analysis_df\n", "globals()['pdf_document_categories'] = dict(document_categories)\n", "globals()['pdf_all_keywords'] = dict(all_keywords)\n", "globals()['pdf_all_measurements'] = all_measurements\n", "\n", "# Save analysis results to files\n", "output_dir = Path('../../../data/cad')\n", "\n", "# Save analysis DataFrame\n", "analysis_csv_path = output_dir / 'cad_pdf_content_analysis.csv'\n", "try:\n", "    analysis_df.to_csv(analysis_csv_path, index=False)\n", "    print(f\"Analysis DataFrame saved to: {analysis_csv_path}\")\n", "except Exception as e:\n", "    logger.warning(f\"Could not save analysis CSV: {e}\")\n", "\n", "# Save detailed analysis as JSON\n", "analysis_json_path = output_dir / 'cad_pdf_content_analysis_detailed.json'\n", "try:\n", "    analysis_export = {\n", "        'analysis_results': analysis_results,\n", "        'document_categories': dict(document_categories),\n", "        'top_keywords': dict(all_keywords.most_common(50)),\n", "        'measurements': all_measurements,\n", "        'analysis_timestamp': datetime.now().isoformat()\n", "    }\n", "    \n", "    with open(analysis_json_path, 'w') as f:\n", "        json.dump(analysis_export, f, indent=2, default=str)\n", "    print(f\"Detailed analysis saved to: {analysis_json_path}\")\n", "except Exception as e:\n", "    logger.warning(f\"Could not save analysis JSON: {e}\")\n", "\n", "# Create summary report\n", "summary_path = output_dir / 'cad_pdf_analysis_summary.txt'\n", "try:\n", "    with open(summary_path, 'w') as f:\n", "        f.write(\"CAD PDF Content Analysis Summary\\n\")\n", "        f.write(\"=\" * 40 + \"\\n\\n\")\n", "        f.write(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "        f.write(f\"Total Files Analyzed: {len(analysis_results)}\\n\\n\")\n", "        \n", "        f.write(\"Document Categories:\\n\")\n", "        for category, count in document_categories.most_common():\n", "            f.write(f\"  {category}: {count} documents\\n\")\n", "        \n", "        f.write(\"\\nTop Keywords:\\n\")\n", "        for keyword, count in all_keywords.most_common(20):\n", "            f.write(f\"  {keyword}: {count} occurrences\\n\")\n", "        \n", "        f.write(f\"\\nMeasurements Found: {len(all_measurements)} total, {len(set(all_measurements))} unique\\n\")\n", "    \n", "    print(f\"Summary report saved to: {summary_path}\")\n", "except Exception as e:\n", "    logger.warning(f\"Could not save summary report: {e}\")\n", "\n", "print(f\"\\nResults exported to variables:\")\n", "print(f\"  - pdf_content_analysis: {len(analysis_results)} file analyses\")\n", "print(f\"  - pdf_analysis_dataframe: DataFrame with {len(analysis_df)} rows\")\n", "print(f\"  - pdf_document_categories: {len(document_categories)} categories\")\n", "print(f\"  - pdf_all_keywords: {len(all_keywords)} unique keywords\")\n", "\n", "logger.info(\"PDF content analysis results exported successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"CAD PDF CONTENT ANALYSIS COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if analysis_results:\n", "    print(f\"Successfully analyzed {len(analysis_results)} PDF files\")\n", "    print(f\"Identified {len(document_categories)} document categories\")\n", "    print(f\"Extracted {len(all_keywords)} unique keywords\")\n", "    print(f\"Found {len(all_measurements)} measurements\")\n", "    print(f\"Generated comprehensive analysis dataset\")\n", "    print(\"\\nCAD PDF processing pipeline completed successfully!\")\n", "    print(\"All stages: Discovery → Text/Table Extraction → Metadata Extraction → Content Analysis\")\n", "else:\n", "    print(\"No content analysis was performed\")\n", "    print(\"Please check previous pipeline stages\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"CAD PDF content analysis notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}