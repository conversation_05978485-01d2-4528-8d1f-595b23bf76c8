{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD PDF Discovery\n", "\n", "This notebook recursively discovers all PDF files within the `data/cad/` directory, including any nested subdirectories.\n", "\n", "**Stage**: Preprocessing - CAD Pipeline  \n", "**Input Data**: PDF files in data/cad/ directory  \n", "**Output**: List of discovered PDF file paths with summary counts  \n", "**Format**: Python list and summary statistics  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Recursively discover all PDF files within the `data/cad/` directory structure to establish a comprehensive inventory of CAD-related PDF documents for subsequent processing steps in the modular pipeline."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Use `pathlib.Path().rglob(\"*.pdf\")` for reliable cross-platform file discovery\n", "2. Implement basic error handling for unreadable files or invalid extensions\n", "3. Generate summary statistics including file counts by subdirectory\n", "4. Output full file paths for downstream processing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import os\n", "from pathlib import Path\n", "from collections import defaultdict\n", "from datetime import datetime\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"CAD PDF Discovery - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths\n", "project_root = Path('../../../')  # Adjust to project root from notebooks/preprocessing/cad/\n", "cad_data_path = project_root / 'data' / 'cad'\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"CAD data path: {cad_data_path.resolve()}\")\n", "\n", "# Verify the CAD data directory exists\n", "if not cad_data_path.exists():\n", "    logger.error(f\"CAD data directory does not exist: {cad_data_path}\")\n", "    raise FileNotFoundError(f\"Directory not found: {cad_data_path}\")\n", "elif not cad_data_path.is_dir():\n", "    logger.error(f\"CAD data path is not a directory: {cad_data_path}\")\n", "    raise NotADirectoryError(f\"Path is not a directory: {cad_data_path}\")\n", "else:\n", "    logger.info(f\"CAD data directory verified: {cad_data_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize containers for discovered files\n", "discovered_pdfs = []\n", "error_files = []\n", "subdirectory_counts = defaultdict(int)\n", "\n", "print(\"Starting PDF discovery...\")\n", "logger.info(\"Beginning recursive PDF file discovery\")\n", "\n", "try:\n", "    # Use rglob for recursive discovery of PDF files\n", "    pdf_pattern = \"*.pdf\"\n", "    pdf_files = cad_data_path.rglob(pdf_pattern)\n", "    \n", "    for pdf_file in pdf_files:\n", "        try:\n", "            # Verify file is readable and has correct extension\n", "            if pdf_file.is_file() and pdf_file.suffix.lower() == '.pdf':\n", "                # Check if file is readable\n", "                if os.access(pdf_file, os.R_OK):\n", "                    discovered_pdfs.append(pdf_file)\n", "                    \n", "                    # Count files by subdirectory\n", "                    relative_path = pdf_file.relative_to(cad_data_path)\n", "                    subdirectory = relative_path.parts[0] if len(relative_path.parts) > 1 else 'root'\n", "                    subdirectory_counts[subdirectory] += 1\n", "                    \n", "                    logger.debug(f\"Discovered PDF: {pdf_file}\")\n", "                else:\n", "                    error_files.append((pdf_file, \"File not readable\"))\n", "                    logger.warning(f\"PDF file not readable: {pdf_file}\")\n", "            else:\n", "                error_files.append((pdf_file, \"Invalid file type or not a file\"))\n", "                logger.warning(f\"Invalid PDF file: {pdf_file}\")\n", "                \n", "        except Exception as e:\n", "            error_files.append((pdf_file, str(e)))\n", "            logger.error(f\"Error processing file {pdf_file}: {e}\")\n", "            \n", "except Exception as e:\n", "    logger.error(f\"Error during PDF discovery: {e}\")\n", "    raise\n", "\n", "logger.info(f\"PDF discovery completed. Found {len(discovered_pdfs)} valid PDF files\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display summary results\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF DISCOVERY SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nTotal PDF files discovered: {len(discovered_pdfs)}\")\n", "print(f\"Files with errors: {len(error_files)}\")\n", "\n", "if subdirectory_counts:\n", "    print(\"\\nFiles by subdirectory:\")\n", "    for subdirectory, count in sorted(subdirectory_counts.items()):\n", "        print(f\"  {subdirectory}: {count} files\")\n", "\n", "if error_files:\n", "    print(\"\\nFiles with errors:\")\n", "    for error_file, error_msg in error_files:\n", "        print(f\"  {error_file}: {error_msg}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display detailed file listing\n", "if discovered_pdfs:\n", "    print(\"\\nDISCOVERED PDF FILES:\")\n", "    print(\"-\" * 40)\n", "    \n", "    for i, pdf_file in enumerate(discovered_pdfs, 1):\n", "        # Show relative path from cad directory for cleaner output\n", "        relative_path = pdf_file.relative_to(cad_data_path)\n", "        file_size = pdf_file.stat().st_size\n", "        file_size_mb = file_size / (1024 * 1024)  # Convert to MB\n", "        \n", "        print(f\"{i:3d}. {relative_path} ({file_size_mb:.2f} MB)\")\n", "        \n", "        # Log full path for debugging if needed\n", "        logger.debug(f\"Full path {i}: {pdf_file}\")\n", "else:\n", "    print(\"\\nNo PDF files were discovered in the CAD data directory.\")\n", "    logger.warning(\"No PDF files found - this may indicate an issue with the data directory structure\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export results for downstream processing\n", "print(\"\\nEXPORTING RESULTS...\")\n", "\n", "# Create a list of full file paths as strings for easy serialization\n", "pdf_file_paths = [str(pdf_file) for pdf_file in discovered_pdfs]\n", "\n", "# Store results in variables for use by subsequent notebooks\n", "globals()['discovered_pdf_files'] = pdf_file_paths\n", "globals()['pdf_discovery_summary'] = {\n", "    'total_files': len(discovered_pdfs),\n", "    'error_files': len(error_files),\n", "    'subdirectory_counts': dict(subdirectory_counts),\n", "    'discovery_timestamp': datetime.now().isoformat()\n", "}\n", "\n", "print(f\"Results exported to variables:\")\n", "print(f\"  - discovered_pdf_files: {len(pdf_file_paths)} file paths\")\n", "print(f\"  - pdf_discovery_summary: Summary statistics\")\n", "\n", "logger.info(\"PDF discovery results exported successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF DISCOVERY COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if discovered_pdfs:\n", "    print(f\"Successfully discovered {len(discovered_pdfs)} PDF files\")\n", "    print(\"Ready for next stage of CAD PDF processing pipeline\")\n", "else:\n", "    print(\"No PDF files discovered - please verify data directory structure\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"PDF discovery notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}