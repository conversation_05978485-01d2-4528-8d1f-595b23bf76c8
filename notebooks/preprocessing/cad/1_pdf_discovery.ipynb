{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD PDF Discovery\n", "\n", "This notebook recursively discovers all PDF files within the `data/cad/` directory, including any nested subdirectories.\n", "\n", "**Stage**: Preprocessing - CAD Pipeline  \n", "**Input Data**: PDF files in data/cad/ directory  \n", "**Output**: List of discovered PDF file paths with summary counts  \n", "**Format**: Python list and summary statistics  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Recursively discover all PDF files within the `data/cad/` directory structure to establish a comprehensive inventory of CAD-related PDF documents for subsequent processing steps in the modular pipeline."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Use `pathlib.Path().rglob(\"*.pdf\")` for reliable cross-platform file discovery\n", "2. Implement basic error handling for unreadable files or invalid extensions\n", "3. Generate summary statistics including file counts by subdirectory\n", "4. Output full file paths for downstream processing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CAD PDF Discovery - Starting...\n", "Timestamp: 2025-06-17 12:15:53\n"]}], "source": ["import logging\n", "import os\n", "from pathlib import Path\n", "from collections import defaultdict\n", "from datetime import datetime\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"CAD PDF Discovery - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:15:53,428 - INFO - CAD data directory verified: ../../../data/cad\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Project root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis\n", "CAD data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/cad\n"]}], "source": ["# Define paths\n", "project_root = Path('../../../')  # Adjust to project root from notebooks/preprocessing/cad/\n", "cad_data_path = project_root / 'data' / 'cad'\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"CAD data path: {cad_data_path.resolve()}\")\n", "\n", "# Verify the CAD data directory exists\n", "if not cad_data_path.exists():\n", "    logger.error(f\"CAD data directory does not exist: {cad_data_path}\")\n", "    raise FileNotFoundError(f\"Directory not found: {cad_data_path}\")\n", "elif not cad_data_path.is_dir():\n", "    logger.error(f\"CAD data path is not a directory: {cad_data_path}\")\n", "    raise NotADirectoryError(f\"Path is not a directory: {cad_data_path}\")\n", "else:\n", "    logger.info(f\"CAD data directory verified: {cad_data_path}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:15:53,434 - INFO - Beginning recursive PDF file discovery\n", "2025-06-17 12:15:53,438 - INFO - PDF discovery completed. Found 33 valid PDF files\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Starting PDF discovery...\n"]}], "source": ["# Initialize containers for discovered files\n", "discovered_pdfs = []\n", "error_files = []\n", "subdirectory_counts = defaultdict(int)\n", "\n", "print(\"Starting PDF discovery...\")\n", "logger.info(\"Beginning recursive PDF file discovery\")\n", "\n", "try:\n", "    # Use rglob for recursive discovery of PDF files\n", "    pdf_pattern = \"*.pdf\"\n", "    pdf_files = cad_data_path.rglob(pdf_pattern)\n", "    \n", "    for pdf_file in pdf_files:\n", "        try:\n", "            # Verify file is readable and has correct extension\n", "            if pdf_file.is_file() and pdf_file.suffix.lower() == '.pdf':\n", "                # Check if file is readable\n", "                if os.access(pdf_file, os.R_OK):\n", "                    discovered_pdfs.append(pdf_file)\n", "                    \n", "                    # Count files by subdirectory\n", "                    relative_path = pdf_file.relative_to(cad_data_path)\n", "                    subdirectory = relative_path.parts[0] if len(relative_path.parts) > 1 else 'root'\n", "                    subdirectory_counts[subdirectory] += 1\n", "                    \n", "                    logger.debug(f\"Discovered PDF: {pdf_file}\")\n", "                else:\n", "                    error_files.append((pdf_file, \"File not readable\"))\n", "                    logger.warning(f\"PDF file not readable: {pdf_file}\")\n", "            else:\n", "                error_files.append((pdf_file, \"Invalid file type or not a file\"))\n", "                logger.warning(f\"Invalid PDF file: {pdf_file}\")\n", "                \n", "        except Exception as e:\n", "            error_files.append((pdf_file, str(e)))\n", "            logger.error(f\"Error processing file {pdf_file}: {e}\")\n", "            \n", "except Exception as e:\n", "    logger.error(f\"Error during PDF discovery: {e}\")\n", "    raise\n", "\n", "logger.info(f\"PDF discovery completed. Found {len(discovered_pdfs)} valid PDF files\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "PDF DISCOVERY SUMMARY\n", "============================================================\n", "\n", "Total PDF files discovered: 33\n", "Files with errors: 0\n", "\n", "Files by subdirectory:\n", "  castro: 26 files\n", "  mccarthy: 5 files\n", "  rpcs: 2 files\n", "\n", "============================================================\n"]}], "source": ["# Display summary results\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF DISCOVERY SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nTotal PDF files discovered: {len(discovered_pdfs)}\")\n", "print(f\"Files with errors: {len(error_files)}\")\n", "\n", "if subdirectory_counts:\n", "    print(\"\\nFiles by subdirectory:\")\n", "    for subdirectory, count in sorted(subdirectory_counts.items()):\n", "        print(f\"  {subdirectory}: {count} files\")\n", "\n", "if error_files:\n", "    print(\"\\nFiles with errors:\")\n", "    for error_file, error_msg in error_files:\n", "        print(f\"  {error_file}: {error_msg}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DISCOVERED PDF FILES:\n", "----------------------------------------\n", "  1. rpcs/ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf (1.49 MB)\n", "  2. rpcs/Forum_AltheaI&II_CA_NXH_ENG_L3_03-11-2025_Rev 4.pdf (12.89 MB)\n", "  3. castro/GRE.EEC.D.00.IT.P.12645.00.118.00_Inquadramento Layout di impianto/GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf (15.36 MB)\n", "  4. castro/GRE.EEC.D.00.IT.P.12645.00.165.00 - Plans and Details Access Road and Platform/GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.pdf (6.58 MB)\n", "  5. castro/GRE.EEC.D.00.IT.P.12645.00.172.00_Outside Fencing and Gates/GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.pdf (2.63 MB)\n", "  6. castro/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks.pdf (7.46 MB)\n", "  7. castro/GRE.EEC.D.00.IT.P.12645.00.114.00_Inquadramento catastale impianto/GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.pdf (1.15 MB)\n", "  8. castro/GRE.EEC.D.00.IT.P.12645.00.116.00_Schema Elettrico Unifilare/GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf (1.51 MB)\n", "  9. castro/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.134.00.pdf (2.59 MB)\n", " 10. castro/OneDrive_2025-02-19/03. TRACKER/Plinto Area - Alzamento a 700mm.pdf (1.33 MB)\n", " 11. castro/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.327.00.pdf (0.79 MB)\n", " 12. castro/OneDrive_2025-02-19/03. TRACKER/Plinto - Alzamento a 700mm.pdf (0.10 MB)\n", " 13. castro/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.322.00.pdf (6.32 MB)\n", " 14. castro/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00.pdf (5.49 MB)\n", " 15. castro/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.pdf (2.22 MB)\n", " 16. castro/GRE.EEC.D.00.IT.P.12645.00.123.00_Generale - Posizione Cabina di consegna/GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.pdf (1.13 MB)\n", " 17. castro/GRE.EEC.D.00.IT.P.12645.00.160.00_Corografia con punto di connessione/GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.pdf (3.59 MB)\n", " 18. castro/GRE.EEC.D.00.IT.P.12645.00.115.00_Sezioni cavidotti impianto/GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.pdf (12.52 MB)\n", " 19. castro/GRE.EEC.D.00.IT.P.12645.00.253.00_Earthing System Layout/GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.pdf (22.69 MB)\n", " 20. castro/GRE.EEC.D.00.IT.P.12645.00.191.00 - TC Single Line Diagram/GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.pdf (0.44 MB)\n", " 21. castro/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps.pdf (33.69 MB)\n", " 22. castro/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Post.pdf (1.86 MB)\n", " 23. castro/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Ante.pdf (3.20 MB)\n", " 24. castro/GRE.EEC.D.00.IT.P.12645.00.249.00_Electrical Layout/GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.pdf (86.04 MB)\n", " 25. castro/GRE.EEC.D.00.IT.P.12645.00.119.00 - General Layout with Meteo Station and Sensors Placement/GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.pdf (4.05 MB)\n", " 26. castro/GRE.EEC.D.00.IT.P.12645.00.121.00_Configurazione del Parco Fotovoltaico/GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.pdf (1.71 MB)\n", " 27. castro/GRE.EEC.R.00.IT.P.12645.00.256.00_Table of All electrical cables/GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.pdf (2.44 MB)\n", " 28. castro/GRE.EEC.D.00.IT.P.12645.00.169.00_General Layout and Detail of Cabins Foundations/GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.pdf (3.95 MB)\n", " 29. mccarthy/PB01_-_POWERBLOCK_PLAN.pdf (3.59 MB)\n", " 30. mccarthy/S-501_ FSLR S6+ Pier Tolerances  Rev.2 markup (1).pdf (0.61 MB)\n", " 31. mccarthy/PB03_-_POWERBLOCK_PLAN.pdf (3.69 MB)\n", " 32. mccarthy/S-203_ PIER PLAN BLOCK--3 Rev.2.pdf (0.78 MB)\n", " 33. mccarthy/PB02_-_POWERBLOCK_PLAN.pdf (3.56 MB)\n"]}], "source": ["# Display detailed file listing\n", "if discovered_pdfs:\n", "    print(\"\\nDISCOVERED PDF FILES:\")\n", "    print(\"-\" * 40)\n", "    \n", "    for i, pdf_file in enumerate(discovered_pdfs, 1):\n", "        # Show relative path from cad directory for cleaner output\n", "        relative_path = pdf_file.relative_to(cad_data_path)\n", "        file_size = pdf_file.stat().st_size\n", "        file_size_mb = file_size / (1024 * 1024)  # Convert to MB\n", "        \n", "        print(f\"{i:3d}. {relative_path} ({file_size_mb:.2f} MB)\")\n", "        \n", "        # Log full path for debugging if needed\n", "        logger.debug(f\"Full path {i}: {pdf_file}\")\n", "else:\n", "    print(\"\\nNo PDF files were discovered in the CAD data directory.\")\n", "    logger.warning(\"No PDF files found - this may indicate an issue with the data directory structure\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:15:53,453 - INFO - PDF discovery results exported successfully\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "EXPORTING RESULTS...\n", "Results exported to variables:\n", "  - discovered_pdf_files: 33 file paths\n", "  - pdf_discovery_summary: Summary statistics\n"]}], "source": ["# Export results for downstream processing\n", "print(\"\\nEXPORTING RESULTS...\")\n", "\n", "# Create a list of full file paths as strings for easy serialization\n", "pdf_file_paths = [str(pdf_file) for pdf_file in discovered_pdfs]\n", "\n", "# Store results in variables for use by subsequent notebooks\n", "globals()['discovered_pdf_files'] = pdf_file_paths\n", "globals()['pdf_discovery_summary'] = {\n", "    'total_files': len(discovered_pdfs),\n", "    'error_files': len(error_files),\n", "    'subdirectory_counts': dict(subdirectory_counts),\n", "    'discovery_timestamp': datetime.now().isoformat()\n", "}\n", "\n", "print(f\"Results exported to variables:\")\n", "print(f\"  - discovered_pdf_files: {len(pdf_file_paths)} file paths\")\n", "print(f\"  - pdf_discovery_summary: Summary statistics\")\n", "\n", "logger.info(\"PDF discovery results exported successfully\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 12:15:53,457 - INFO - PDF discovery notebook execution completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "PDF DISCOVERY COMPLETED\n", "============================================================\n", "Successfully discovered 33 PDF files\n", "Ready for next stage of CAD PDF processing pipeline\n", "\n", "Completion time: 2025-06-17 12:15:53\n"]}], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF DISCOVERY COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if discovered_pdfs:\n", "    print(f\"Successfully discovered {len(discovered_pdfs)} PDF files\")\n", "    print(\"Ready for next stage of CAD PDF processing pipeline\")\n", "else:\n", "    print(\"No PDF files discovered - please verify data directory structure\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"PDF discovery notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}