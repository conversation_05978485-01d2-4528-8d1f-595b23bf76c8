{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📊 Metadata Integration Guide\n", "\n", "This guide shows how to use extracted metadata from IFC/CAD files throughout your drone photogrammetry analysis workflow.\n", "\n", "**Metadata Sources:**\n", "- `extract_ifc_metadata.ipynb` → IFC pile metadata (coordinates, types, properties)\n", "- `extract_cad_metadata.ipynb` → CAD block metadata (coordinates, layers, geometry)\n", "\n", "**Integration Points:**\n", "1. Post-IFC preprocessing (quality assessment, spatial validation)\n", "2. Alignment workflow (reference points, validation)\n", "3. Detection workflows (ground truth, validation)\n", "4. Analysis and reporting (comparison, accuracy metrics)\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** December 2024"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import cdist\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Metadata Integration Guide - Ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Understanding Your Metadata\n", "\n", "First, let's understand what metadata is available from each source."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# IFC Metadata Structure (from extract_ifc_metadata.ipynb)\n", "ifc_metadata_structure = {\n", "    'columns': [\n", "        'Pile No.',        # Unique pile identifier (e.g., \"TRPL_Tracker Pile:952577\")\n", "        'Tracker No.',     # Tracker identifier\n", "        'Pile Type',       # Type of pile (e.g., \"COLUMN\")\n", "        'x', 'y', 'z',    # Local coordinates in IFC coordinate system\n", "        'Longitude',       # Geographic longitude (WGS84)\n", "        'Latitude'         # Geographic latitude (WGS84)\n", "    ],\n", "    'coordinate_systems': ['IFC Local', 'Geographic (WGS84)'],\n", "    'typical_count': '14,460 elements',\n", "    'use_cases': [\n", "        'Spatial validation of point clouds',\n", "        'Ground truth for pile detection',\n", "        'Alignment reference points',\n", "        'Geographic positioning'\n", "    ]\n", "}\n", "\n", "# CAD Metadata Structure (from extract_cad_metadata.ipynb)\n", "cad_metadata_structure = {\n", "    'columns': [\n", "        'Handle',          # Unique CAD entity handle\n", "        'Pile No.',        # Extracted pile identifier\n", "        'X_Local', 'Y_Local', 'Z_Local',  # Local CAD coordinates\n", "        'Rotation',        # Rotation angle\n", "        'Scale_X', 'Scale_Y', 'Scale_Z',  # Scale factors\n", "        'Layer',           # CAD layer name\n", "        'Block Name',      # Block/symbol name\n", "        'Block_Width', 'Block_Height',     # Geometric properties\n", "        'Block_Area'       # Calculated area\n", "    ],\n", "    'coordinate_systems': ['CAD Local'],\n", "    'typical_count': 'Variable (depends on CAD file)',\n", "    'use_cases': [\n", "        'Design vs. as-built comparison',\n", "        'Geometric validation',\n", "        'Layer-based filtering',\n", "        'Block type classification'\n", "    ]\n", "}\n", "\n", "print(\"=== IFC Metadata Structure ===\")\n", "for key, value in ifc_metadata_structure.items():\n", "    print(f\"{key}: {value}\")\n", "\n", "print(\"\\n=== CAD Metadata Structure ===\")\n", "for key, value in cad_metadata_structure.items():\n", "    print(f\"{key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Metadata Loading and Validation\n", "\n", "Functions to load and validate metadata from extraction notebooks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_and_validate_metadata(metadata_path, metadata_type='ifc'):\n", "    \"\"\"\n", "    Load and validate metadata from extraction notebooks.\n", "    \n", "    Parameters:\n", "    -----------\n", "    metadata_path : str\n", "        Path to metadata CSV file\n", "    metadata_type : str\n", "        Type of metadata ('ifc' or 'cad')\n", "        \n", "    Returns:\n", "    --------\n", "    metadata_df : pandas.DataFrame\n", "        Validated metadata\n", "    validation_report : dict\n", "        Validation results\n", "    \"\"\"\n", "    try:\n", "        # Load metadata\n", "        metadata_df = pd.read_csv(metadata_path)\n", "        \n", "        # Validation based on metadata type\n", "        validation_report = {\n", "            'file_path': metadata_path,\n", "            'metadata_type': metadata_type,\n", "            'total_records': len(metadata_df),\n", "            'columns_found': list(metadata_df.columns),\n", "            'validation_errors': [],\n", "            'validation_warnings': [],\n", "            'coordinate_info': {},\n", "            'data_quality': {}\n", "        }\n", "        \n", "        if metadata_type == 'ifc':\n", "            # Validate IFC metadata\n", "            required_cols = ['<PERSON>le No.', 'x', 'y', 'z']\n", "            optional_cols = ['Longitude', 'Latitude', 'Pile Type', 'Tracker No.']\n", "            \n", "            # Check required columns\n", "            missing_required = [col for col in required_cols if col not in metadata_df.columns]\n", "            if missing_required:\n", "                validation_report['validation_errors'].append(f\"Missing required columns: {missing_required}\")\n", "            \n", "            # Check optional columns\n", "            missing_optional = [col for col in optional_cols if col not in metadata_df.columns]\n", "            if missing_optional:\n", "                validation_report['validation_warnings'].append(f\"Missing optional columns: {missing_optional}\")\n", "            \n", "            # Coordinate validation\n", "            if all(col in metadata_df.columns for col in ['x', 'y', 'z']):\n", "                validation_report['coordinate_info'] = {\n", "                    'local_bounds': {\n", "                        'x_range': [metadata_df['x'].min(), metadata_df['x'].max()],\n", "                        'y_range': [metadata_df['y'].min(), metadata_df['y'].max()],\n", "                        'z_range': [metadata_df['z'].min(), metadata_df['z'].max()]\n", "                    },\n", "                    'has_geographic': 'Longitude' in metadata_df.columns and 'Latitude' in metadata_df.columns\n", "                }\n", "                \n", "                if validation_report['coordinate_info']['has_geographic']:\n", "                    validation_report['coordinate_info']['geographic_bounds'] = {\n", "                        'lon_range': [metadata_df['Longitude'].min(), metadata_df['Longitude'].max()],\n", "                        'lat_range': [metadata_df['Latitude'].min(), metadata_df['Latitude'].max()]\n", "                    }\n", "        \n", "        elif metadata_type == 'cad':\n", "            # Validate CAD metadata\n", "            required_cols = ['Pile No.', 'X_Local', 'Y_Local', 'Z_Local']\n", "            optional_cols = ['Handle', 'Layer', 'Block Name', 'Rotation']\n", "            \n", "            # Check required columns\n", "            missing_required = [col for col in required_cols if col not in metadata_df.columns]\n", "            if missing_required:\n", "                validation_report['validation_errors'].append(f\"Missing required columns: {missing_required}\")\n", "            \n", "            # Check optional columns\n", "            missing_optional = [col for col in optional_cols if col not in metadata_df.columns]\n", "            if missing_optional:\n", "                validation_report['validation_warnings'].append(f\"Missing optional columns: {missing_optional}\")\n", "            \n", "            # Coordinate validation\n", "            if all(col in metadata_df.columns for col in ['X_Local', 'Y_Local', 'Z_Local']):\n", "                validation_report['coordinate_info'] = {\n", "                    'local_bounds': {\n", "                        'x_range': [metadata_df['X_Local'].min(), metadata_df['X_Local'].max()],\n", "                        'y_range': [metadata_df['Y_Local'].min(), metadata_df['Y_Local'].max()],\n", "                        'z_range': [metadata_df['Z_Local'].min(), metadata_df['Z_Local'].max()]\n", "                    },\n", "                    'has_geographic': 'Longitude' in metadata_df.columns and 'Latitude' in metadata_df.columns\n", "                }\n", "        \n", "        # Data quality checks\n", "        validation_report['data_quality'] = {\n", "            'duplicate_pile_numbers': metadata_df['Pile No.'].duplicated().sum(),\n", "            'missing_coordinates': metadata_df.isnull().sum().to_dict(),\n", "            'unique_pile_count': metadata_df['Pile No.'].nunique()\n", "        }\n", "        \n", "        # Overall validation status\n", "        validation_report['is_valid'] = len(validation_report['validation_errors']) == 0\n", "        \n", "        return metadata_df, validation_report\n", "        \n", "    except Exception as e:\n", "        error_report = {\n", "            'file_path': metadata_path,\n", "            'metadata_type': metadata_type,\n", "            'validation_errors': [f\"Failed to load file: {str(e)}\"],\n", "            'is_valid': False\n", "        }\n", "        return None, error_report\n", "\n", "def print_validation_report(validation_report):\n", "    \"\"\"\n", "    Print a formatted validation report.\n", "    \"\"\"\n", "    print(f\"=== Metadata Validation Report ===\")\n", "    print(f\"File: {validation_report['file_path']}\")\n", "    print(f\"Type: {validation_report['metadata_type'].upper()}\")\n", "    print(f\"Records: {validation_report.get('total_records', 'N/A')}\")\n", "    print(f\"Valid: {'✅ Yes' if validation_report['is_valid'] else '❌ No'}\")\n", "    \n", "    if validation_report['validation_errors']:\n", "        print(\"\\n🚨 Errors:\")\n", "        for error in validation_report['validation_errors']:\n", "            print(f\"  - {error}\")\n", "    \n", "    if validation_report['validation_warnings']:\n", "        print(\"\\n⚠️ Warnings:\")\n", "        for warning in validation_report['validation_warnings']:\n", "            print(f\"  - {warning}\")\n", "    \n", "    if 'coordinate_info' in validation_report and validation_report['coordinate_info']:\n", "        coord_info = validation_report['coordinate_info']\n", "        print(\"\\n📍 Coordinate Info:\")\n", "        if 'local_bounds' in coord_info:\n", "            bounds = coord_info['local_bounds']\n", "            print(f\"  Local bounds: X{bounds['x_range']}, Y{bounds['y_range']}, Z{bounds['z_range']}\")\n", "        if coord_info.get('has_geographic'):\n", "            geo_bounds = coord_info.get('geographic_bounds', {})\n", "            print(f\"  Geographic: Lon{geo_bounds.get('lon_range', 'N/A')}, Lat{geo_bounds.get('lat_range', 'N/A')}\")\n", "    \n", "    if 'data_quality' in validation_report:\n", "        quality = validation_report['data_quality']\n", "        print(\"\\n📊 Data Quality:\")\n", "        print(f\"  Unique piles: {quality.get('unique_pile_count', 'N/A')}\")\n", "        print(f\"  Duplicates: {quality.get('duplicate_pile_numbers', 'N/A')}\")\n", "    \n", "    print(\"=\" * 40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Integration with Alignment Workflow\n", "\n", "Use metadata to improve alignment accuracy and validation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_reference_points_from_metadata(metadata_df, metadata_type='ifc', \n", "                                        sample_ratio=0.1, min_points=10, max_points=100):\n", "    \"\"\"\n", "    Create reference points from metadata for alignment validation.\n", "    \n", "    Parameters:\n", "    -----------\n", "    metadata_df : pandas.DataFrame\n", "        Metadata from IFC/CAD extraction\n", "    metadata_type : str\n", "        Type of metadata ('ifc' or 'cad')\n", "    sample_ratio : float\n", "        Ratio of metadata points to use as references\n", "    min_points : int\n", "        Minimum number of reference points\n", "    max_points : int\n", "        Maximum number of reference points\n", "        \n", "    Returns:\n", "    --------\n", "    reference_points : numpy.ndarray\n", "        Reference point coordinates (N, 3)\n", "    reference_metadata : pandas.DataFrame\n", "        Metadata for reference points\n", "    \"\"\"\n", "    # Extract coordinates based on metadata type\n", "    if metadata_type == 'ifc':\n", "        coord_cols = ['x', 'y', 'z']\n", "    elif metadata_type == 'cad':\n", "        coord_cols = ['X_Local', 'Y_Local', 'Z_Local']\n", "    else:\n", "        raise ValueError(f\"Unknown metadata type: {metadata_type}\")\n", "    \n", "    # Check if coordinate columns exist\n", "    if not all(col in metadata_df.columns for col in coord_cols):\n", "        missing = [col for col in coord_cols if col not in metadata_df.columns]\n", "        raise ValueError(f\"Missing coordinate columns: {missing}\")\n", "    \n", "    # Calculate number of reference points\n", "    total_points = len(metadata_df)\n", "    num_ref_points = int(total_points * sample_ratio)\n", "    num_ref_points = max(min_points, min(num_ref_points, max_points))\n", "    num_ref_points = min(num_ref_points, total_points)  # Can't exceed available points\n", "    \n", "    # Sample reference points (stratified sampling for better distribution)\n", "    if num_ref_points >= total_points:\n", "        # Use all points\n", "        reference_indices = metadata_df.index.tolist()\n", "    else:\n", "        # Stratified sampling based on spatial distribution\n", "        coords = metadata_df[coord_cols].values\n", "        \n", "        # Simple grid-based stratification\n", "        x_min, x_max = coords[:, 0].min(), coords[:, 0].max()\n", "        y_min, y_max = coords[:, 1].min(), coords[:, 1].max()\n", "        \n", "        # Create grid\n", "        grid_size = int(np.sqrt(num_ref_points)) + 1\n", "        x_bins = np.linspace(x_min, x_max, grid_size + 1)\n", "        y_bins = np.linspace(y_min, y_max, grid_size + 1)\n", "        \n", "        reference_indices = []\n", "        \n", "        for i in range(grid_size):\n", "            for j in range(grid_size):\n", "                # Find points in this grid cell\n", "                mask = ((coords[:, 0] >= x_bins[i]) & (coords[:, 0] < x_bins[i+1]) &\n", "                       (coords[:, 1] >= y_bins[j]) & (coords[:, 1] < y_bins[j+1]))\n", "                \n", "                cell_indices = metadata_df.index[mask].tolist()\n", "                \n", "                if cell_indices:\n", "                    # Randomly select one point from this cell\n", "                    selected_idx = np.random.choice(cell_indices)\n", "                    reference_indices.append(selected_idx)\n", "                    \n", "                    if len(reference_indices) >= num_ref_points:\n", "                        break\n", "            \n", "            if len(reference_indices) >= num_ref_points:\n", "                break\n", "        \n", "        # If we don't have enough points, add random ones\n", "        if len(reference_indices) < num_ref_points:\n", "            remaining_indices = [idx for idx in metadata_df.index if idx not in reference_indices]\n", "            additional_needed = num_ref_points - len(reference_indices)\n", "            additional_indices = np.random.choice(remaining_indices, \n", "                                                size=min(additional_needed, len(remaining_indices)), \n", "                                                replace=False)\n", "            reference_indices.extend(additional_indices)\n", "    \n", "    # Extract reference data\n", "    reference_metadata = metadata_df.iloc[reference_indices].copy()\n", "    reference_points = reference_metadata[coord_cols].values\n", "    \n", "    print(f\"Created {len(reference_points)} reference points from {total_points} metadata elements\")\n", "    print(f\"Sampling ratio: {len(reference_points)/total_points:.3f}\")\n", "    \n", "    return reference_points, reference_metadata\n", "\n", "def validate_alignment_with_metadata(aligned_points, reference_points, reference_metadata,\n", "                                   tolerance=2.0):\n", "    \"\"\"\n", "    Validate alignment results using metadata reference points.\n", "    \n", "    Parameters:\n", "    -----------\n", "    aligned_points : numpy.n<PERSON><PERSON>\n", "        Aligned point cloud (N, 3)\n", "    reference_points : numpy.ndarray\n", "        Reference points from metadata (M, 3)\n", "    reference_metadata : pandas.DataFrame\n", "        Metadata for reference points\n", "    tolerance : float\n", "        Distance tolerance for validation (meters)\n", "        \n", "    Returns:\n", "    --------\n", "    validation_results : dict\n", "        Alignment validation results\n", "    \"\"\"\n", "    # Find nearest neighbors between aligned points and reference points\n", "    tree = cKDTree(aligned_points)\n", "    distances, indices = tree.query(reference_points)\n", "    \n", "    # Calculate validation metrics\n", "    within_tolerance = distances <= tolerance\n", "    validation_rate = np.sum(within_tolerance) / len(reference_points)\n", "    \n", "    validation_results = {\n", "        'total_reference_points': len(reference_points),\n", "        'points_within_tolerance': np.sum(within_tolerance),\n", "        'validation_rate': validation_rate,\n", "        'mean_distance': np.mean(distances),\n", "        'median_distance': np.median(distances),\n", "        'max_distance': np.max(distances),\n", "        'std_distance': np.std(distances),\n", "        'tolerance_used': tolerance,\n", "        'distances': distances,\n", "        'within_tolerance_mask': within_tolerance\n", "    }\n", "    \n", "    print(f\"Alignment Validation Results:\")\n", "    print(f\"- Reference points: {validation_results['total_reference_points']}\")\n", "    print(f\"- Within tolerance ({tolerance}m): {validation_results['points_within_tolerance']} ({validation_rate*100:.1f}%)\")\n", "    print(f\"- Mean distance: {validation_results['mean_distance']:.3f}m\")\n", "    print(f\"- Median distance: {validation_results['median_distance']:.3f}m\")\n", "    print(f\"- Max distance: {validation_results['max_distance']:.3f}m\")\n", "    \n", "    return validation_results"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}