#!/usr/bin/env python3
"""
Standardized metadata schema for CAD and IFC extraction.

This module defines the unified data schema used across both CAD and IFC
metadata extraction workflows to ensure consistent downstream processing.
"""

import pandas as pd
import numpy as np
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path

@dataclass
class ElementMetadata:
    """
    Standardized metadata structure for construction elements (piles, columns, etc.)
    """
    # Identification
    element_id: str  # Unique identifier (handle, GUID, etc.)
    element_name: str  # Human-readable name
    element_type: str  # Type classification (pile, column, beam, etc.)
    source_file: str  # Source file name
    source_type: str  # "CAD" or "IFC"
    
    # Spatial coordinates (local)
    x_local: float
    y_local: float
    z_local: float
    
    # Spatial coordinates (geographic, if transformed)
    longitude: Optional[float] = None
    latitude: Optional[float] = None
    elevation: Optional[float] = None
    
    # Geometric properties
    rotation: Optional[float] = None  # Rotation angle in degrees
    scale_x: Optional[float] = None
    scale_y: Optional[float] = None
    scale_z: Optional[float] = None
    
    # Dimensional properties
    width: Optional[float] = None  # Element width
    height: Optional[float] = None  # Element height
    depth: Optional[float] = None  # Element depth/length
    diameter: Optional[float] = None  # For circular elements
    area: Optional[float] = None  # Cross-sectional area
    volume: Optional[float] = None  # Element volume
    
    # Material properties
    material_name: Optional[str] = None
    material_type: Optional[str] = None
    material_grade: Optional[str] = None
    
    # Structural properties
    load_bearing: Optional[bool] = None
    structural_function: Optional[str] = None
    
    # Layer/Level information
    layer_name: Optional[str] = None
    level_name: Optional[str] = None
    building_story: Optional[str] = None
    
    # Additional attributes (flexible key-value pairs)
    attributes: Optional[Dict[str, Any]] = None
    
    # Metadata
    extraction_timestamp: Optional[str] = None
    coordinate_system: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for DataFrame creation."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ElementMetadata':
        """Create instance from dictionary."""
        return cls(**data)

class MetadataSchema:
    """
    Schema manager for metadata extraction workflows.
    """
    
    # Standard column names for CSV export
    REQUIRED_COLUMNS = [
        'element_id', 'element_name', 'element_type', 'source_file', 'source_type',
        'x_local', 'y_local', 'z_local'
    ]
    
    OPTIONAL_COLUMNS = [
        'longitude', 'latitude', 'elevation',
        'rotation', 'scale_x', 'scale_y', 'scale_z',
        'width', 'height', 'depth', 'diameter', 'area', 'volume',
        'material_name', 'material_type', 'material_grade',
        'load_bearing', 'structural_function',
        'layer_name', 'level_name', 'building_story',
        'extraction_timestamp', 'coordinate_system'
    ]
    
    ALL_COLUMNS = REQUIRED_COLUMNS + OPTIONAL_COLUMNS
    
    @staticmethod
    def create_empty_dataframe() -> pd.DataFrame:
        """Create an empty DataFrame with the standard schema."""
        return pd.DataFrame(columns=MetadataSchema.ALL_COLUMNS)
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame) -> bool:
        """Validate that DataFrame contains required columns."""
        missing_columns = set(MetadataSchema.REQUIRED_COLUMNS) - set(df.columns)
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        return True
    
    @staticmethod
    def standardize_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """Standardize DataFrame to match schema."""
        # Add missing columns with default values
        for col in MetadataSchema.ALL_COLUMNS:
            if col not in df.columns:
                if col in ['x_local', 'y_local', 'z_local']:
                    df[col] = 0.0
                elif col in ['element_id', 'element_name', 'element_type', 'source_file', 'source_type']:
                    df[col] = ''
                else:
                    df[col] = None
        
        # Reorder columns to match schema
        df = df[MetadataSchema.ALL_COLUMNS]
        
        # Set appropriate data types
        numeric_columns = [
            'x_local', 'y_local', 'z_local', 'longitude', 'latitude', 'elevation',
            'rotation', 'scale_x', 'scale_y', 'scale_z',
            'width', 'height', 'depth', 'diameter', 'area', 'volume'
        ]
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df

class MetadataExporter:
    """
    Unified exporter for metadata in multiple formats.
    """
    
    def __init__(self, output_path: str, site_name: str, source_type: str):
        self.output_path = Path(output_path)
        self.site_name = site_name
        self.source_type = source_type.lower()
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def export_csv(self, df: pd.DataFrame, filename: Optional[str] = None) -> str:
        """Export DataFrame to CSV format."""
        if filename is None:
            filename = f"{self.site_name}_{self.source_type}_metadata.csv"
        
        csv_path = self.output_path / filename
        df.to_csv(csv_path, index=False)
        print(f"CSV exported: {csv_path}")
        return str(csv_path)
    
    def export_json(self, data: Dict[str, Any], filename: Optional[str] = None) -> str:
        """Export hierarchical metadata to JSON format."""
        if filename is None:
            filename = f"{self.site_name}_{self.source_type}_metadata.json"
        
        json_path = self.output_path / filename
        with open(json_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        print(f"JSON exported: {json_path}")
        return str(json_path)
    
    def export_parquet(self, df: pd.DataFrame, filename: Optional[str] = None) -> str:
        """Export DataFrame to Parquet format."""
        try:
            import pyarrow as pa
            import pyarrow.parquet as pq
        except ImportError:
            print("Parquet export requires pyarrow. Skipping.")
            return ""
        
        if filename is None:
            filename = f"{self.site_name}_{self.source_type}_metadata.parquet"
        
        parquet_path = self.output_path / filename
        df.to_parquet(parquet_path, index=False)
        print(f"Parquet exported: {parquet_path}")
        return str(parquet_path)
    
    def export_all_formats(self, df: pd.DataFrame, metadata: Dict[str, Any]) -> Dict[str, str]:
        """Export data in all available formats."""
        # Standardize DataFrame
        df = MetadataSchema.standardize_dataframe(df)
        
        # Export files
        exported_files = {}
        
        # CSV export
        csv_path = self.export_csv(df)
        exported_files['csv'] = csv_path
        
        # JSON export
        json_metadata = {
            'extraction_info': {
                'site_name': self.site_name,
                'source_type': self.source_type,
                'timestamp': self.timestamp,
                'total_elements': len(df)
            },
            'schema_version': '1.0',
            'metadata': metadata,
            'summary_statistics': {
                'element_types': df['element_type'].value_counts().to_dict(),
                'coordinate_bounds': {
                    'x_min': float(df['x_local'].min()),
                    'x_max': float(df['x_local'].max()),
                    'y_min': float(df['y_local'].min()),
                    'y_max': float(df['y_local'].max()),
                    'z_min': float(df['z_local'].min()),
                    'z_max': float(df['z_local'].max())
                } if len(df) > 0 else {}
            }
        }
        
        json_path = self.export_json(json_metadata)
        exported_files['json'] = json_path
        
        # Parquet export (if available)
        try:
            parquet_path = self.export_parquet(df)
            if parquet_path:
                exported_files['parquet'] = parquet_path
        except Exception as e:
            print(f"Parquet export failed: {e}")
        
        return exported_files

# Utility functions for common operations
def create_element_from_cad_insert(insert_entity, source_file: str, **kwargs) -> ElementMetadata:
    """Create ElementMetadata from CAD INSERT entity."""
    return ElementMetadata(
        element_id=getattr(insert_entity.dxf, 'handle', ''),
        element_name=getattr(insert_entity.dxf, 'name', ''),
        element_type='pile',  # Default, can be overridden
        source_file=source_file,
        source_type='CAD',
        x_local=insert_entity.dxf.insert[0],
        y_local=insert_entity.dxf.insert[1],
        z_local=insert_entity.dxf.insert[2],
        rotation=getattr(insert_entity.dxf, 'rotation', None),
        scale_x=getattr(insert_entity.dxf, 'xscale', None),
        scale_y=getattr(insert_entity.dxf, 'yscale', None),
        scale_z=getattr(insert_entity.dxf, 'zscale', None),
        layer_name=getattr(insert_entity.dxf, 'layer', None),
        extraction_timestamp=datetime.now().isoformat(),
        **kwargs
    )

def create_element_from_ifc_element(ifc_element, source_file: str, **kwargs) -> ElementMetadata:
    """Create ElementMetadata from IFC element."""
    # Extract coordinates (simplified - would need proper IFC geometry processing)
    placement = getattr(ifc_element, 'ObjectPlacement', None)
    x, y, z = 0.0, 0.0, 0.0
    
    if placement and hasattr(placement, 'RelativePlacement'):
        location = placement.RelativePlacement.Location
        if hasattr(location, 'Coordinates'):
            coords = location.Coordinates
            x, y, z = coords[0], coords[1], coords[2] if len(coords) > 2 else 0.0
    
    return ElementMetadata(
        element_id=getattr(ifc_element, 'GlobalId', ''),
        element_name=getattr(ifc_element, 'Name', ''),
        element_type=ifc_element.is_a(),
        source_file=source_file,
        source_type='IFC',
        x_local=x,
        y_local=y,
        z_local=z,
        extraction_timestamp=datetime.now().isoformat(),
        **kwargs
    )
