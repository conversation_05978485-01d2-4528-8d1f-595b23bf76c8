{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation - Progressive Morphological Filter (PMF)\n", "\n", "This notebook implements PMF-based ground segmentation for point cloud processing.\n", "\n", "**Method**: Progressive Morphological Filter  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground-removed / non-ground point cloud  \n", "**Format**: .ply (recommended for compatibility with Open3D + visualization), .pcd (preferred for PCL + ML pipelines)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## PMF Algorithm:\n", "- Based on <PERSON> et al. (2003) algorithm\n", "- Uses morphological operations on rasterized point cloud\n", "- Progressive window size increase\n", "- Best for complex terrain with vegetation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy import ndimage"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Castro\"  # Site name for output file naming\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "point_cloud_path = \"\"  # Path to input point cloud file\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "# PMF-specific parameters\n", "cell_size = 1.0  # Grid cell size for rasterization (meters)\n", "max_window_size = 33  # Maximum window size for morphological operations\n", "slope = 0.15  # Slope parameter for terrain (radians)\n", "max_distance = 2.5  # Maximum distance threshold (meters)\n", "initial_distance = 0.5  # Initial distance threshold (meters)\n", "height_threshold_ratio = 0.1  # Bottom percentage of height range to consider"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 12:03:11,192 - INFO - Created current run output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/ground_segmentation/output_runs/Castro_pmf_20250616_120311\n", "2025-06-16 12:03:11,193 - INFO - Using default input path: ../../data/ENEL/Castro/raw\n", "2025-06-16 12:03:11,194 - INFO - Input path exists: True\n", "2025-06-16 12:03:11,195 - INFO - Ground segmentation output path exists: True\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Input path: ../../data/ENEL/Castro/raw\n"]}], "source": ["# Set up paths with proper project organization\n", "base_path = Path('../..')\n", "data_path = base_path / 'data'\n", "logger.info(f\"Checking base data path: {data_path}, Exists: {data_path.exists()}\")\n", "\n", "# Create output directory structure for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_pmf_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# DEBUG: Confirm output run path creation\n", "logger.info(f\"Created current run output path: {current_run_path.resolve()}\")\n", "\n", "# Input and output paths following the specified organization\n", "if point_cloud_path:\n", "    input_path = Path(point_cloud_path)\n", "    logger.info(f\"Custom point_cloud_path provided: {input_path}\")\n", "    if not input_path.exists():\n", "        raise FileNotFoundError(f\"Input path does not exist: {input_path}\")\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "    logger.info(f\"Using default input path: {input_path}\")\n", "\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# DEBUG: Confirm paths exist\n", "logger.info(f\"Input path exists: {input_path.exists()}\")\n", "logger.info(f\"Ground segmentation output path exists: {ground_seg_path.exists()}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 12:03:18,971 - INFO - Reading LAS file: ../../data/ENEL/Castro/raw/area4_point.las\n", "2025-06-16 12:03:50,883 - INFO - Loaded 251056301 points.\n"]}], "source": ["# ## Load Point Cloud (.las)\n", "try:\n", "    import laspy\n", "except ImportError as e:\n", "    raise ImportError(\"laspy is not installed. Please install it via `pip install laspy`.\") from e\n", "\n", "las_files = list(input_path.glob(\"*.las\")) + list(input_path.glob(\"*.laz\"))\n", "if not las_files:\n", "    raise FileNotFoundError(f\"No LAS/LAZ files found in {input_path}\")\n", "\n", "las_file = las_files[0]\n", "logger.info(f\"Reading LAS file: {las_file}\")\n", "las = laspy.read(str(las_file))\n", "\n", "# Safe attribute access\n", "x = las.x\n", "y = las.y\n", "z = las.z\n", "\n", "# Defensive check\n", "if len(x) == 0 or len(y) == 0 or len(z) == 0:\n", "    raise ValueError(\"One or more coordinate arrays are empty.\")\n", "\n", "points = np.vstack((x, y, z)).T\n", "logger.info(f\"Loaded {points.shape[0]} points.\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ground points: 147551106\n", "Non-ground points: 103505195\n"]}], "source": ["from scipy.ndimage import grey_erosion, grey_dilation\n", "\n", "# Grid the point cloud (2D raster)\n", "min_xy = np.min(points[:, :2], axis=0)\n", "max_xy = np.max(points[:, :2], axis=0)\n", "dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)\n", "grid = np.full(dims, np.nan)\n", "\n", "# Populate raster with lowest Z value per cell\n", "for x, y, z in points:\n", "    xi = int((x - min_xy[0]) / cell_size)\n", "    yi = int((y - min_xy[1]) / cell_size)\n", "    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "        if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:\n", "            grid[xi, yi] = z\n", "\n", "# Fill holes\n", "filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)\n", "\n", "# Morphological opening (erosion then dilation)\n", "opened = grey_dilation(grey_erosion(filled_grid, size=max_window_size), size=max_window_size)\n", "\n", "# Ground mask based on slope threshold\n", "z_diff = filled_grid - opened\n", "ground_mask_2d = z_diff < slope\n", "\n", "# Reconstruct full ground point mask\n", "ground_mask = []\n", "for x, y, z in points:\n", "    xi = int((x - min_xy[0]) / cell_size)\n", "    yi = int((y - min_xy[1]) / cell_size)\n", "    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "        if ground_mask_2d[xi, yi]:\n", "            ground_mask.append(True)\n", "        else:\n", "            ground_mask.append(False)\n", "ground_mask = np.array(ground_mask)\n", "\n", "ground_points = points[ground_mask]\n", "nonground_points = points[~ground_mask]\n", "\n", "print(f\"Ground points: {ground_points.shape[0]}\")\n", "print(f\"Non-ground points: {nonground_points.shape[0]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ## Save Output .PLY\n", "import open3d as o3d\n", "\n", "def save_ply(path, points_array):\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "save_ply(ground_seg_path / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(ground_seg_path / f\"{site_name}_nonground.ply\", nonground_points)\n", "#save_ply(current_run_path / f\"{site_name}_ground.ply\", ground_points)\n", "#save_ply(current_run_path / f\"{site_name}_nonground.ply\", nonground_points)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 12:13:22,105 - INFO - Ground Ratio: 0.5877\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Ground Ratio: 0.5877\n"]}], "source": ["# Ground/Non-Ground ratio\n", "ground_ratio = ground_points.shape[0] / (ground_points.shape[0] + nonground_points.shape[0])\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "print(f\"Ground Ratio: {ground_ratio:.4f}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ground_z_mean 55.54223537046901\n", "nonground_z_mean 56.302448423376134\n", "z_separation 0.760213052907126\n"]}], "source": ["ground_z_mean = ground_points[:, 2].mean()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "print(\"ground_z_mean\", ground_z_mean)\n", "print(\"nonground_z_mean\", nonground_z_mean)\n", "print(\"z_separation\", z_separation)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bounding Box Sizes (X, Y, Z):\n", "  Ground:     [219.886 191.999   8.523]\n", "  Non-Ground: [229.823 201.301  17.416]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "print(\"Bounding Box Sizes (X, Y, Z):\")\n", "print(f\"  Ground:     {ground_bbox}\")\n", "print(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PMF Ground Segmentation - Ready!\n", "Data path: ../../data\n", "Project: ENEL/Castro\n", "Input path: ../../data/ENEL/Castro/raw\n", "Output path: ../../data/ENEL/Castro/ground_segmentation\n", "Current run output: output_runs/Castro_pmf_20250616_120311\n", "PMF Parameters: cell_size=1.0m, max_window=33, slope=0.15\n"]}], "source": ["# Final readiness print\n", "# Save parameters to JSON for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"PMF\",\n", "        \"notebook_version\": \"1.1\"\n", "    },\n", "    \"pmf_parameters\": {\n", "        \"cell_size\": cell_size,\n", "        \"max_window_size\": max_window_size,\n", "        \"slope\": slope,\n", "        \"max_distance\": max_distance,\n", "        \"initial_distance\": initial_distance,\n", "        \"height_threshold_ratio\": height_threshold_ratio\n", "    },\n", "    \"processing_parameters\": {\n", "        \"buffer_radius\": buffer_radius\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(current_run_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = current_run_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {params_file}\")\n", "\n", "print(\"PMF Ground Segmentation - Ready!\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {ground_seg_path}\")\n", "print(f\"Current run output: {current_run_path}\")\n", "print(f\"PMF Parameters: cell_size={cell_size}m, max_window={max_window_size}, slope={slope}\")"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}