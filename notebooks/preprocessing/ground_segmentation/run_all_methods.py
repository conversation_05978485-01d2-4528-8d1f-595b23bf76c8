#!/usr/bin/env python3
"""
Master script to run all ground segmentation methods per project site using Papermill.

This script executes RANSAC, PMF, and CSF ground segmentation notebooks
for specified project sites with customizable parameters.
"""

import papermill as pm
import pandas as pd
from datetime import datetime
from pathlib import Path
import json
import argparse

# Project site configurations
SITE_CONFIGS = {
    "ENEL": {
        "Trino": {
            "point_cloud_path": "data/ENEL/Trino/raw/",
            "buffer_radius": 50.0,
            "terrain_type": "flat"  # flat, hilly, complex
        },
        "Castro": {
            "point_cloud_path": "data/ENEL/Castro/raw/",
            "buffer_radius": 75.0,
            "terrain_type": "hilly"
        },
        "Mudjar": {
            "point_cloud_path": "data/ENEL/Mudjar/raw/",
            "buffer_radius": 60.0,
            "terrain_type": "complex"
        },
        "Giorgio": {
            "point_cloud_path": "data/ENEL/Giorgio/raw/",
            "buffer_radius": 45.0,
            "terrain_type": "flat"
        }
    },
    "USA": {
        "McCarthy": {
            "point_cloud_path": "data/USA/McCarthy/raw/",
            "buffer_radius": 80.0,
            "terrain_type": "hilly"
        },
        "RPCS": {
            "point_cloud_path": "data/USA/RPCS/raw/",
            "buffer_radius": 55.0,
            "terrain_type": "flat"
        },
        "RES": {
            "point_cloud_path": "data/USA/RES/raw/",
            "buffer_radius": 65.0,
            "terrain_type": "complex"
        }
    }
}

# Method-specific parameter configurations based on terrain type
METHOD_CONFIGS = {
    "RANSAC": {
        "flat": {
            "distance_threshold": 0.15,
            "num_iterations": 1000,
            "min_inliers_ratio": 0.05,
            "early_stop_ratio": 0.6
        },
        "hilly": {
            "distance_threshold": 0.25,
            "num_iterations": 1500,
            "min_inliers_ratio": 0.03,
            "early_stop_ratio": 0.5
        },
        "complex": {
            "distance_threshold": 0.3,
            "num_iterations": 2000,
            "min_inliers_ratio": 0.02,
            "early_stop_ratio": 0.4
        }
    },
    "PMF": {
        "flat": {
            "cell_size": 1.0,
            "max_window_size": 33,
            "slope": 0.1,
            "height_threshold_ratio": 0.05
        },
        "hilly": {
            "cell_size": 0.8,
            "max_window_size": 45,
            "slope": 0.2,
            "height_threshold_ratio": 0.1
        },
        "complex": {
            "cell_size": 0.5,
            "max_window_size": 65,
            "slope": 0.3,
            "height_threshold_ratio": 0.15
        }
    },
    "CSF": {
        "flat": {
            "cloth_resolution": 0.5,
            "classification_threshold": 0.3,
            "rigidness": 3,
            "max_iterations": 300
        },
        "hilly": {
            "cloth_resolution": 0.3,
            "classification_threshold": 0.4,
            "rigidness": 2,
            "max_iterations": 500
        },
        "complex": {
            "cloth_resolution": 0.2,
            "classification_threshold": 0.5,
            "rigidness": 1,
            "max_iterations": 800
        }
    }
}

def run_method_for_site(method, project_type, site_name, site_config, method_config):
    """
    Run a specific ground segmentation method for a site.
    
    Parameters:
    -----------
    method : str
        Ground segmentation method ("RANSAC", "PMF", "CSF")
    project_type : str
        Project type ("ENEL", "USA")
    site_name : str
        Site name
    site_config : dict
        Site-specific configuration
    method_config : dict
        Method-specific configuration
        
    Returns:
    --------
    str : Path to output notebook
    """
    
    # Create timestamp for this run
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Define input and output paths
    method_lower = method.lower()
    input_notebook = f"ground_segmentation_{method_lower}.ipynb"
    output_notebook = f"output_runs/{site_name}_{method_lower}_{timestamp}_executed.ipynb"
    
    # Ensure output directory exists
    Path("output_runs").mkdir(exist_ok=True)
    
    # Base parameters
    parameters = {
        'site_name': site_name,
        'project_type': project_type,
        'point_cloud_path': site_config['point_cloud_path'],
        'buffer_radius': site_config['buffer_radius']
    }
    
    # Add method-specific parameters
    parameters.update(method_config)
    
    print(f"Executing {method} for {project_type}/{site_name}")
    print(f"Terrain type: {site_config['terrain_type']}")
    print(f"Output notebook: {output_notebook}")
    
    try:
        # Execute notebook with parameters
        pm.execute_notebook(
            input_notebook,
            output_notebook,
            parameters=parameters,
            log_output=True
        )
        
        print(f"✓ {method} execution completed for {site_name}")
        return output_notebook
        
    except Exception as e:
        print(f"✗ {method} execution failed for {site_name}: {e}")
        return None

def run_all_methods_for_site(project_type, site_name, methods=None):
    """
    Run all ground segmentation methods for a specific site.
    
    Parameters:
    -----------
    project_type : str
        Project type ("ENEL", "USA")
    site_name : str
        Site name
    methods : list, optional
        List of methods to run. If None, runs all methods.
        
    Returns:
    --------
    dict : Results summary
    """
    
    if methods is None:
        methods = ["RANSAC", "PMF", "CSF"]
    
    if project_type not in SITE_CONFIGS:
        raise ValueError(f"Unknown project type: {project_type}")
    
    if site_name not in SITE_CONFIGS[project_type]:
        raise ValueError(f"Unknown site: {site_name} for project {project_type}")
    
    site_config = SITE_CONFIGS[project_type][site_name]
    terrain_type = site_config['terrain_type']
    
    results = {
        'site': f"{project_type}/{site_name}",
        'terrain_type': terrain_type,
        'timestamp': datetime.now().isoformat(),
        'methods': {}
    }
    
    print(f"\n=== Running all methods for {project_type}/{site_name} ===")
    print(f"Terrain type: {terrain_type}")
    print(f"Methods: {', '.join(methods)}")
    
    for method in methods:
        method_config = METHOD_CONFIGS[method][terrain_type]
        
        output_notebook = run_method_for_site(
            method, project_type, site_name, site_config, method_config
        )
        
        results['methods'][method] = {
            'output_notebook': output_notebook,
            'parameters': method_config,
            'success': output_notebook is not None
        }
    
    return results

def run_all_sites(project_types=None, methods=None):
    """
    Run ground segmentation for all configured sites.
    
    Parameters:
    -----------
    project_types : list, optional
        List of project types to process. If None, processes all.
    methods : list, optional
        List of methods to run. If None, runs all methods.
        
    Returns:
    --------
    dict : Complete results summary
    """
    
    if project_types is None:
        project_types = list(SITE_CONFIGS.keys())
    
    if methods is None:
        methods = ["RANSAC", "PMF", "CSF"]
    
    all_results = {
        'execution_timestamp': datetime.now().isoformat(),
        'methods_executed': methods,
        'sites': {}
    }
    
    for project_type in project_types:
        if project_type not in SITE_CONFIGS:
            print(f"Warning: Unknown project type {project_type}, skipping")
            continue
            
        for site_name in SITE_CONFIGS[project_type]:
            try:
                site_results = run_all_methods_for_site(project_type, site_name, methods)
                all_results['sites'][f"{project_type}/{site_name}"] = site_results
            except Exception as e:
                print(f"Error processing {project_type}/{site_name}: {e}")
                all_results['sites'][f"{project_type}/{site_name}"] = {
                    'error': str(e),
                    'success': False
                }
    
    # Save results summary
    results_file = f"output_runs/execution_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n=== Execution Summary ===")
    print(f"Results saved to: {results_file}")
    
    return all_results

def main():
    """Command line interface for the ground segmentation runner."""
    
    parser = argparse.ArgumentParser(description='Run ground segmentation methods for project sites')
    parser.add_argument('--project-type', choices=['ENEL', 'USA'], help='Project type to process')
    parser.add_argument('--site-name', help='Specific site to process')
    parser.add_argument('--methods', nargs='+', choices=['RANSAC', 'PMF', 'CSF'], 
                       help='Methods to run (default: all)')
    parser.add_argument('--all-sites', action='store_true', help='Run for all configured sites')
    
    args = parser.parse_args()
    
    if args.all_sites:
        # Run for all sites
        project_types = [args.project_type] if args.project_type else None
        run_all_sites(project_types, args.methods)
        
    elif args.project_type and args.site_name:
        # Run for specific site
        run_all_methods_for_site(args.project_type, args.site_name, args.methods)
        
    else:
        # Interactive mode - show available options
        print("Available project sites:")
        for project_type, sites in SITE_CONFIGS.items():
            print(f"\n{project_type}:")
            for site_name, config in sites.items():
                print(f"  - {site_name} (terrain: {config['terrain_type']})")
        
        print("\nUsage examples:")
        print("  python run_all_methods.py --all-sites")
        print("  python run_all_methods.py --project-type ENEL --site-name Trino")
        print("  python run_all_methods.py --project-type USA --site-name McCarthy --methods RANSAC PMF")

if __name__ == "__main__":
    main()
