#!/usr/bin/env python3
"""
Compare ground segmentation results across different methods.

This script analyzes and compares the results from RANSAC, PMF, and CSF
ground segmentation methods for the same site.
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import numpy as np
from datetime import datetime

def load_metadata(metadata_path):
    """Load metadata from a ground segmentation run."""
    with open(metadata_path, 'r') as f:
        return json.load(f)

def find_site_runs(site_name, output_dir="output_runs"):
    """
    Find all runs for a specific site across different methods.
    
    Parameters:
    -----------
    site_name : str
        Name of the site to analyze
    output_dir : str
        Directory containing output runs
        
    Returns:
    --------
    dict : Dictionary of method -> list of run directories
    """
    
    output_path = Path(output_dir)
    site_runs = {
        'RANSAC': [],
        'PMF': [],
        'CSF': []
    }
    
    # Find all directories for this site
    for run_dir in output_path.glob(f"{site_name}_*"):
        if run_dir.is_dir():
            dir_name = run_dir.name
            
            if '_ransac_' in dir_name:
                site_runs['RANSAC'].append(run_dir)
            elif '_pmf_' in dir_name:
                site_runs['PMF'].append(run_dir)
            elif '_csf_' in dir_name:
                site_runs['CSF'].append(run_dir)
    
    return site_runs

def extract_run_metrics(run_dir):
    """
    Extract key metrics from a run directory.
    
    Parameters:
    -----------
    run_dir : Path
        Path to run directory
        
    Returns:
    --------
    dict : Dictionary of extracted metrics
    """
    
    # Find metadata file
    metadata_files = list(run_dir.glob("*_metadata.json"))
    if not metadata_files:
        return None
    
    metadata = load_metadata(metadata_files[0])
    
    # Extract key metrics
    metrics = {
        'run_dir': run_dir.name,
        'timestamp': metadata['project_info']['segmentation_timestamp'],
        'method': metadata['parameters'].get('segmentation_method', 'Unknown'),
        'total_points': metadata['input_data']['total_points'],
        'ground_points': metadata['segmentation_results']['ground_points'],
        'non_ground_points': metadata['segmentation_results']['non_ground_points'],
        'ground_ratio': metadata['segmentation_results']['ground_ratio'],
        'non_ground_ratio': metadata['segmentation_results']['non_ground_ratio'],
        'has_colors': metadata['input_data']['has_colors']
    }
    
    # Add method-specific parameters
    params = metadata['parameters']
    if 'distance_threshold' in params:  # RANSAC
        metrics.update({
            'distance_threshold': params['distance_threshold'],
            'num_iterations': params['num_iterations'],
            'min_inliers_ratio': params['min_inliers_ratio']
        })
    elif 'cell_size' in params:  # PMF
        metrics.update({
            'cell_size': params['cell_size'],
            'max_window_size': params['max_window_size'],
            'slope': params['slope']
        })
    elif 'cloth_resolution' in params:  # CSF
        metrics.update({
            'cloth_resolution': params['cloth_resolution'],
            'classification_threshold': params['classification_threshold'],
            'rigidness': params.get('rigidness', 'N/A')
        })
    
    return metrics

def compare_site_methods(site_name, output_dir="output_runs"):
    """
    Compare all methods for a specific site.
    
    Parameters:
    -----------
    site_name : str
        Name of the site to analyze
    output_dir : str
        Directory containing output runs
        
    Returns:
    --------
    pd.DataFrame : Comparison results
    """
    
    site_runs = find_site_runs(site_name, output_dir)
    all_metrics = []
    
    for method, run_dirs in site_runs.items():
        for run_dir in run_dirs:
            metrics = extract_run_metrics(run_dir)
            if metrics:
                metrics['method'] = method
                all_metrics.append(metrics)
    
    if not all_metrics:
        print(f"No runs found for site: {site_name}")
        return None
    
    df = pd.DataFrame(all_metrics)
    return df

def plot_method_comparison(df, site_name):
    """
    Create comparison plots for different methods.
    
    Parameters:
    -----------
    df : pd.DataFrame
        Comparison results dataframe
    site_name : str
        Name of the site
    """
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'Ground Segmentation Method Comparison - {site_name}', fontsize=16)
    
    # Ground ratio comparison
    axes[0, 0].bar(df['method'], df['ground_ratio'])
    axes[0, 0].set_title('Ground Point Ratio by Method')
    axes[0, 0].set_ylabel('Ground Ratio')
    axes[0, 0].set_ylim(0, 1)
    
    # Total points processed
    axes[0, 1].bar(df['method'], df['total_points'])
    axes[0, 1].set_title('Total Points Processed')
    axes[0, 1].set_ylabel('Point Count')
    
    # Ground vs Non-ground points
    methods = df['method'].unique()
    x = np.arange(len(methods))
    width = 0.35
    
    ground_counts = [df[df['method'] == m]['ground_points'].iloc[0] for m in methods]
    non_ground_counts = [df[df['method'] == m]['non_ground_points'].iloc[0] for m in methods]
    
    axes[1, 0].bar(x - width/2, ground_counts, width, label='Ground Points', alpha=0.8)
    axes[1, 0].bar(x + width/2, non_ground_counts, width, label='Non-Ground Points', alpha=0.8)
    axes[1, 0].set_title('Point Distribution by Method')
    axes[1, 0].set_ylabel('Point Count')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(methods)
    axes[1, 0].legend()
    
    # Method parameters (if available)
    param_text = ""
    for _, row in df.iterrows():
        method = row['method']
        if method == 'RANSAC' and 'distance_threshold' in row:
            param_text += f"{method}: threshold={row['distance_threshold']:.2f}m, iter={row['num_iterations']}\n"
        elif method == 'PMF' and 'cell_size' in row:
            param_text += f"{method}: cell_size={row['cell_size']:.1f}m, window={row['max_window_size']}\n"
        elif method == 'CSF' and 'cloth_resolution' in row:
            param_text += f"{method}: resolution={row['cloth_resolution']:.1f}m, threshold={row['classification_threshold']:.1f}m\n"
    
    axes[1, 1].text(0.1, 0.9, param_text, transform=axes[1, 1].transAxes, 
                    verticalalignment='top', fontfamily='monospace')
    axes[1, 1].set_title('Method Parameters')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    
    # Save plot
    output_path = Path(output_dir) / f"{site_name}_method_comparison.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Comparison plot saved to: {output_path}")
    
    plt.show()

def generate_comparison_report(site_name, output_dir="output_runs"):
    """
    Generate a comprehensive comparison report for a site.
    
    Parameters:
    -----------
    site_name : str
        Name of the site to analyze
    output_dir : str
        Directory containing output runs
    """
    
    print(f"\n=== Ground Segmentation Method Comparison Report ===")
    print(f"Site: {site_name}")
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    df = compare_site_methods(site_name, output_dir)
    
    if df is None or df.empty:
        print(f"No comparison data available for site: {site_name}")
        return
    
    # Summary statistics
    print(f"\nSummary Statistics:")
    print(f"Methods compared: {', '.join(df['method'].unique())}")
    print(f"Total runs analyzed: {len(df)}")
    
    # Method performance comparison
    print(f"\nMethod Performance:")
    for method in df['method'].unique():
        method_data = df[df['method'] == method].iloc[0]
        print(f"\n{method}:")
        print(f"  Ground ratio: {method_data['ground_ratio']:.3f}")
        print(f"  Ground points: {method_data['ground_points']:,}")
        print(f"  Non-ground points: {method_data['non_ground_points']:,}")
        print(f"  Total points: {method_data['total_points']:,}")
    
    # Recommendations
    print(f"\nRecommendations:")
    
    # Find method with highest ground ratio
    best_ground_ratio = df.loc[df['ground_ratio'].idxmax()]
    print(f"- Highest ground detection: {best_ground_ratio['method']} ({best_ground_ratio['ground_ratio']:.3f})")
    
    # Find method with most balanced results
    df['balance_score'] = 1 - abs(df['ground_ratio'] - 0.5)  # Closer to 0.5 is more balanced
    most_balanced = df.loc[df['balance_score'].idxmax()]
    print(f"- Most balanced segmentation: {most_balanced['method']} (ratio: {most_balanced['ground_ratio']:.3f})")
    
    # Create visualization
    plot_method_comparison(df, site_name)
    
    # Save detailed results
    results_file = Path(output_dir) / f"{site_name}_comparison_results.csv"
    df.to_csv(results_file, index=False)
    print(f"\nDetailed results saved to: {results_file}")

def main():
    """Main function for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Compare ground segmentation methods')
    parser.add_argument('site_name', help='Name of the site to analyze')
    parser.add_argument('--output-dir', default='output_runs', help='Output directory (default: output_runs)')
    
    args = parser.parse_args()
    
    generate_comparison_report(args.site_name, args.output_dir)

if __name__ == "__main__":
    main()
