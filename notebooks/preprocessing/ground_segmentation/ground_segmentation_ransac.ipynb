{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation using RANSAC Method\n", "\n", "This notebook implements RANSAC-based ground segmentation for point cloud processing in solar array inspection projects.\n", "\n", "**Method**: RANSAC (Random Sample Consensus) Plane Fitting  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground and non-ground point clouds with RANSAC-specific naming  \n", "**Format**: .ply files with method-specific prefixes  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## RANSAC Algorithm Overview\n", "\n", "RANSAC is a robust iterative method for fitting mathematical models to data containing outliers:\n", "\n", "**Strengths:**\n", "- Highly robust to outliers and noise\n", "- Works well on relatively flat terrain\n", "- Fast convergence for simple planar surfaces\n", "- Deterministic results with fixed random seed\n", "\n", "**Limitations:**\n", "- Assumes ground is a single plane\n", "- May struggle with complex terrain variations\n", "- Performance depends on parameter tuning\n", "- Can misclassify low vegetation as ground\n", "\n", "**Best Use Cases:**\n", "- Flat or gently sloping terrain\n", "- Areas with minimal vegetation\n", "- Quick initial ground removal\n", "- Preprocessing for other algorithms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration Parameters\n", "\n", "These parameters control the RANSAC algorithm behavior and can be adjusted via Papermill for different sites and conditions."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Castro\"  # Site name for output file naming\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "point_cloud_path = \"\"  # Path to input point cloud file\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "# RANSAC-specific parameters\n", "distance_threshold = 0.2  # Maximum distance for a point to be considered an inlier (meters)\n", "num_iterations = 1000     # Number of RANSAC iterations to perform\n", "min_inliers_ratio = 0.05  # Minimum ratio of inliers to accept a plane\n", "early_stop_ratio = 0.6    # Ratio of inliers to total points for early stopping\n", "\n", "# Processing parameters\n", "max_points_processing = 1000000  # Maximum points to process (for performance)\n", "random_seed = 42                 # Random seed for reproducible results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Library Imports and Environment Setup\n", "\n", "Import required libraries for point cloud processing, visualization, and RANSAC implementation."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "\n", "# Set random seed for reproducible results\n", "np.random.seed(random_seed)\n", "\n", "print(f\"Libraries imported successfully. Random seed set to {random_seed}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Library Setup\n", "\n", "The RANSAC method requires minimal dependencies compared to other ground segmentation methods. The random seed ensures reproducible results across multiple runs, which is crucial for comparing different methods on the same dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Logging Configuration\n", "\n", "Configure logging to track processing steps and performance metrics."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(f\"RANSAC Ground Segmentation initialized for site: {site_name}\")\n", "logger.info(f\"Parameters - Distance threshold: {distance_threshold}m, Iterations: {num_iterations}\")\n", "\n", "# Save parameters to JSON for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"RANSAC\",\n", "        \"notebook_version\": \"1.1\"\n", "    },\n", "    \"ransac_parameters\": {\n", "        \"distance_threshold\": distance_threshold,\n", "        \"num_iterations\": num_iterations,\n", "        \"min_inliers_ratio\": min_inliers_ratio,\n", "        \"early_stop_ratio\": early_stop_ratio\n", "    },\n", "    \"processing_parameters\": {\n", "        \"max_points_processing\": max_points_processing,\n", "        \"buffer_radius\": buffer_radius,\n", "        \"visualization_enabled\": visualization_enabled\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(current_run_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = current_run_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {params_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Directory Structure and Path Configuration\n", "\n", "Set up input and output paths following the project organization structure."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 12:39:41,337 - INFO - Checking base data path: ../../data, Exists: True\n", "2025-06-16 12:39:41,338 - INFO - Created current run output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/ground_segmentation/output_runs/Castro_ransac_20250616_123941\n", "2025-06-16 12:39:41,338 - INFO - Using default input path: ../../data/ENEL/Castro/raw\n", "2025-06-16 12:39:41,338 - INFO - Input path exists: True\n", "2025-06-16 12:39:41,339 - INFO - Ground segmentation output path exists: True\n"]}], "source": ["# Set up paths with proper project organization\n", "base_path = Path('../..')\n", "data_path = base_path / 'data'\n", "logger.info(f\"Checking base data path: {data_path}, Exists: {data_path.exists()}\")\n", "\n", "# Create output directory structure for this run with method-specific naming\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_ransac_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Created current run output path: {current_run_path.resolve()}\")\n", "\n", "# Input and output paths following the specified organization\n", "if point_cloud_path:\n", "    input_path = Path(point_cloud_path)\n", "    logger.info(f\"Custom point_cloud_path provided: {input_path}\")\n", "    if not input_path.exists():\n", "        raise FileNotFoundError(f\"Input path does not exist: {input_path}\")\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "    logger.info(f\"Using default input path: {input_path}\")\n", "\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Input path exists: {input_path.exists()}\")\n", "logger.info(f\"Ground segmentation output path exists: {ground_seg_path.exists()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Path Configuration\n", "\n", "The RANSAC method uses method-specific output directory naming (ransac) to distinguish results from other ground segmentation methods. This ensures clear separation of outputs when comparing different approaches on the same dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Data Loading\n", "\n", "Load the raw point cloud data from LAS/LAZ files and prepare for RANSAC processing."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 12:39:41,820 - INFO - Reading LAS file: ../../data/ENEL/Castro/raw/area4_point.las\n", "2025-06-16 12:40:12,884 - INFO - Loaded 251056301 points.\n"]}], "source": ["# Load Point Cloud data\n", "try:\n", "    import laspy\n", "except ImportError as e:\n", "    raise ImportError(\"laspy is not installed. Please install it via `pip install laspy`.\") from e\n", "\n", "las_files = list(input_path.glob(\"*.las\")) + list(input_path.glob(\"*.laz\"))\n", "if not las_files:\n", "    raise FileNotFoundError(f\"No LAS/LAZ files found in {input_path}\")\n", "\n", "las_file = las_files[0]\n", "logger.info(f\"Reading LAS file: {las_file}\")\n", "las = laspy.read(str(las_file))\n", "\n", "# Extract coordinate arrays\n", "x = las.x\n", "y = las.y\n", "z = las.z\n", "\n", "# Validate data integrity\n", "if len(x) == 0 or len(y) == 0 or len(z) == 0:\n", "    raise ValueError(\"One or more coordinate arrays are empty.\")\n", "\n", "# Create point array\n", "points = np.vstack((x, y, z)).T\n", "original_point_count = points.shape[0]\n", "logger.info(f\"Loaded {original_point_count:,} points from {las_file.name}\")\n", "\n", "# Display basic statistics\n", "print(f\"Point cloud statistics:\")\n", "print(f\"  Total points: {original_point_count:,}\")\n", "print(f\"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)\")\n", "print(f\"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)\")\n", "print(f\"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Data Loading\n", "\n", "The point cloud has been successfully loaded. The coordinate ranges provide insight into the terrain characteristics that will affect RANSAC performance. Large Z-ranges may indicate complex terrain that could challenge the single-plane assumption of RANSAC."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC Ground Plane Detection\n", "\n", "Execute the core RANSAC algorithm to identify the dominant ground plane in the point cloud."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running RANSAC ground detection...\n", "Parameters: threshold=0.2m, iterations=1000, min_ratio=0.05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["RANSAC iterations: 100%|██████████| 1000/1000 [00:22<00:00, 44.25it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["RANSAC completed in 22.71 seconds\n", "Ground points: 493,597 (0.49 ratio)\n", "Plane: -0.002x + -0.004y + 1.000z + 22395.613 = 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import numpy as np\n", "import time\n", "from tqdm import tqdm\n", "\n", "print(f\"Running RANSAC ground detection...\")\n", "print(f\"Parameters: threshold={distance_threshold}m, iterations={num_iterations}, min_ratio={min_inliers_ratio}\")\n", "\n", "# Downsample first\n", "points = points[np.random.choice(points.shape[0], size=min(1_000_000, points.shape[0]), replace=False)]\n", "\n", "# Recompute point count AFTER downsampling\n", "n_points = points.shape[0]\n", "min_inliers = int(n_points * min_inliers_ratio)\n", "\n", "best_plane_params = None\n", "best_inliers = []\n", "max_inliers = 0\n", "\n", "start_time = time.time()\n", "\n", "for i in tqdm(range(num_iterations), desc=\"RANSAC iterations\"):\n", "    # Randomly sample 3 points\n", "    sample_indices = np.random.choice(n_points, 3, replace=False)\n", "    p1, p2, p3 = points[sample_indices]\n", "\n", "    # Compute plane normal\n", "    v1 = p2 - p1\n", "    v2 = p3 - p1\n", "    normal = np.cross(v1, v2)\n", "\n", "    norm = np.linalg.norm(normal)\n", "    if norm < 1e-6:\n", "        continue  # Skip degenerate planes\n", "\n", "    normal = normal / norm\n", "\n", "    # Enforce upward-facing normal\n", "    if normal[2] < 0:\n", "        normal = -normal\n", "\n", "    # Plane equation: ax + by + cz + d = 0\n", "    d = -np.dot(normal, p1)\n", "    plane_params = np.append(normal, d)\n", "\n", "    # Distance of all points to the plane\n", "    distances = np.abs(np.dot(points, plane_params[:3]) + d)\n", "\n", "    # Find inliers within threshold\n", "    inliers = np.where(distances < distance_threshold)[0]\n", "    n_inliers = len(inliers)\n", "\n", "    if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "        best_plane_params = plane_params\n", "        best_inliers = inliers\n", "        max_inliers = n_inliers\n", "\n", "        inlier_ratio = n_inliers / n_points\n", "        if inlier_ratio > early_stop_ratio:\n", "            print(f\"Early stopping at iteration {i+1}: Found {n_inliers:,} ground points ({inlier_ratio:.2f} ratio)\")\n", "            break\n", "\n", "end_time = time.time()\n", "\n", "if best_plane_params is not None:\n", "    print(f\"RANSAC completed in {end_time - start_time:.2f} seconds\")\n", "    print(f\"Ground points: {max_inliers:,} ({max_inliers / n_points:.2f} ratio)\")\n", "    print(f\"Plane: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0\")\n", "\n", "    ground_points = points[best_inliers]\n", "    nonground_points = np.delete(points, best_inliers, axis=0)\n", "else:\n", "    raise ValueError(\"RANSAC failed to find a valid ground plane.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: RANSAC Ground Detection Results\n", "\n", "The RANSAC algorithm successfully identified a ground plane with approximately 49% of points classified as ground. The plane equation shows a nearly horizontal surface (z-coefficient ≈ 1.0) with minimal slope, which is typical for flat terrain. The processing time of ~23 seconds for 1M points demonstrates RANSAC's computational efficiency. The ground ratio of 0.49 suggests a balanced distribution between ground and non-ground features, indicating the presence of significant above-ground structures."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Output Generation\n", "\n", "Save the segmented ground and non-ground point clouds with RANSAC-specific naming convention."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 14:18:03,579 - INFO - Saved: ../../data/ENEL/Castro/ground_segmentation/Castro_ransac_ground.ply\n", "2025-06-16 14:18:03,609 - INFO - Saved: ../../data/ENEL/Castro/ground_segmentation/Castro_ransac_nonground.ply\n"]}], "source": ["# Save segmented point clouds with method-specific naming\n", "import open3d as o3d\n", "\n", "def save_ply(path, points_array, method_name=\"\"):\n", "    \"\"\"Save point cloud with method-specific naming for comparison.\"\"\"\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save with RANSAC method identifier\n", "method_name = \"ransac\"\n", "save_ply(ground_seg_path / f\"{site_name}_{method_name}_ground.ply\", ground_points, method_name)\n", "save_ply(ground_seg_path / f\"{site_name}_{method_name}_nonground.ply\", nonground_points, method_name)\n", "\n", "# Also save to current run directory for this specific execution\n", "save_ply(current_run_path / f\"{site_name}_{method_name}_ground.ply\", ground_points, method_name)\n", "save_ply(current_run_path / f\"{site_name}_{method_name}_nonground.ply\", nonground_points, method_name)\n", "\n", "print(f\"\\nRANSAC segmentation outputs saved:\")\n", "print(f\"  Ground points: {len(ground_points):,}\")\n", "print(f\"  Non-ground points: {len(nonground_points):,}\")\n", "print(f\"  Method identifier: {method_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Output Generation\n", "\n", "Point clouds have been saved with RANSAC-specific naming (ransac_ground.ply, ransac_nonground.ply) to enable direct comparison with other ground segmentation methods. This naming convention ensures that results from different algorithms can be easily distinguished and analyzed side-by-side."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistical Analysis of Segmentation Results\n", "\n", "Calculate key metrics to evaluate RANSAC segmentation quality and characteristics."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 14:18:03,615 - INFO - Ground Ratio: 0.4936\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Ground Ratio: 0.4936\n"]}], "source": ["# Calculate ground to non-ground ratio\n", "ground_ratio = ground_points.shape[0] / (ground_points.shape[0] + nonground_points.shape[0])\n", "logger.info(f\"RANSAC Ground Ratio: {ground_ratio:.4f}\")\n", "print(f\"RANSAC Ground Ratio: {ground_ratio:.4f}\")\n", "print(f\"\\nRANSAC Segmentation Summary:\")\n", "print(f\"  Total points processed: {ground_points.shape[0] + nonground_points.shape[0]:,}\")\n", "print(f\"  Ground points: {ground_points.shape[0]:,} ({ground_ratio:.1%})\")\n", "print(f\"  Non-ground points: {nonground_points.shape[0]:,} ({1-ground_ratio:.1%})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Statistical Analysis\n", "\n", "The RANSAC method achieved a ground ratio of 49.4%, indicating a balanced segmentation between ground and non-ground features. This ratio is characteristic of areas with significant infrastructure or vegetation coverage above the ground plane."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Elevation Analysis and Vertical Separation\n", "\n", "Analyze the vertical characteristics of ground vs non-ground points to assess segmentation quality."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ground_z_mean 55.95473208102967\n", "nonground_z_mean 55.75878075169381\n", "z_separation -0.19595132933585546\n"]}], "source": ["# Calculate elevation statistics for ground and non-ground points\n", "ground_z_mean = ground_points[:, 2].mean()\n", "ground_z_std = ground_points[:, 2].std()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "nonground_z_std = nonground_points[:, 2].std()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "print(f\"\\nRANSAC Elevation Analysis:\")\n", "print(f\"  Ground elevation - Mean: {ground_z_mean:.3f}m, Std: {ground_z_std:.3f}m\")\n", "print(f\"  Non-ground elevation - Mean: {nonground_z_mean:.3f}m, Std: {nonground_z_std:.3f}m\")\n", "print(f\"  Vertical separation: {z_separation:.3f}m\")\n", "\n", "# Calculate elevation ranges\n", "ground_z_range = ground_points[:, 2].max() - ground_points[:, 2].min()\n", "nonground_z_range = nonground_points[:, 2].max() - nonground_points[:, 2].min()\n", "print(f\"  Ground elevation range: {ground_z_range:.3f}m\")\n", "print(f\"  Non-ground elevation range: {nonground_z_range:.3f}m\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bounding Box Sizes (X, Y, Z):\n", "  Ground:     [223.808 200.084   1.418]\n", "  Non-Ground: [229.737 198.284  17.321]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "print(\"Bounding Box Sizes (X, Y, Z):\")\n", "print(f\"  Ground:     {ground_bbox}\")\n", "print(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RANSAC Ground Segmentation - Ready!\n", "Data path: ../../data\n", "Project: ENEL/Castro\n", "Input path: ../../data/ENEL/Castro/raw\n", "Output path: ../../data/ENEL/Castro/ground_segmentation\n", "Current run output: output_runs/Castro_pmf_20250616_123941\n", "RANSAC Parameters: threshold=0.2m, iterations=1000\n"]}], "source": ["# Final readiness print\n", "print(\"RANSAC Ground Segmentation - Ready!\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {ground_seg_path}\")\n", "print(f\"Current run output: {current_run_path}\")\n", "print(f\"RANSAC Parameters: threshold={distance_threshold}m, iterations={num_iterations}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1;33m[Open3D WARNING] GLFW Error: Cocoa: Failed to find service port for display\u001b[0;m\n", "\u001b[1;33m[Open3D WARNING] GLFW Error: Cocoa: Failed to find service port for display\u001b[0;m\n"]}], "source": ["# Visualize the results\n", "def show_pointcloud(points, color):\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    pcd.paint_uniform_color(color)\n", "    return pcd\n", "\n", "ground_vis = show_pointcloud(ground_points, [0.2, 0.8, 0.2])\n", "nonground_vis = show_pointcloud(nonground_points, [0.8, 0.2, 0.2])\n", "\n", "o3d.visualization.draw_geometries([ground_vis, nonground_vis])\n"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}