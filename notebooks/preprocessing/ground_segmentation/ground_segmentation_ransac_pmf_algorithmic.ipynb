# Papermill Parameters - Override these when running with papermill

# Site configuration
site_name = "Castro"
project_type = "ENEL"
point_cloud_path = ""  # Leave empty for auto-detection

# RANSAC parameters
ransac_distance_threshold = 0.2  # Distance threshold (meters)
ransac_num_iterations = 1000     # Number of iterations
ransac_min_inliers_ratio = 0.05  # Minimum inlier ratio
ransac_early_stop_ratio = 0.6    # Early stopping ratio

# PMF parameters
pmf_cell_size = 1.0              # Grid cell size (meters)
pmf_max_window_size = 33         # Maximum window size
pmf_slope = 0.15                 # Slope parameter (radians)

# Processing parameters
max_points_processing = 1000000  # Max points for processing
save_intermediate_results = True # Save RANSAC-only and PMF-additional results
create_visualizations = True     # Generate comparison plots

# Import libraries
import numpy as np
import json
import matplotlib.pyplot as plt
import time
import logging
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
from scipy import ndimage
from scipy.ndimage import grey_erosion, grey_dilation
import open3d as o3d
import laspy

# Set random seed for reproducibility
np.random.seed(42)
print("Libraries imported")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
print("Logging configured")

# Setup paths and directories
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
data_path = Path('../../..') / 'data'
input_path = data_path / project_type / site_name / 'raw'
ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
current_run_path = Path('output_runs') / f'{site_name}_ransac_pmf_{timestamp}'

# Create directories
ground_seg_path.mkdir(parents=True, exist_ok=True)
current_run_path.mkdir(parents=True, exist_ok=True)

print(f"Paths configured")
print(f"- Input: {input_path}")
print(f"- Output: {ground_seg_path}")
print(f"- Run: {current_run_path}")

# Save parameters for reproducibility
parameters = {
    "run_info": {
        "timestamp": timestamp,
        "site_name": site_name,
        "project_type": project_type,
        "method": "RANSAC+PMF Sequential"
    },
    "ransac_parameters": {
        "distance_threshold": ransac_distance_threshold,
        "num_iterations": ransac_num_iterations,
        "min_inliers_ratio": ransac_min_inliers_ratio,
        "early_stop_ratio": ransac_early_stop_ratio
    },
    "pmf_parameters": {
        "cell_size": pmf_cell_size,
        "max_window_size": pmf_max_window_size,
        "slope": pmf_slope
    }
}

with open(current_run_path / "parameters.json", 'w') as f:
    json.dump(parameters, f, indent=2)

print(f"Parameters saved to: {current_run_path / 'parameters.json'}")
logger.info(f"RANSAC+PMF processing initialized for site: {site_name}")

# Discover LAS/LAZ files
las_files = list(input_path.glob("*.las")) + list(input_path.glob("*.laz"))
if not las_files:
    raise FileNotFoundError(f"No LAS/LAZ files found in {input_path}")

las_file = las_files[0]
print(f"Found {len(las_files)} LAS/LAZ file(s) - Loading: {las_file.name}")
logger.info(f"Reading LAS file: {las_file}")

# Load point cloud from LAS file
las = laspy.read(str(las_file))
x, y, z = las.x, las.y, las.z
points = np.column_stack((x, y, z))
original_point_count = len(points)

print(f"Loaded {original_point_count:,} points")
print(f"  X: {x.min():.2f} to {x.max():.2f}")
print(f"  Y: {y.min():.2f} to {y.max():.2f}")
print(f"  Z: {z.min():.2f} to {z.max():.2f}")
logger.info(f"Loaded {original_point_count:,} points")

# Downsample for processing efficiency if needed
if original_point_count > max_points_processing:
    indices = np.random.choice(original_point_count, max_points_processing, replace=False)
    points = points[indices]
    print(f"Downsampled to {len(points):,} points for processing")
    logger.info(f"Downsampled from {original_point_count:,} to {len(points):,} points")
else:
    print(f"Using all {len(points):,} points for processing")

# Initialize RANSAC parameters
n_points = len(points)
min_inliers = int(n_points * ransac_min_inliers_ratio)
early_stop_inliers = int(n_points * ransac_early_stop_ratio)

print(f"RANSAC Configuration:")
print(f"- Distance threshold: {ransac_distance_threshold}m")
print(f"- Iterations: {ransac_num_iterations}")
print(f"- Min inliers: {min_inliers:,} ({ransac_min_inliers_ratio*100:.1f}%)")
print(f"- Early stop: {early_stop_inliers:,} ({ransac_early_stop_ratio*100:.1f}%)")

# RANSAC algorithm - iterative plane fitting
best_plane_params = None
best_inliers = []
max_inliers = 0
start_time = time.time()

print("Running RANSAC iterations...")
for i in tqdm(range(ransac_num_iterations), desc="RANSAC"):
    # Sample 3 random points to define plane
    sample_indices = np.random.choice(n_points, 3, replace=False)
    p1, p2, p3 = points[sample_indices]
    
    # Calculate plane normal using cross product
    v1, v2 = p2 - p1, p3 - p1
    normal = np.cross(v1, v2)
    
    # Skip degenerate cases (collinear points)
    if np.linalg.norm(normal) < 1e-6:
        continue
    
    # Normalize and ensure upward-facing normal
    normal = normal / np.linalg.norm(normal)
    if normal[2] < 0:
        normal = -normal
    
    # Plane equation: ax + by + cz + d = 0
    d = -np.dot(normal, p1)
    plane_params = np.append(normal, d)
    
    # Calculate point-to-plane distances
    distances = np.abs(np.dot(points, plane_params[:3]) + d)
    inliers = np.where(distances < ransac_distance_threshold)[0]
    n_inliers = len(inliers)
    
    # Update best plane if this one is better
    if n_inliers > max_inliers and n_inliers >= min_inliers:
        best_plane_params = plane_params
        best_inliers = inliers
        max_inliers = n_inliers
        
        # Early stopping if enough inliers found
        if n_inliers >= early_stop_inliers:
            print(f"\nEarly stopping at iteration {i+1} with {n_inliers:,} inliers")
            break

ransac_time = time.time() - start_time

if best_plane_params is None:
    raise RuntimeError("RANSAC failed to find a valid ground plane")

print(f"RANSAC completed in {ransac_time:.2f} seconds")

# Extract RANSAC ground and non-ground points
ransac_ground_mask = np.zeros(len(points), dtype=bool)
ransac_ground_mask[best_inliers] = True
ransac_nonground_mask = ~ransac_ground_mask

ransac_ground_points = points[ransac_ground_mask]
ransac_nonground_points = points[ransac_nonground_mask]

print(f"RANSAC Results:")
print(f"- Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)")
print(f"- Non-ground points: {len(ransac_nonground_points):,} ({len(ransac_nonground_points)/len(points)*100:.1f}%)")
print(f"- Plane: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0")

logger.info(f"RANSAC found {len(ransac_ground_points):,} ground points in {ransac_time:.2f}s")

# PMF configuration
print(f"PMF Configuration:")
print(f"- Processing {len(ransac_nonground_points):,} non-ground points from RANSAC")
print(f"- Cell size: {pmf_cell_size}m")
print(f"- Max window: {pmf_max_window_size}")
print(f"- Slope threshold: {pmf_slope} radians")

if len(ransac_nonground_points) == 0:
    print("No non-ground points to process with PMF")
    pmf_additional_ground_points = np.array([]).reshape(0, 3)
    pmf_remaining_nonground_points = np.array([]).reshape(0, 3)
    pmf_time = 0.0
else:
    print("Starting PMF processing...")

# PMF algorithm - grid-based morphological filtering
if len(ransac_nonground_points) > 0:
    pmf_start_time = time.time()
    
    # Create 2D grid from non-ground points
    min_xy = np.min(ransac_nonground_points[:, :2], axis=0)
    max_xy = np.max(ransac_nonground_points[:, :2], axis=0)
    dims = np.ceil((max_xy - min_xy) / pmf_cell_size).astype(int)
    
    print(f"- Grid dimensions: {dims[0]} x {dims[1]} cells")
    
    if dims[0] <= 0 or dims[1] <= 0:
        print("Invalid grid dimensions, skipping PMF")
        pmf_ground_mask_subset = np.array([False] * len(ransac_nonground_points))
    else:
        # Initialize grid with NaN
        grid = np.full(dims, np.nan)
        
        # Populate grid with minimum Z values per cell
        for i, (x, y, z) in enumerate(ransac_nonground_points):
            xi = int((x - min_xy[0]) / pmf_cell_size)
            yi = int((y - min_xy[1]) / pmf_cell_size)
            if 0 <= xi < dims[0] and 0 <= yi < dims[1]:
                if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:
                    grid[xi, yi] = z
        
        print(f"- Grid populated with {np.sum(~np.isnan(grid))} non-empty cells")

# PMF morphological operations
if len(ransac_nonground_points) > 0 and dims[0] > 0 and dims[1] > 0:
    # Fill holes in grid
    filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)
    
    # Morphological opening (erosion followed by dilation)
    opened = grey_dilation(
        grey_erosion(filled_grid, size=pmf_max_window_size), 
        size=pmf_max_window_size
    )
    
    # Calculate height differences
    z_diff = filled_grid - opened
    ground_mask_2d = z_diff < pmf_slope
    
    print(f"- Morphological filtering completed")
    print(f"- Ground cells: {np.sum(ground_mask_2d)} / {np.prod(dims)}")

# Map grid results back to points
if len(ransac_nonground_points) > 0 and dims[0] > 0 and dims[1] > 0:
    pmf_ground_mask_subset = []
    for x, y, z in ransac_nonground_points:
        xi = int((x - min_xy[0]) / pmf_cell_size)
        yi = int((y - min_xy[1]) / pmf_cell_size)
        if 0 <= xi < dims[0] and 0 <= yi < dims[1]:
            pmf_ground_mask_subset.append(ground_mask_2d[xi, yi])
        else:
            pmf_ground_mask_subset.append(False)
    
    pmf_ground_mask_subset = np.array(pmf_ground_mask_subset)
    pmf_time = time.time() - pmf_start_time
    
    print(f"- PMF completed in {pmf_time:.2f} seconds")
else:
    pmf_ground_mask_subset = np.array([False] * len(ransac_nonground_points))
    pmf_time = 0.0

# Extract PMF results
pmf_additional_ground_points = ransac_nonground_points[pmf_ground_mask_subset]
pmf_remaining_nonground_points = ransac_nonground_points[~pmf_ground_mask_subset]

print(f"PMF Results:")
print(f"- Additional ground points: {len(pmf_additional_ground_points):,}")
print(f"- Remaining non-ground: {len(pmf_remaining_nonground_points):,}")
print(f"- PMF recovery rate: {len(pmf_additional_ground_points)/len(ransac_nonground_points)*100:.1f}%")

logger.info(f"PMF found {len(pmf_additional_ground_points):,} additional ground points in {pmf_time:.2f}s")

# Combine RANSAC and PMF ground points
if len(pmf_additional_ground_points) > 0:
    combined_ground_points = np.vstack([ransac_ground_points, pmf_additional_ground_points])
else:
    combined_ground_points = ransac_ground_points

combined_nonground_points = pmf_remaining_nonground_points
total_processing_time = ransac_time + pmf_time

print(f"Combined Results (RANSAC + PMF):")
print(f"- Total ground points: {len(combined_ground_points):,} ({len(combined_ground_points)/len(points)*100:.1f}%)")
print(f"- Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)")
print(f"- Total processing time: {total_processing_time:.2f} seconds")
print(f"")
print(f"Breakdown:")
print(f"- RANSAC ground: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)")
print(f"- PMF additional: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)")
print(f"- Improvement: +{len(pmf_additional_ground_points)/len(points)*100:.1f}% ground coverage")

logger.info(f"Combined segmentation: {len(combined_ground_points):,} ground, {len(combined_nonground_points):,} non-ground")

# Save processing results summary
results_summary = {
    "processing_info": {
        "total_points": len(points),
        "original_points": original_point_count,
        "total_time_seconds": total_processing_time,
        "ransac_time_seconds": ransac_time,
        "pmf_time_seconds": pmf_time
    },
    "ransac_results": {
        "ground_points": len(ransac_ground_points),
        "ground_ratio": len(ransac_ground_points) / len(points),
        "plane_equation": best_plane_params.tolist()
    },
    "pmf_results": {
        "additional_ground_points": len(pmf_additional_ground_points),
        "additional_ground_ratio": len(pmf_additional_ground_points) / len(points)
    },
    "combined_results": {
        "final_ground_points": len(combined_ground_points),
        "final_nonground_points": len(combined_nonground_points),
        "final_ground_ratio": len(combined_ground_points) / len(points)
    }
}

with open(current_run_path / "results_summary.json", 'w') as f:
    json.dump(results_summary, f, indent=2)

print(f"Results summary saved to: {current_run_path / 'results_summary.json'}")

# Save function using Open3D (consistent with other notebooks)
def save_ply(path, points_array, description=""):
    """Save points to PLY file using Open3D."""
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(path), pc)
    logger.info(f"Saved: {path}")
    if description:
        print(f"Saved {description}: {len(points_array):,} points to {path.name}")

print("Saving point cloud outputs...")

# Save main results with method-specific naming
method_name = "ransac_pmf"

# Save to main ground segmentation directory
ground_file = ground_seg_path / f"{site_name}_{method_name}_ground.ply"
nonground_file = ground_seg_path / f"{site_name}_{method_name}_nonground.ply"

save_ply(ground_file, combined_ground_points, "combined ground")
save_ply(nonground_file, combined_nonground_points, "combined non-ground")

# Save to run directory
save_ply(current_run_path / "ground_points.ply", combined_ground_points)
save_ply(current_run_path / "nonground_points.ply", combined_nonground_points)

# Save intermediate results if requested
if save_intermediate_results:
    print("Saving intermediate results...")
    
    # RANSAC-only results
    save_ply(current_run_path / "ransac_only_ground.ply", ransac_ground_points, "RANSAC ground")
    save_ply(current_run_path / "ransac_only_nonground.ply", ransac_nonground_points, "RANSAC non-ground")
    
    # PMF additional results
    if len(pmf_additional_ground_points) > 0:
        save_ply(current_run_path / "pmf_additional_ground.ply", pmf_additional_ground_points, "PMF additional ground")
    
    print(f"Intermediate results saved to: {current_run_path}")

# Prepare data for visualization (subsample for performance)
if create_visualizations:
    vis_sample_size = 50000
    
    if len(points) > vis_sample_size:
        # Subsample points for visualization
        vis_indices = np.random.choice(len(points), vis_sample_size, replace=False)
        vis_points = points[vis_indices]
        
        # Create corresponding masks for visualization
        vis_ransac_ground = ransac_ground_mask[vis_indices]
        
        print(f"Subsampled {len(vis_points):,} points for visualization")
    else:
        vis_points = points
        vis_ransac_ground = ransac_ground_mask
        print(f"Using all {len(vis_points):,} points for visualization")
    
    print("Visualization data prepared")
else:
    print("Visualization disabled - skipping plots")

# Create combined ground mask for visualization
if create_visualizations:
    vis_combined_ground = np.zeros(len(vis_points), dtype=bool)
    
    # Mark RANSAC ground points
    vis_combined_ground[vis_ransac_ground] = True
    
    # Mark PMF additional ground points
    if len(pmf_additional_ground_points) > 0:
        for i, point in enumerate(vis_points):
            if not vis_ransac_ground[i]:  # Only check non-RANSAC points
                # Check if this point is in PMF additional ground
                distances = np.linalg.norm(pmf_additional_ground_points - point, axis=1)
                if len(distances) > 0 and np.min(distances) < 0.1:  # Small tolerance
                    vis_combined_ground[i] = True
    
    print(f"Combined visualization mask created")
    print(f"- RANSAC ground: {np.sum(vis_ransac_ground):,} points")
    print(f"- Combined ground: {np.sum(vis_combined_ground):,} points")
    print(f"- PMF improvement: +{np.sum(vis_combined_ground) - np.sum(vis_ransac_ground):,} points")

# Create comprehensive comparison visualization
if create_visualizations:
    fig = plt.figure(figsize=(20, 15))
    
    # Calculate axis limits for consistent scaling
    xlim = (vis_points[:, 0].min(), vis_points[:, 0].max())
    ylim = (vis_points[:, 1].min(), vis_points[:, 1].max())
    zlim = (vis_points[:, 2].min(), vis_points[:, 2].max())
    
    print("Creating 3D visualizations...")
    
    # Plot 1: Original point cloud
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    ax1.scatter(vis_points[:, 0], vis_points[:, 1], vis_points[:, 2], 
                c=vis_points[:, 2], cmap='viridis', s=1, alpha=0.6)
    ax1.set_title('Original Point Cloud')
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_xlim(xlim)
    ax1.set_ylim(ylim)
    ax1.set_zlim(zlim)
    
    print("- Original point cloud plot")

# Plot 2: RANSAC results
if create_visualizations:
    ax2 = fig.add_subplot(2, 3, 2, projection='3d')
    
    # RANSAC ground points
    ground_vis = vis_points[vis_ransac_ground]
    nonground_vis = vis_points[~vis_ransac_ground]
    
    if len(ground_vis) > 0:
        ax2.scatter(ground_vis[:, 0], ground_vis[:, 1], ground_vis[:, 2], 
                    c='brown', s=1, alpha=0.6, label='Ground')
    if len(nonground_vis) > 0:
        ax2.scatter(nonground_vis[:, 0], nonground_vis[:, 1], nonground_vis[:, 2], 
                    c='green', s=1, alpha=0.6, label='Non-ground')
    
    ax2.set_title('RANSAC Results')
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_zlabel('Z (m)')
    ax2.set_xlim(xlim)
    ax2.set_ylim(ylim)
    ax2.set_zlim(zlim)
    ax2.legend()
    
    print("RANSAC results plot")

# Plot 3: Combined RANSAC + PMF results
if create_visualizations:
    ax3 = fig.add_subplot(2, 3, 3, projection='3d')
    
    # Combined ground and non-ground points
    combined_ground_vis = vis_points[vis_combined_ground]
    combined_nonground_vis = vis_points[~vis_combined_ground]
    
    if len(combined_ground_vis) > 0:
        ax3.scatter(combined_ground_vis[:, 0], combined_ground_vis[:, 1], combined_ground_vis[:, 2], 
                    c='brown', s=1, alpha=0.6, label='Ground')
    if len(combined_nonground_vis) > 0:
        ax3.scatter(combined_nonground_vis[:, 0], combined_nonground_vis[:, 1], combined_nonground_vis[:, 2], 
                    c='green', s=1, alpha=0.6, label='Non-ground')
    
    ax3.set_title('RANSAC + PMF Results')
    ax3.set_xlabel('X (m)')
    ax3.set_ylabel('Y (m)')
    ax3.set_zlabel('Z (m)')
    ax3.set_xlim(xlim)
    ax3.set_ylim(ylim)
    ax3.set_zlim(zlim)
    ax3.legend()
    
    print("Combined results plot")

# Plot 4: Height distribution comparison
if create_visualizations:
    ax4 = fig.add_subplot(2, 3, 4)
    bins = 50
    
    # Plot height histograms
    ax4.hist(ransac_ground_points[:, 2], bins=bins, alpha=0.7, 
             label='RANSAC Ground', color='brown', edgecolor='black')
    
    if len(pmf_additional_ground_points) > 0:
        ax4.hist(pmf_additional_ground_points[:, 2], bins=bins, alpha=0.7, 
                 label='PMF Additional', color='orange', edgecolor='black')
    
    ax4.hist(combined_nonground_points[:, 2], bins=bins, alpha=0.7, 
             label='Non-ground', color='green', edgecolor='black')
    
    ax4.set_title('Height Distribution')
    ax4.set_xlabel('Z (meters)')
    ax4.set_ylabel('Point Count')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    print("Height distribution plot")

# Plot 5: Processing time comparison
if create_visualizations:
    ax5 = fig.add_subplot(2, 3, 5)
    
    methods = ['RANSAC', 'PMF', 'Total']
    times = [ransac_time, pmf_time, total_processing_time]
    colors = ['blue', 'orange', 'red']
    
    bars = ax5.bar(methods, times, color=colors, alpha=0.7, edgecolor='black')
    ax5.set_title('Processing Time Comparison')
    ax5.set_ylabel('Time (seconds)')
    
    # Add value labels on bars
    for bar, time_val in zip(bars, times):
        ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(times)*0.01, 
                f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')
    
    ax5.grid(True, alpha=0.3)
    
    print("Processing time plot")

# Plot 6: Point count breakdown
if create_visualizations:
    ax6 = fig.add_subplot(2, 3, 6)
    
    categories = ['RANSAC\nGround', 'PMF\nAdditional', 'Final\nNon-ground']
    counts = [len(ransac_ground_points), len(pmf_additional_ground_points), len(combined_nonground_points)]
    colors = ['brown', 'orange', 'green']
    
    bars = ax6.bar(categories, counts, color=colors, alpha=0.7, edgecolor='black')
    ax6.set_title('Point Count Breakdown')
    ax6.set_ylabel('Point Count')
    
    # Add value labels on bars
    for bar, count in zip(bars, counts):
        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01, 
                f'{count:,}', ha='center', va='bottom', fontweight='bold', rotation=0)
    
    ax6.grid(True, alpha=0.3)
    
    print("Point count breakdown plot")

# Finalize and save visualization
if create_visualizations:
    plt.tight_layout()
    
    # Save visualization
    viz_file = current_run_path / "ransac_pmf_comparison.png"
    plt.savefig(viz_file, dpi=300, bbox_inches='tight')
    
    print(f"Visualization saved to: {viz_file}")
    
    # Display the plot
    plt.show()
    
    logger.info(f"Visualization saved: {viz_file}")

# Additional PMF-specific visualization (if PMF found additional points)
if create_visualizations and len(pmf_additional_ground_points) > 0:
    fig_pmf = plt.figure(figsize=(12, 8))
    ax_pmf = fig_pmf.add_subplot(1, 1, 1, projection='3d')
    
    # Show only PMF additional ground points
    ax_pmf.scatter(pmf_additional_ground_points[:, 0],
                   pmf_additional_ground_points[:, 1],
                   pmf_additional_ground_points[:, 2],
                   c='orange', s=2, alpha=0.8, label='PMF Additional Ground')
    
    ax_pmf.set_title(f'PMF Recovered Ground Points ({len(pmf_additional_ground_points):,} points)')
    ax_pmf.set_xlabel('X (m)')
    ax_pmf.set_ylabel('Y (m)')
    ax_pmf.set_zlabel('Z (m)')
    ax_pmf.legend()
    
    # Save PMF-specific visualization
    pmf_viz_file = current_run_path / "pmf_additional_ground.png"
    plt.savefig(pmf_viz_file, dpi=300, bbox_inches='tight')
    
    print(f"PMF-specific visualization saved to: {pmf_viz_file}")
    plt.show()
    
elif create_visualizations:
    print("No PMF additional points to visualize")

# Comprehensive final processing summary
print("\n" + "="*70)
print("RANSAC + PMF GROUND SEGMENTATION COMPLETE")
print("="*70)
print(f"Site: {site_name} ({project_type})")
print(f"Method: {method_name}")
print(f"Timestamp: {timestamp}")
print("")
print(f"Data Processing:")
print(f"- Input: {original_point_count:,} points")
print(f"- Processed: {len(points):,} points")
print(f"- Downsampling: {'Yes' if len(points) < original_point_count else 'No'}")
print("")
print(f"RANSAC Results:")
print(f"- Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)")
print(f"- Processing time: {ransac_time:.2f} seconds")
print(f"- Plane equation: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0")
print("")
print(f"PMF Results:")
print(f"- Additional ground: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)")
print(f"- Processing time: {pmf_time:.2f} seconds")
print(f"- Recovery rate: {len(pmf_additional_ground_points)/len(ransac_nonground_points)*100:.1f}% of non-ground points")
print("")
print(f"Final Results:")
print(f"- Total ground: {len(combined_ground_points):,} ({len(combined_ground_points)/len(points)*100:.1f}%)")
print(f"- Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)")
print(f"- Improvement: +{len(pmf_additional_ground_points)/len(points)*100:.1f}% ground coverage")
print(f"- Total processing time: {total_processing_time:.2f} seconds")
print("")
print(f"Outputs:")
print(f"- Main results: {ground_seg_path}")
print(f"- Run details: {current_run_path}")
print(f"- Parameters: {current_run_path / 'parameters.json'}")
print(f"- Results summary: {current_run_path / 'results_summary.json'}")
if create_visualizations:
    print(f"  Visualization: {current_run_path / 'ransac_pmf_comparison.png'}")
print("="*70)

logger.info(f"RANSAC+PMF processing completed successfully for {site_name}")

# Set up paths with proper project organization
base_path = Path('../../..')
data_path = base_path / 'data'
logger.info(f"Checking base data path: {data_path}, Exists: {data_path.exists()}")

# Create output directory structure for this run with method-specific naming
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_runs_path = Path('output_runs')
current_run_path = output_runs_path / f'{site_name}_ransac_{timestamp}'
current_run_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Created current run output path: {current_run_path.resolve()}")

# Input and output paths following the specified organization
if point_cloud_path:
    input_path = Path(point_cloud_path)
    logger.info(f"Custom point_cloud_path provided: {input_path}")
    if not input_path.exists():
        raise FileNotFoundError(f"Input path does not exist: {input_path}")
else:
    raw_path = data_path / project_type / site_name / 'raw'
    input_path = raw_path
    logger.info(f"Using default input path: {input_path}")

ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
ground_seg_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Input path exists: {input_path.exists()}")
logger.info(f"Ground segmentation output path exists: {ground_seg_path.exists()}")