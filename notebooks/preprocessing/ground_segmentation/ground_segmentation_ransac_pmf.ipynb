# Papermill parameters cell
# These parameters can be overridden when running with papermill

# Site and project configuration
site_name = "Castro"  # Site identifier
buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)
point_cloud_path = ""  # Path to input point cloud file
project_type = "ENEL"  # Options: "ENEL", "USA"

# RANSAC parameters
ransac_distance_threshold = 0.2  # Maximum distance for a point to be considered an inlier (meters)
ransac_num_iterations = 1000     # Number of RANSAC iterations to perform
ransac_min_inliers_ratio = 0.05  # Minimum ratio of inliers to accept a plane
ransac_early_stop_ratio = 0.6    # Ratio of inliers to total points for early stopping

# PMF parameters
pmf_cell_size = 1.0  # Grid cell size for rasterization (meters)
pmf_max_window_size = 33  # Maximum window size for morphological operations
pmf_slope = 0.15  # Slope parameter for terrain (radians)
pmf_max_distance = 2.5  # Maximum distance threshold (meters)
pmf_initial_distance = 0.5  # Initial distance threshold (meters)

# Processing parameters
max_points_processing = 1000000  # Maximum points to process (for performance)
visualization_enabled = True     # Enable 3D visualizations
save_intermediate_results = True # Save intermediate point clouds

# Output parameters
save_ground_points = True        # Save ground points to PLY file
save_nonground_points = True     # Save non-ground points to PLY file
generate_summary_stats = True    # Generate summary statistics
create_visualizations = True     # Create and save visualization plots

# Import libraries
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import logging
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
from scipy import ndimage
from scipy.ndimage import grey_erosion, grey_dilation
import open3d as o3d
import laspy

# Set random seed for reproducible results
random_seed = 42                 # Random seed for reproducible results
np.random.seed(random_seed)
print(f"Libraries imported successfully. Random seed set to {random_seed}")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info(f"RANSAC+PMF Ground Segmentation initialized for site: {site_name}")
logger.info(f"RANSAC Parameters - Distance threshold: {ransac_distance_threshold}m, Iterations: {ransac_num_iterations}")
logger.info(f"PMF Parameters - Cell size: {pmf_cell_size}m, Max window: {pmf_max_window_size}, Slope: {pmf_slope}")

# Set up paths with proper project organization
base_path = Path('../../..')
data_path = base_path / 'data'
logger.info(f"Checking base data path: {data_path}, Exists: {data_path.exists()}")

# Create output directory structure for this run with method-specific naming
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_runs_path = Path('output_runs')
current_run_path = output_runs_path / f'{site_name}_ransac_{timestamp}'
current_run_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Created current run output path: {current_run_path.resolve()}")

# Input and output paths following the specified organization
if point_cloud_path:
    input_path = Path(point_cloud_path)
    logger.info(f"Custom point_cloud_path provided: {input_path}")
    if not input_path.exists():
        raise FileNotFoundError(f"Input path does not exist: {input_path}")
else:
    raw_path = data_path / project_type / site_name / 'raw'
    input_path = raw_path
    logger.info(f"Using default input path: {input_path}")

ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
ground_seg_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Input path exists: {input_path.exists()}")
logger.info(f"Ground segmentation output path exists: {ground_seg_path.exists()}")

# Save parameters to JSON for reproducibility
parameters = {
    "run_info": {
        "timestamp": timestamp,
        "site_name": site_name,
        "project_type": project_type,
        "method": "RANSAC+PMF Sequential",
        "notebook_version": "1.0"
    },
    "ransac_parameters": {
        "distance_threshold": ransac_distance_threshold,
        "num_iterations": ransac_num_iterations,
        "min_inliers_ratio": ransac_min_inliers_ratio,
        "early_stop_ratio": ransac_early_stop_ratio
    },
    "pmf_parameters": {
        "cell_size": pmf_cell_size,
        "max_window_size": pmf_max_window_size,
        "slope": pmf_slope,
        "max_distance": pmf_max_distance,
        "initial_distance": pmf_initial_distance
    },
    "processing_parameters": {
        "max_points_processing": max_points_processing,
        "buffer_radius": buffer_radius,
        "visualization_enabled": visualization_enabled,
        "save_intermediate_results": save_intermediate_results
    },
    "paths": {
        "input_path": str(input_path),
        "output_path": str(ground_seg_path),
        "run_output_path": str(current_run_path)
    }
}

# Save parameters to file
params_file = current_run_path / "parameters.json"
with open(params_file, 'w') as f:
    json.dump(parameters, f, indent=2)

print(f"Parameters saved to: {params_file}")
print("\nRun Configuration:")
print(f"  RANSAC: threshold={ransac_distance_threshold}m, iterations={ransac_num_iterations}")
print(f"  PMF: cell_size={pmf_cell_size}m, max_window={pmf_max_window_size}, slope={pmf_slope}")

from pathlib import Path
import numpy as np
import laspy

# Parameters
max_points_processing = 1_000_000

# Ensure input_path is defined and valid
input_path = Path(input_path)
if not input_path.exists() or not input_path.is_dir():
    raise FileNotFoundError(f"Invalid input_path: {input_path}")

# Discover LAS/LAZ files
las_files = list(input_path.glob("*.las")) + list(input_path.glob("*.laz"))
if not las_files:
    raise FileNotFoundError(f"No LAS/LAZ files found in {input_path}")

# Read first available LAS/LAZ file
las_file = las_files[0]
logger.info(f"Reading LAS file: {las_file}")
print(f"Reading: {las_file}")

las = laspy.read(str(las_file))
x, y, z = las.x, las.y, las.z

# Combine XYZ into Nx3 array
points = np.column_stack((x, y, z))
original_point_count = len(points)

logger.info(f"Loaded {original_point_count:,} points.")
print(f"Loaded {original_point_count:,} points")
print(f"Bounds - X: {x.min():.2f} to {x.max():.2f}, "
      f"Y: {y.min():.2f} to {y.max():.2f}, "
      f"Z: {z.min():.2f} to {z.max():.2f}")

# Downsample if necessary
if original_point_count > max_points_processing:
    indices = np.random.choice(original_point_count, max_points_processing, replace=False)
    points = points[indices]
    print(f"Downsampled to {len(points):,} points for processing")


def ransac_ground_detection(points, distance_threshold, num_iterations, min_inliers_ratio, early_stop_ratio):
    """RANSAC-based ground plane detection."""
    n_points = len(points)
    min_inliers = int(n_points * min_inliers_ratio)
    early_stop_inliers = int(n_points * early_stop_ratio)
    
    best_plane_params = None
    best_inliers = []
    max_inliers = 0
    
    start_time = time.time()
    
    for i in tqdm(range(num_iterations), desc="RANSAC iterations"):
        # Randomly sample 3 points
        sample_indices = np.random.choice(n_points, 3, replace=False)
        p1, p2, p3 = points[sample_indices]
        
        # Compute plane normal
        v1 = p2 - p1
        v2 = p3 - p1
        normal = np.cross(v1, v2)
        
        # Skip if degenerate
        if np.linalg.norm(normal) < 1e-6:
            continue
        
        normal = normal / np.linalg.norm(normal)
        
        # Ensure upward-facing normal
        if normal[2] < 0:
            normal = -normal
        
        # Compute plane equation: ax + by + cz + d = 0
        d = -np.dot(normal, p1)
        plane_params = np.append(normal, d)
        
        # Distance of all points to the plane
        distances = np.abs(np.dot(points, plane_params[:3]) + d)
        
        # Find inliers within threshold
        inliers = np.where(distances < distance_threshold)[0]
        n_inliers = len(inliers)
        
        if n_inliers > max_inliers and n_inliers >= min_inliers:
            best_plane_params = plane_params
            best_inliers = inliers
            max_inliers = n_inliers
            
            # Early stopping if we have enough inliers
            if n_inliers >= early_stop_inliers:
                print(f"Early stopping at iteration {i+1} with {n_inliers} inliers")
                break
    
    elapsed_time = time.time() - start_time
    
    return best_plane_params, best_inliers, elapsed_time

# Run RANSAC ground detection
print(f"Running RANSAC ground detection...")
print(f"Parameters: threshold={ransac_distance_threshold}m, iterations={ransac_num_iterations}, min_ratio={ransac_min_inliers_ratio}")

plane_params, ransac_ground_indices, ransac_time = ransac_ground_detection(
    points, ransac_distance_threshold, ransac_num_iterations, 
    ransac_min_inliers_ratio, ransac_early_stop_ratio
)

if plane_params is None:
    raise RuntimeError("RANSAC failed to find a valid ground plane")

# Create ground/non-ground masks
ransac_ground_mask = np.zeros(len(points), dtype=bool)
ransac_ground_mask[ransac_ground_indices] = True
ransac_nonground_mask = ~ransac_ground_mask

ransac_ground_points = points[ransac_ground_mask]
ransac_nonground_points = points[ransac_nonground_mask]

print(f"\nRANSAC Results:")
print(f"  Processing time: {ransac_time:.2f} seconds")
print(f"  Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)")
print(f"  Non-ground points: {len(ransac_nonground_points):,} ({len(ransac_nonground_points)/len(points)*100:.1f}%)")
print(f"  Plane equation: {plane_params[0]:.3f}x + {plane_params[1]:.3f}y + {plane_params[2]:.3f}z + {plane_params[3]:.3f} = 0")

def pmf_ground_detection(points, cell_size, max_window_size, slope):
    """Progressive Morphological Filter for ground detection."""
    if len(points) == 0:
        return np.array([], dtype=bool)
    
    # Grid the point cloud (2D raster)
    min_xy = np.min(points[:, :2], axis=0)
    max_xy = np.max(points[:, :2], axis=0)
    dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)
    
    if dims[0] <= 0 or dims[1] <= 0:
        return np.array([False] * len(points))
    
    grid = np.full(dims, np.nan)
    
    # Populate raster with lowest Z value per cell
    for i, (x, y, z) in enumerate(points):
        xi = int((x - min_xy[0]) / cell_size)
        yi = int((y - min_xy[1]) / cell_size)
        if 0 <= xi < dims[0] and 0 <= yi < dims[1]:
            if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:
                grid[xi, yi] = z
    
    # Fill holes
    filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)
    
    # Morphological opening (erosion then dilation)
    opened = grey_dilation(grey_erosion(filled_grid, size=max_window_size), size=max_window_size)
    
    # Ground mask based on slope threshold
    z_diff = filled_grid - opened
    ground_mask_2d = z_diff < slope
    
    # Reconstruct full ground point mask
    ground_mask = []
    for x, y, z in points:
        xi = int((x - min_xy[0]) / cell_size)
        yi = int((y - min_xy[1]) / cell_size)
        if 0 <= xi < dims[0] and 0 <= yi < dims[1]:
            ground_mask.append(ground_mask_2d[xi, yi])
        else:
            ground_mask.append(False)
    
    return np.array(ground_mask)

# Apply PMF to non-ground points from RANSAC
print(f"\nApplying PMF to {len(ransac_nonground_points):,} non-ground points from RANSAC...")
print(f"PMF Parameters: cell_size={pmf_cell_size}m, max_window={pmf_max_window_size}, slope={pmf_slope}")

pmf_start_time = time.time()
pmf_ground_mask_subset = pmf_ground_detection(
    ransac_nonground_points, pmf_cell_size, pmf_max_window_size, pmf_slope
)
pmf_time = time.time() - pmf_start_time

# Extract additional ground points found by PMF
pmf_additional_ground_points = ransac_nonground_points[pmf_ground_mask_subset]
pmf_remaining_nonground_points = ransac_nonground_points[~pmf_ground_mask_subset]

print(f"\nPMF Results:")
print(f"  Processing time: {pmf_time:.2f} seconds")
print(f"  Additional ground points found: {len(pmf_additional_ground_points):,}")
print(f"  Remaining non-ground points: {len(pmf_remaining_nonground_points):,}")

# Combine RANSAC ground points with PMF additional ground points
combined_ground_points = np.vstack([
    ransac_ground_points,
    pmf_additional_ground_points
])

combined_nonground_points = pmf_remaining_nonground_points

# Calculate final statistics
total_processing_time = ransac_time + pmf_time
final_ground_ratio = len(combined_ground_points) / len(points)
final_nonground_ratio = len(combined_nonground_points) / len(points)

print(f"\nCombined Results (RANSAC + PMF):")
print(f"  Total processing time: {total_processing_time:.2f} seconds")
print(f"  Final ground points: {len(combined_ground_points):,} ({final_ground_ratio*100:.1f}%)")
print(f"  Final non-ground points: {len(combined_nonground_points):,} ({final_nonground_ratio*100:.1f}%)")
print(f"  \nBreakdown:")
print(f"    RANSAC ground: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)")
print(f"    PMF additional: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)")
print(f"    Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)")

# Save results summary
results_summary = {
    "processing_info": {
        "total_points": len(points),
        "original_points": original_point_count,
        "processing_time_seconds": total_processing_time,
        "ransac_time_seconds": ransac_time,
        "pmf_time_seconds": pmf_time
    },
    "ransac_results": {
        "ground_points": len(ransac_ground_points),
        "ground_ratio": len(ransac_ground_points) / len(points),
        "plane_equation": plane_params.tolist() if plane_params is not None else None
    },
    "pmf_results": {
        "additional_ground_points": len(pmf_additional_ground_points),
        "additional_ground_ratio": len(pmf_additional_ground_points) / len(points)
    },
    "combined_results": {
        "final_ground_points": len(combined_ground_points),
        "final_nonground_points": len(combined_nonground_points),
        "final_ground_ratio": final_ground_ratio,
        "final_nonground_ratio": final_nonground_ratio
    }
}

# Save results to JSON
results_file = current_run_path / "results_summary.json"
with open(results_file, 'w') as f:
    json.dump(results_summary, f, indent=2)

print(f"\nResults summary saved to: {results_file}")

def save_points_to_ply(points, filename, colors=None):
    """Save points to PLY file with optional colors."""
    with open(filename, 'w') as f:
        # Write header
        f.write("ply\n")
        f.write("format ascii 1.0\n")
        f.write(f"element vertex {len(points)}\n")
        f.write("property float x\n")
        f.write("property float y\n")
        f.write("property float z\n")
        if colors is not None:
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
        f.write("end_header\n")
        
        # Write points
        for i, point in enumerate(points):
            if colors is not None:
                f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {colors[i][0]} {colors[i][1]} {colors[i][2]}\n")
            else:
                f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f}\n")

# Save segmented point clouds using Open3D (consistent with other notebooks)
def save_ply(path, points_array, method_name=None):
    """Save points to PLY file using Open3D (consistent with other ground segmentation notebooks)."""
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(path), pc)
    logger.info(f"Saved: {path}")


# Save ground and non-ground points with method-specific naming
method_name = "ransac_pmf"

if save_ground_points:
    ground_file = ground_seg_path / f"{site_name}_{method_name}_ground.ply"
    save_ply(ground_file, combined_ground_points, "combined ground")
    
    # Also save to run directory
    run_ground_file = current_run_path / "ground_points.ply"
    save_ply(run_ground_file, combined_ground_points)

if save_nonground_points:
    nonground_file = ground_seg_path / f"{site_name}_{method_name}_nonground.ply"
    save_ply(nonground_file, combined_nonground_points, "combined non-ground")
    
    # Also save to run directory
    run_nonground_file = current_run_path / "nonground_points.ply"
    save_ply(run_nonground_file, combined_nonground_points)

# Save intermediate results if requested
if save_intermediate_results:
    # RANSAC-only results
    ransac_ground_file = current_run_path / "ransac_only_ground.ply"
    ransac_nonground_file = current_run_path / "ransac_only_nonground.ply"
    save_ply(ransac_ground_file, ransac_ground_points, "RANSAC ground")
    save_ply(ransac_nonground_file, ransac_nonground_points, "RANSAC non-ground")
    
    # PMF additional results
    if len(pmf_additional_ground_points) > 0:
        pmf_additional_file = current_run_path / "pmf_additional_ground.ply"
        save_ply(pmf_additional_file, pmf_additional_ground_points, "PMF additional ground")
    
    print(f"Intermediate results saved to: {current_run_path}")

print(f"\nRANSAC+PMF segmentation outputs saved:")
print(f"  Combined ground points: {len(combined_ground_points):,}")
print(f"  Combined non-ground points: {len(combined_nonground_points):,}")
print(f"  Method identifier: {method_name}")
print(f"  All results saved successfully!")

# Set up paths with proper project organization
base_path = Path('../../..')
data_path = base_path / 'data'
logger.info(f"Checking base data path: {data_path}, Exists: {data_path.exists()}")

# Create output directory structure for this run with method-specific naming
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_runs_path = Path('output_runs')
current_run_path = output_runs_path / f'{site_name}_ransac_{timestamp}'
current_run_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Created current run output path: {current_run_path.resolve()}")

# Input and output paths following the specified organization
if point_cloud_path:
    input_path = Path(point_cloud_path)
    logger.info(f"Custom point_cloud_path provided: {input_path}")
    if not input_path.exists():
        raise FileNotFoundError(f"Input path does not exist: {input_path}")
else:
    raw_path = data_path / project_type / site_name / 'raw'
    input_path = raw_path
    logger.info(f"Using default input path: {input_path}")

ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
ground_seg_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Input path exists: {input_path.exists()}")
logger.info(f"Ground segmentation output path exists: {ground_seg_path.exists()}")

# Subsampling and Ground Mask Preparation
# Parameters
vis_sample_size = 50000
tolerance = 0.1  # distance threshold for PMF match

if len(points) > vis_sample_size:
    vis_indices = np.random.choice(len(points), vis_sample_size, replace=False)
    vis_points = points[vis_indices]
    vis_ransac_ground = ransac_ground_mask[vis_indices]
else:
    vis_points = points
    vis_ransac_ground = ransac_ground_mask

# Original point cloud
fig = plt.figure(figsize=(20, 15))

ax1 = fig.add_subplot(2, 3, 1, projection='3d')
ax1.scatter(vis_points[:, 0], vis_points[:, 1], vis_points[:, 2],
            c=vis_points[:, 2], cmap='viridis', s=1, alpha=0.6)
ax1.set_title('Original Point Cloud')
ax1.set_xlabel('X')
ax1.set_ylabel('Y')
ax1.set_zlabel('Z')

# Create combined ground mask
vis_combined_ground = np.zeros(len(vis_points), dtype=bool)
vis_combined_ground[vis_ransac_ground] = True

for i, point in enumerate(vis_points):
    if not vis_ransac_ground[i]:
        distances = np.linalg.norm(pmf_additional_ground_points - point, axis=1)
        if len(distances) > 0 and np.min(distances) < tolerance:
            vis_combined_ground[i] = True

fig = plt.figure(figsize=(22, 14))

# Axis limits
xlim = (np.min(vis_points[:, 0]), np.max(vis_points[:, 0]))
ylim = (np.min(vis_points[:, 1]), np.max(vis_points[:, 1]))
zlim = (np.min(vis_points[:, 2]), np.max(vis_points[:, 2]))

ax1 = fig.add_subplot(2, 3, 1, projection='3d')
ax1.scatter(vis_points[:, 0], vis_points[:, 1], vis_points[:, 2],
            c=vis_points[:, 2], cmap='viridis', s=1, alpha=0.5)
ax1.set_title('Original Point Cloud')
ax1.set_xlabel('X')
ax1.set_ylabel('Y')
ax1.set_zlabel('Z')
ax1.view_init(elev=20, azim=60)
ax1.set_xlim(xlim)
ax1.set_ylim(ylim)
ax1.set_zlim(zlim)

ax2 = fig.add_subplot(2, 3, 2, projection='3d')
ax2.scatter(vis_points[vis_ransac_ground][:, 0],
            vis_points[vis_ransac_ground][:, 1],
            vis_points[vis_ransac_ground][:, 2],
            c='brown', s=1, alpha=0.6, label='Ground')
ax2.scatter(vis_points[~vis_ransac_ground][:, 0],
            vis_points[~vis_ransac_ground][:, 1],
            vis_points[~vis_ransac_ground][:, 2],
            c='green', s=1, alpha=0.6, label='Non-ground')
ax2.set_title('RANSAC Results')
ax2.set_xlabel('X')
ax2.set_ylabel('Y')
ax2.set_zlabel('Z')
ax2.view_init(elev=20, azim=60)
ax2.legend(title="Legend", title_fontsize='small')
ax2.set_xlim(xlim)
ax2.set_ylim(ylim)
ax2.set_zlim(zlim)

ax3 = fig.add_subplot(2, 3, 3, projection='3d')
ax3.scatter(vis_points[vis_combined_ground][:, 0],
            vis_points[vis_combined_ground][:, 1],
            vis_points[vis_combined_ground][:, 2],
            c='brown', s=1, alpha=0.6, label='Ground')
ax3.scatter(vis_points[~vis_combined_ground][:, 0],
            vis_points[~vis_combined_ground][:, 1],
            vis_points[~vis_combined_ground][:, 2],
            c='green', s=1, alpha=0.6, label='Non-ground')
ax3.set_title('RANSAC + PMF Results')
ax3.set_xlabel('X')
ax3.set_ylabel('Y')
ax3.set_zlabel('Z')
ax3.view_init(elev=20, azim=60)
ax3.legend(title="Legend", title_fontsize='small')
ax3.set_xlim(xlim)
ax3.set_ylim(ylim)
ax3.set_zlim(zlim)

ax4 = fig.add_subplot(2, 3, 4)
bins = 30
ax4.hist(ransac_ground_points[:, 2], bins=bins, alpha=0.6, label='RANSAC Ground', color='brown', edgecolor='black')
if len(pmf_additional_ground_points) > 0:
    ax4.hist(pmf_additional_ground_points[:, 2], bins=bins, alpha=0.6, label='PMF Additional', color='orange', edgecolor='black')
ax4.hist(combined_nonground_points[:, 2], bins=bins, alpha=0.6, label='Non-ground', color='green', edgecolor='black')
ax4.set_title('Height Distribution')
ax4.set_xlabel('Z (meters)')
ax4.set_ylabel('Point Count')
ax4.grid(True, alpha=0.3)
ax4.legend()

ax5 = fig.add_subplot(2, 3, 5)
methods = ['RANSAC', 'PMF', 'Total']
times = [ransac_time, pmf_time, total_processing_time]
bars = ax5.bar(methods, times, color=['blue', 'orange', 'red'])
ax5.set_title('Processing Time Comparison')
ax5.set_ylabel('Time (seconds)')
for bar, time_val in zip(bars, times):
    ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,
             f'{time_val:.2f}s', ha='center', va='bottom')
ax5.grid(True, alpha=0.3)

ax6 = fig.add_subplot(2, 3, 6)
categories = ['RANSAC\nGround', 'PMF\nAdditional', 'Non-ground']
counts = [len(ransac_ground_points), len(pmf_additional_ground_points), len(combined_nonground_points)]
colors = ['brown', 'orange', 'green']
bars = ax6.bar(categories, counts, color=colors)
ax6.set_title('Point Count Breakdown')
ax6.set_ylabel('Point Count')
for bar, count in zip(bars, counts):
    ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01,
             f'{count:,}', ha='center', va='bottom')
ax6.grid(True, alpha=0.3)

plt.tight_layout()
viz_file = current_run_path / "ransac_pmf_comparison.png"
plt.savefig(viz_file, dpi=300, bbox_inches='tight')
plt.show()
print(f"Visualization saved to: {viz_file}")

ax_extra = fig.add_subplot(1, 1, 1, projection='3d')
ax_extra.scatter(pmf_additional_ground_points[:, 0],
                 pmf_additional_ground_points[:, 1],
                 pmf_additional_ground_points[:, 2],
                 c='orange', s=1, label='PMF Additional (Edges)')
ax_extra.set_title("PMF Recovered Ground Points")
ax_extra.legend()