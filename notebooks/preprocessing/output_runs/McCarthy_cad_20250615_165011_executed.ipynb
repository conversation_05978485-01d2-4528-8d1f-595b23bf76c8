{"cells": [{"cell_type": "markdown", "id": "350427e3", "metadata": {"tags": ["papermill-error-cell-tag"]}, "source": ["<span style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">An Exception was encountered at '<a href=\"#papermill-error-cell\">In [5]</a>'.</span>"]}, {"cell_type": "markdown", "id": "01f8dc1d", "metadata": {"papermill": {"duration": 0.005912, "end_time": "2025-06-15T11:20:12.714640", "exception": false, "start_time": "2025-06-15T11:20:12.708728", "status": "completed"}, "tags": []}, "source": ["# CAD Metadata Extraction\n", "\n", "This notebook extracts metadata and geometric information from CAD files (DXF/DWG) for preprocessing workflows.\n", "\n", "**Stage**: Preprocessing  \n", "**Input Data**: CAD files (.dwg, .dxf)  \n", "**Output**: Structured metadata in CSV, JSON, and Parquet formats  \n", "**Schema**: Standardized pile/element specification format  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load CAD Files**: Import .dwg/.dxf files using ezdxf\n", "2. **Extract Construction Elements**: Identify pile/column INSERT blocks\n", "3. **Geometric Analysis**: Process 3D geometry and measurements\n", "4. **Coordinate Transformation**: Convert to geographic coordinates if needed\n", "5. **Export Structured Data**: Save in multiple formats for downstream usage"]}, {"cell_type": "markdown", "id": "72de2721", "metadata": {"papermill": {"duration": 0.003512, "end_time": "2025-06-15T11:20:12.721933", "exception": false, "start_time": "2025-06-15T11:20:12.718421", "status": "completed"}, "tags": []}, "source": ["## Parameters"]}, {"cell_type": "code", "execution_count": 1, "id": "954cd704", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T11:20:12.729573Z", "iopub.status.busy": "2025-06-15T11:20:12.729419Z", "iopub.status.idle": "2025-06-15T11:20:12.734180Z", "shell.execute_reply": "2025-06-15T11:20:12.733687Z"}, "papermill": {"duration": 0.007856, "end_time": "2025-06-15T11:20:12.735424", "exception": false, "start_time": "2025-06-15T11:20:12.727568", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Trino\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "cad_file_path = \"\"  # Path to specific CAD file (optional)\n", "coordinate_system = \"EPSG:32643\"  # Source coordinate reference system\n", "target_crs = \"EPSG:4326\"  # Target CRS (WGS84 for GPS coordinates)\n", "\n", "# Extraction parameters\n", "pile_name_pattern = r\"PILE_(\\d+)\"  # Regex pattern for pile identification\n", "include_attributes = True  # Extract block attributes\n", "include_geometry = True  # Perform geometric analysis\n", "coordinate_transform = True  # Transform coordinates to target CRS\n", "\n", "# Output format options\n", "export_csv = True  # Export tabular data as CSV\n", "export_json = True  # Export hierarchical metadata as JSON\n", "export_parquet = True  # Export as Parquet for large datasets"]}, {"cell_type": "code", "execution_count": 2, "id": "46bf8c0b", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T11:20:12.738789Z", "iopub.status.busy": "2025-06-15T11:20:12.738693Z", "iopub.status.idle": "2025-06-15T11:20:12.740564Z", "shell.execute_reply": "2025-06-15T11:20:12.740284Z"}, "papermill": {"duration": 0.004094, "end_time": "2025-06-15T11:20:12.741369", "exception": false, "start_time": "2025-06-15T11:20:12.737275", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "site_name = \"<PERSON>\"\n", "project_type = \"USA\"\n", "coordinate_system = \"EPSG:32612\"\n", "target_crs = \"EPSG:4326\"\n", "pile_name_pattern = \"PILE_(\\\\d+)\"\n", "include_attributes = True\n", "include_geometry = True\n", "coordinate_transform = True\n", "export_csv = True\n", "export_json = True\n", "export_parquet = True\n"]}, {"cell_type": "markdown", "id": "77f9602c", "metadata": {"papermill": {"duration": 0.001058, "end_time": "2025-06-15T11:20:12.743582", "exception": false, "start_time": "2025-06-15T11:20:12.742524", "status": "completed"}, "tags": []}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": 3, "id": "a86fedda", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T11:20:12.746125Z", "iopub.status.busy": "2025-06-15T11:20:12.746033Z", "iopub.status.idle": "2025-06-15T11:20:17.778476Z", "shell.execute_reply": "2025-06-15T11:20:17.778226Z"}, "papermill": {"duration": 5.03512, "end_time": "2025-06-15T11:20:17.779755", "exception": false, "start_time": "2025-06-15T11:20:12.744635", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parquet support available: 20.0.0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Coordinate transformation support available\n", "CAD Metadata Extraction - Ready!\n", "Data path: ../../data\n", "Project: USA/McCarthy\n", "Input path: ../../data/USA/McCarthy/raw\n", "Output path: ../../data/USA/McCarthy/preprocessing\n", "Current run output: output_runs/McCarthy_cad_20250615_165017\n", "Coordinate system: EPSG:32612 -> EPSG:4326\n"]}], "source": ["# Import libraries\n", "import ezdxf\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import os\n", "import re\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import Counter\n", "import logging\n", "\n", "# Optional imports\n", "try:\n", "    import pyarrow as pa\n", "    import pyarrow.parquet as pq\n", "    PARQUET_SUPPORT = True\n", "    print(f\"Parquet support available: {pa.__version__}\")\n", "except ImportError:\n", "    print(\"Parquet support not available. Install pyarrow for Parquet export.\")\n", "    PARQUET_SUPPORT = False\n", "\n", "try:\n", "    from pyproj import Transformer\n", "    COORDINATE_TRANSFORM_SUPPORT = True\n", "    print(f\"Coordinate transformation support available\")\n", "except ImportError:\n", "    print(\"Coordinate transformation not available. Install pyproj for CRS transformation.\")\n", "    COORDINATE_TRANSFORM_SUPPORT = False\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Create output directory structure for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_cad_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Input and output paths\n", "if cad_file_path:\n", "    input_path = Path(cad_file_path)\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "\n", "preprocessing_path = data_path / project_type / site_name / 'preprocessing'\n", "preprocessing_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"CAD Metadata Extraction - Ready!\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {preprocessing_path}\")\n", "print(f\"Current run output: {current_run_path}\")\n", "print(f\"Coordinate system: {coordinate_system} -> {target_crs}\")"]}, {"cell_type": "markdown", "id": "c677405c", "metadata": {"papermill": {"duration": 0.001445, "end_time": "2025-06-15T11:20:17.784638", "exception": false, "start_time": "2025-06-15T11:20:17.783193", "status": "completed"}, "tags": []}, "source": ["## Data Loading Functions"]}, {"cell_type": "code", "execution_count": 4, "id": "698a9e42", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T11:20:17.787909Z", "iopub.status.busy": "2025-06-15T11:20:17.787685Z", "iopub.status.idle": "2025-06-15T11:20:17.794182Z", "shell.execute_reply": "2025-06-15T11:20:17.793845Z"}, "papermill": {"duration": 0.009044, "end_time": "2025-06-15T11:20:17.794992", "exception": false, "start_time": "2025-06-15T11:20:17.785948", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Import the standardized schema\n", "import sys\n", "sys.path.append('.')\n", "from metadata_schema import ElementMetadata, MetadataSchema, MetadataExporter, create_element_from_cad_insert\n", "\n", "def find_cad_files():\n", "    \"\"\"\n", "    Find CAD files in the input directory.\n", "    \"\"\"\n", "    if cad_file_path and Path(cad_file_path).exists():\n", "        return [Path(cad_file_path)]\n", "    \n", "    search_path = input_path if input_path.is_dir() else input_path.parent\n", "    cad_files = [\n", "        *list(search_path.glob('*.dxf')),\n", "        *list(search_path.glob('*.dwg'))\n", "    ]\n", "    \n", "    return cad_files\n", "\n", "def load_cad_file(file_path):\n", "    \"\"\"\n", "    Load CAD file using ezdxf.\n", "    \"\"\"\n", "    try:\n", "        if file_path.suffix.lower() == '.dwg':\n", "            print(f\"DWG files need to be converted to DXF format first.\")\n", "            print(f\"Please convert {file_path} to DXF format.\")\n", "            return None, None\n", "        \n", "        doc = ezdxf.readfile(str(file_path))\n", "        modelspace = doc.modelspace()\n", "        \n", "        print(f\"Successfully loaded CAD file: {file_path.name}\")\n", "        print(f\"DXF version: {doc.dxfversion}\")\n", "        \n", "        return doc, modelspace\n", "        \n", "    except ezdxf.DXFError as e:\n", "        print(f\"Error loading DXF file: {e}\")\n", "        return None, None"]}, {"cell_type": "markdown", "id": "e51d8181", "metadata": {"papermill": {"duration": 0.001372, "end_time": "2025-06-15T11:20:17.798324", "exception": false, "start_time": "2025-06-15T11:20:17.796952", "status": "completed"}, "tags": []}, "source": ["## Load and Process CAD Data"]}, {"cell_type": "markdown", "id": "225f703c", "metadata": {"tags": ["papermill-error-cell-tag"]}, "source": ["<span id=\"papermill-error-cell\" style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">Execution using papermill encountered an exception here and stopped:</span>"]}, {"cell_type": "code", "execution_count": 5, "id": "d3cc358b", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T11:20:17.801583Z", "iopub.status.busy": "2025-06-15T11:20:17.801472Z", "iopub.status.idle": "2025-06-15T11:20:17.958902Z", "shell.execute_reply": "2025-06-15T11:20:17.958495Z"}, "papermill": {"duration": 0.159733, "end_time": "2025-06-15T11:20:17.959503", "exception": true, "start_time": "2025-06-15T11:20:17.799770", "status": "failed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No CAD files found in ../../data/USA/McCarthy/raw\n", "Please place your CAD files (.dxf or .dwg) in the input directory.\n"]}, {"ename": "FileNotFoundError", "evalue": "No CAD files found in ../../data/USA/McCarthy/raw", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 7\u001b[39m\n\u001b[32m      5\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mNo CAD files found in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minput_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      6\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mPlease place your CAD files (.dxf or .dwg) in the input directory.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mNo CAD files found in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minput_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      9\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFound \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(cad_files)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m CAD file(s):\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m file_path \u001b[38;5;129;01min\u001b[39;00m cad_files:\n", "\u001b[31mFileNotFoundError\u001b[39m: No CAD files found in ../../data/USA/McCarthy/raw"]}], "source": ["# Find and load CAD files\n", "cad_files = find_cad_files()\n", "\n", "if not cad_files:\n", "    print(f\"No CAD files found in {input_path}\")\n", "    print(\"Please place your CAD files (.dxf or .dwg) in the input directory.\")\n", "    raise FileNotFoundError(f\"No CAD files found in {input_path}\")\n", "\n", "print(f\"Found {len(cad_files)} CAD file(s):\")\n", "for file_path in cad_files:\n", "    print(f\"  - {file_path.name}\")\n", "\n", "# Process the first available CAD file\n", "cad_file_path = cad_files[0]\n", "doc, modelspace = load_cad_file(cad_file_path)\n", "\n", "if doc is None:\n", "    raise ValueError(f\"Failed to load CAD file: {cad_file_path}\")\n", "\n", "# Display basic file information\n", "print(f\"\\nCAD File Analysis:\")\n", "print(f\"File: {cad_file_path.name}\")\n", "print(f\"DXF version: {doc.dxfversion}\")\n", "\n", "# Display available layers\n", "layers = list(doc.layers)\n", "print(f\"Layers ({len(layers)}): {', '.join([layer.dxf.name for layer in layers[:10]])}{'...' if len(layers) > 10 else ''}\")\n", "\n", "# Display available blocks\n", "blocks = list(doc.blocks)\n", "non_system_blocks = [block.name for block in blocks if not block.name.startswith('*')]\n", "print(f\"Blocks ({len(non_system_blocks)}): {', '.join(non_system_blocks[:10])}{'...' if len(non_system_blocks) > 10 else ''}\")\n", "\n", "# Count entity types in modelspace\n", "entity_types = {}\n", "for entity in modelspace:\n", "    entity_type = entity.dxftype()\n", "    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1\n", "\n", "print(f\"\\nEntity types in modelspace:\")\n", "for entity_type, count in sorted(entity_types.items()):\n", "    print(f\"  {entity_type}: {count}\")"]}, {"cell_type": "markdown", "id": "d7fca760", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["## Extract <PERSON><PERSON><PERSON> from INSERT Entities"]}, {"cell_type": "code", "execution_count": null, "id": "db24b067", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Extract metadata from INSERT entities\n", "elements = []\n", "insert_entities = list(modelspace.query(\"INSERT\"))\n", "\n", "print(f\"Processing {len(insert_entities)} INSERT entities...\")\n", "\n", "for entity in insert_entities:\n", "    try:\n", "        # Extract basic properties\n", "        block_name = entity.dxf.name\n", "        x, y, z = entity.dxf.insert\n", "        layer = entity.dxf.layer\n", "        \n", "        # Extract rotation and scale if available\n", "        rotation = getattr(entity.dxf, \"rotation\", 0)\n", "        scale_x = getattr(entity.dxf, \"xscale\", 1)\n", "        scale_y = getattr(entity.dxf, \"yscale\", 1)\n", "        scale_z = getattr(entity.dxf, \"zscale\", 1)\n", "        \n", "        # Use naming convention to extract element information\n", "        pile_match = re.search(pile_name_pattern, block_name)\n", "        element_name = f\"PILE:{pile_match.group(1)}\" if pile_match else block_name\n", "        element_type = \"pile\" if pile_match else \"unknown\"\n", "        \n", "        # Get entity handle (unique identifier)\n", "        handle = entity.dxf.handle\n", "        \n", "        # Extract attributes if available and requested\n", "        attributes = {}\n", "        if include_attributes and hasattr(entity, \"attribs\") and entity.attribs:\n", "            for attrib in entity.attribs:\n", "                attributes[attrib.dxf.tag] = attrib.dxf.text\n", "        \n", "        # Create standardized element metadata\n", "        element = ElementMetadata(\n", "            element_id=handle,\n", "            element_name=element_name,\n", "            element_type=element_type,\n", "            source_file=cad_file_path.name,\n", "            source_type=\"CAD\",\n", "            x_local=x,\n", "            y_local=y,\n", "            z_local=z,\n", "            rotation=rotation,\n", "            scale_x=scale_x,\n", "            scale_y=scale_y,\n", "            scale_z=scale_z,\n", "            layer_name=layer,\n", "            attributes=attributes if attributes else None,\n", "            extraction_timestamp=datetime.now().isoformat(),\n", "            coordinate_system=coordinate_system\n", "        )\n", "        \n", "        elements.append(element)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing entity {getattr(entity.dxf, 'handle', 'unknown')}: {e}\")\n", "\n", "print(f\"Extracted metadata for {len(elements)} elements\")\n", "\n", "# Convert to DataFrame\n", "if elements:\n", "    df = pd.DataFrame([element.to_dict() for element in elements])\n", "    df = MetadataSchema.standardize_dataframe(df)\n", "    \n", "    print(f\"\\nDataFrame shape: {df.shape}\")\n", "    print(f\"Element types: {df['element_type'].value_counts().to_dict()}\")\n", "    \n", "    # Display sample data\n", "    print(f\"\\nSample data:\")\n", "    display(df.head())\n", "else:\n", "    df = MetadataSchema.create_empty_dataframe()\n", "    print(\"No elements extracted\")"]}, {"cell_type": "markdown", "id": "9439dff0", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["## Coordinate Transformation"]}, {"cell_type": "code", "execution_count": null, "id": "8a0ecc06", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Transform coordinates if requested and data is available\n", "if coordinate_transform and len(df) > 0 and COORDINATE_TRANSFORM_SUPPORT:\n", "    try:\n", "        # Create coordinate transformer\n", "        transformer = Transformer.from_crs(coordinate_system, target_crs, always_xy=True)\n", "        print(f\"Transforming coordinates: {coordinate_system} -> {target_crs}\")\n", "        \n", "        # Transform coordinates for each element\n", "        transformed_coords = []\n", "        for _, row in df.iterrows():\n", "            try:\n", "                lon, lat = transformer.transform(row['x_local'], row['y_local'])\n", "                transformed_coords.append({'longitude': lon, 'latitude': lat, 'elevation': row['z_local']})\n", "            except Exception as e:\n", "                print(f\"Error transforming coordinates for element {row['element_id']}: {e}\")\n", "                transformed_coords.append({'longitude': None, 'latitude': None, 'elevation': None})\n", "        \n", "        # Add transformed coordinates to DataFrame\n", "        transform_df = pd.DataFrame(transformed_coords)\n", "        df['longitude'] = transform_df['longitude']\n", "        df['latitude'] = transform_df['latitude']\n", "        df['elevation'] = transform_df['elevation']\n", "        \n", "        print(f\"Coordinate transformation completed for {len(df)} elements\")\n", "        \n", "        # Display coordinate bounds\n", "        if df['longitude'].notna().any():\n", "            print(f\"Coordinate bounds:\")\n", "            print(f\"  Longitude: {df['longitude'].min():.6f} to {df['longitude'].max():.6f}\")\n", "            print(f\"  Latitude: {df['latitude'].min():.6f} to {df['latitude'].max():.6f}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Coordinate transformation failed: {e}\")\n", "        print(\"Continuing without coordinate transformation\")\n", "        \n", "elif coordinate_transform and not COORDINATE_TRANSFORM_SUPPORT:\n", "    print(\"Coordinate transformation requested but pyproj not available\")\n", "    print(\"Install pyproj for coordinate transformation support\")\n", "    \n", "elif len(df) == 0:\n", "    print(\"No data available for coordinate transformation\")"]}, {"cell_type": "markdown", "id": "3a86e27c", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "id": "000a31ce", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Export results in multiple formats\n", "if len(df) > 0:\n", "    # Create exporter\n", "    exporter = MetadataExporter(current_run_path, site_name, \"CAD\")\n", "    \n", "    # Prepare metadata for JSON export\n", "    extraction_metadata = {\n", "        'extraction_parameters': {\n", "            'site_name': site_name,\n", "            'project_type': project_type,\n", "            'coordinate_system': coordinate_system,\n", "            'target_crs': target_crs,\n", "            'pile_name_pattern': pile_name_pattern,\n", "            'include_attributes': include_attributes,\n", "            'include_geometry': include_geometry,\n", "            'coordinate_transform': coordinate_transform\n", "        },\n", "        'source_file_info': {\n", "            'filename': cad_file_path.name,\n", "            'file_size': cad_file_path.stat().st_size,\n", "            'dxf_version': doc.dxfversion if doc else 'unknown'\n", "        },\n", "        'processing_info': {\n", "            'total_insert_entities': len(insert_entities),\n", "            'extracted_elements': len(df),\n", "            'extraction_timestamp': timestamp\n", "        }\n", "    }\n", "    \n", "    # Export in all requested formats\n", "    exported_files = exporter.export_all_formats(df, extraction_metadata)\n", "    \n", "    print(f\"\\nExport completed:\")\n", "    for format_type, file_path in exported_files.items():\n", "        print(f\"  {format_type.upper()}: {file_path}\")\n", "    \n", "    # Also save to main preprocessing directory\n", "    main_csv_path = preprocessing_path / f\"{site_name}_cad_metadata.csv\"\n", "    df.to_csv(main_csv_path, index=False)\n", "    print(f\"  Main CSV: {main_csv_path}\")\n", "    \n", "else:\n", "    print(\"No data to export\")\n", "\n", "print(f\"\\nCAD metadata extraction completed for {site_name}\")\n", "print(f\"Results saved to: {current_run_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "papermill": {"default_parameters": {}, "duration": 6.499867, "end_time": "2025-06-15T11:20:18.176986", "environment_variables": {}, "exception": true, "input_path": "metadata_extraction_cad.ipynb", "output_path": "output_runs/McCarthy_cad_20250615_165011_executed.ipynb", "parameters": {"coordinate_system": "EPSG:32612", "coordinate_transform": true, "export_csv": true, "export_json": true, "export_parquet": true, "include_attributes": true, "include_geometry": true, "pile_name_pattern": "PILE_(\\d+)", "project_type": "USA", "site_name": "<PERSON>", "target_crs": "EPSG:4326"}, "start_time": "2025-06-15T11:20:11.677119", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}