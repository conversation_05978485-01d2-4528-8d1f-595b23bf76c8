{"cells": [{"cell_type": "markdown", "id": "d9ff5cb7", "metadata": {"tags": ["papermill-error-cell-tag"]}, "source": ["<span style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">An Exception was encountered at '<a href=\"#papermill-error-cell\">In [3]</a>'.</span>"]}, {"cell_type": "markdown", "id": "5377f76a", "metadata": {"papermill": {"duration": 0.007308, "end_time": "2025-06-15T11:19:47.461037", "exception": false, "start_time": "2025-06-15T11:19:47.453729", "status": "completed"}, "tags": []}, "source": ["# CAD Metadata Extraction\n", "\n", "This notebook extracts metadata and geometric information from CAD files (DXF/DWG) for preprocessing workflows.\n", "\n", "**Stage**: Preprocessing  \n", "**Input Data**: CAD files (.dwg, .dxf)  \n", "**Output**: Structured metadata in CSV, JSON, and Parquet formats  \n", "**Schema**: Standardized pile/element specification format  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load CAD Files**: Import .dwg/.dxf files using ezdxf\n", "2. **Extract Construction Elements**: Identify pile/column INSERT blocks\n", "3. **Geometric Analysis**: Process 3D geometry and measurements\n", "4. **Coordinate Transformation**: Convert to geographic coordinates if needed\n", "5. **Export Structured Data**: Save in multiple formats for downstream usage"]}, {"cell_type": "markdown", "id": "a0023f7b", "metadata": {"papermill": {"duration": 0.001082, "end_time": "2025-06-15T11:19:47.464418", "exception": false, "start_time": "2025-06-15T11:19:47.463336", "status": "completed"}, "tags": []}, "source": ["## Parameters"]}, {"cell_type": "code", "execution_count": 1, "id": "28b29ec7", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T11:19:47.467712Z", "iopub.status.busy": "2025-06-15T11:19:47.467491Z", "iopub.status.idle": "2025-06-15T11:19:47.471514Z", "shell.execute_reply": "2025-06-15T11:19:47.471097Z"}, "papermill": {"duration": 0.006982, "end_time": "2025-06-15T11:19:47.472669", "exception": false, "start_time": "2025-06-15T11:19:47.465687", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Trino\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "cad_file_path = \"\"  # Path to specific CAD file (optional)\n", "coordinate_system = \"EPSG:32643\"  # Source coordinate reference system\n", "target_crs = \"EPSG:4326\"  # Target CRS (WGS84 for GPS coordinates)\n", "\n", "# Extraction parameters\n", "pile_name_pattern = r\"PILE_(\\d+)\"  # Regex pattern for pile identification\n", "include_attributes = True  # Extract block attributes\n", "include_geometry = True  # Perform geometric analysis\n", "coordinate_transform = True  # Transform coordinates to target CRS\n", "\n", "# Output format options\n", "export_csv = True  # Export tabular data as CSV\n", "export_json = True  # Export hierarchical metadata as JSON\n", "export_parquet = True  # Export as Parquet for large datasets"]}, {"cell_type": "code", "execution_count": 2, "id": "944c5018", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T11:19:47.475886Z", "iopub.status.busy": "2025-06-15T11:19:47.475758Z", "iopub.status.idle": "2025-06-15T11:19:47.477919Z", "shell.execute_reply": "2025-06-15T11:19:47.477679Z"}, "papermill": {"duration": 0.004313, "end_time": "2025-06-15T11:19:47.478611", "exception": false, "start_time": "2025-06-15T11:19:47.474298", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "site_name = \"<PERSON>\"\n", "project_type = \"USA\"\n", "coordinate_system = \"EPSG:32612\"\n", "target_crs = \"EPSG:4326\"\n", "pile_name_pattern = \"PILE_(\\\\d+)\"\n", "include_attributes = True\n", "include_geometry = True\n", "coordinate_transform = True\n", "export_csv = True\n", "export_json = True\n", "export_parquet = True\n"]}, {"cell_type": "markdown", "id": "e6e012b2", "metadata": {"papermill": {"duration": 0.001057, "end_time": "2025-06-15T11:19:47.480922", "exception": false, "start_time": "2025-06-15T11:19:47.479865", "status": "completed"}, "tags": []}, "source": ["## Setup and Imports"]}, {"cell_type": "markdown", "id": "c738a094", "metadata": {"tags": ["papermill-error-cell-tag"]}, "source": ["<span id=\"papermill-error-cell\" style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">Execution using papermill encountered an exception here and stopped:</span>"]}, {"cell_type": "code", "execution_count": 3, "id": "5d20b8e6", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T11:19:47.483589Z", "iopub.status.busy": "2025-06-15T11:19:47.483502Z", "iopub.status.idle": "2025-06-15T11:19:47.575146Z", "shell.execute_reply": "2025-06-15T11:19:47.574779Z"}, "papermill": {"duration": 0.093754, "end_time": "2025-06-15T11:19:47.575703", "exception": true, "start_time": "2025-06-15T11:19:47.481949", "status": "failed"}, "tags": []}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'ezdxf'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Import libraries\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mezdxf\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'ezdxf'"]}], "source": ["# Import libraries\n", "import ezdxf\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import os\n", "import re\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import Counter\n", "import logging\n", "\n", "# Optional imports\n", "try:\n", "    import pyarrow as pa\n", "    import pyarrow.parquet as pq\n", "    PARQUET_SUPPORT = True\n", "    print(f\"Parquet support available: {pa.__version__}\")\n", "except ImportError:\n", "    print(\"Parquet support not available. Install pyarrow for Parquet export.\")\n", "    PARQUET_SUPPORT = False\n", "\n", "try:\n", "    from pyproj import Transformer\n", "    COORDINATE_TRANSFORM_SUPPORT = True\n", "    print(f\"Coordinate transformation support available\")\n", "except ImportError:\n", "    print(\"Coordinate transformation not available. Install pyproj for CRS transformation.\")\n", "    COORDINATE_TRANSFORM_SUPPORT = False\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Create output directory structure for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_cad_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Input and output paths\n", "if cad_file_path:\n", "    input_path = Path(cad_file_path)\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "\n", "preprocessing_path = data_path / project_type / site_name / 'preprocessing'\n", "preprocessing_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"CAD Metadata Extraction - Ready!\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {preprocessing_path}\")\n", "print(f\"Current run output: {current_run_path}\")\n", "print(f\"Coordinate system: {coordinate_system} -> {target_crs}\")"]}, {"cell_type": "markdown", "id": "fe3d7365", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["## Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "id": "72438cd1", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Import the standardized schema\n", "import sys\n", "sys.path.append('.')\n", "from metadata_schema import ElementMetadata, MetadataSchema, MetadataExporter, create_element_from_cad_insert\n", "\n", "def find_cad_files():\n", "    \"\"\"\n", "    Find CAD files in the input directory.\n", "    \"\"\"\n", "    if cad_file_path and Path(cad_file_path).exists():\n", "        return [Path(cad_file_path)]\n", "    \n", "    search_path = input_path if input_path.is_dir() else input_path.parent\n", "    cad_files = [\n", "        *list(search_path.glob('*.dxf')),\n", "        *list(search_path.glob('*.dwg'))\n", "    ]\n", "    \n", "    return cad_files\n", "\n", "def load_cad_file(file_path):\n", "    \"\"\"\n", "    Load CAD file using ezdxf.\n", "    \"\"\"\n", "    try:\n", "        if file_path.suffix.lower() == '.dwg':\n", "            print(f\"DWG files need to be converted to DXF format first.\")\n", "            print(f\"Please convert {file_path} to DXF format.\")\n", "            return None, None\n", "        \n", "        doc = ezdxf.readfile(str(file_path))\n", "        modelspace = doc.modelspace()\n", "        \n", "        print(f\"Successfully loaded CAD file: {file_path.name}\")\n", "        print(f\"DXF version: {doc.dxfversion}\")\n", "        \n", "        return doc, modelspace\n", "        \n", "    except ezdxf.DXFError as e:\n", "        print(f\"Error loading DXF file: {e}\")\n", "        return None, None"]}, {"cell_type": "markdown", "id": "1b50589b", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["## Load and Process CAD Data"]}, {"cell_type": "code", "execution_count": null, "id": "e65af33c", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Find and load CAD files\n", "cad_files = find_cad_files()\n", "\n", "if not cad_files:\n", "    print(f\"No CAD files found in {input_path}\")\n", "    print(\"Please place your CAD files (.dxf or .dwg) in the input directory.\")\n", "    raise FileNotFoundError(f\"No CAD files found in {input_path}\")\n", "\n", "print(f\"Found {len(cad_files)} CAD file(s):\")\n", "for file_path in cad_files:\n", "    print(f\"  - {file_path.name}\")\n", "\n", "# Process the first available CAD file\n", "cad_file_path = cad_files[0]\n", "doc, modelspace = load_cad_file(cad_file_path)\n", "\n", "if doc is None:\n", "    raise ValueError(f\"Failed to load CAD file: {cad_file_path}\")\n", "\n", "# Display basic file information\n", "print(f\"\\nCAD File Analysis:\")\n", "print(f\"File: {cad_file_path.name}\")\n", "print(f\"DXF version: {doc.dxfversion}\")\n", "\n", "# Display available layers\n", "layers = list(doc.layers)\n", "print(f\"Layers ({len(layers)}): {', '.join([layer.dxf.name for layer in layers[:10]])}{'...' if len(layers) > 10 else ''}\")\n", "\n", "# Display available blocks\n", "blocks = list(doc.blocks)\n", "non_system_blocks = [block.name for block in blocks if not block.name.startswith('*')]\n", "print(f\"Blocks ({len(non_system_blocks)}): {', '.join(non_system_blocks[:10])}{'...' if len(non_system_blocks) > 10 else ''}\")\n", "\n", "# Count entity types in modelspace\n", "entity_types = {}\n", "for entity in modelspace:\n", "    entity_type = entity.dxftype()\n", "    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1\n", "\n", "print(f\"\\nEntity types in modelspace:\")\n", "for entity_type, count in sorted(entity_types.items()):\n", "    print(f\"  {entity_type}: {count}\")"]}, {"cell_type": "markdown", "id": "d7b9972d", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["## Extract <PERSON><PERSON><PERSON> from INSERT Entities"]}, {"cell_type": "code", "execution_count": null, "id": "7510cc99", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Extract metadata from INSERT entities\n", "elements = []\n", "insert_entities = list(modelspace.query(\"INSERT\"))\n", "\n", "print(f\"Processing {len(insert_entities)} INSERT entities...\")\n", "\n", "for entity in insert_entities:\n", "    try:\n", "        # Extract basic properties\n", "        block_name = entity.dxf.name\n", "        x, y, z = entity.dxf.insert\n", "        layer = entity.dxf.layer\n", "        \n", "        # Extract rotation and scale if available\n", "        rotation = getattr(entity.dxf, \"rotation\", 0)\n", "        scale_x = getattr(entity.dxf, \"xscale\", 1)\n", "        scale_y = getattr(entity.dxf, \"yscale\", 1)\n", "        scale_z = getattr(entity.dxf, \"zscale\", 1)\n", "        \n", "        # Use naming convention to extract element information\n", "        pile_match = re.search(pile_name_pattern, block_name)\n", "        element_name = f\"PILE:{pile_match.group(1)}\" if pile_match else block_name\n", "        element_type = \"pile\" if pile_match else \"unknown\"\n", "        \n", "        # Get entity handle (unique identifier)\n", "        handle = entity.dxf.handle\n", "        \n", "        # Extract attributes if available and requested\n", "        attributes = {}\n", "        if include_attributes and hasattr(entity, \"attribs\") and entity.attribs:\n", "            for attrib in entity.attribs:\n", "                attributes[attrib.dxf.tag] = attrib.dxf.text\n", "        \n", "        # Create standardized element metadata\n", "        element = ElementMetadata(\n", "            element_id=handle,\n", "            element_name=element_name,\n", "            element_type=element_type,\n", "            source_file=cad_file_path.name,\n", "            source_type=\"CAD\",\n", "            x_local=x,\n", "            y_local=y,\n", "            z_local=z,\n", "            rotation=rotation,\n", "            scale_x=scale_x,\n", "            scale_y=scale_y,\n", "            scale_z=scale_z,\n", "            layer_name=layer,\n", "            attributes=attributes if attributes else None,\n", "            extraction_timestamp=datetime.now().isoformat(),\n", "            coordinate_system=coordinate_system\n", "        )\n", "        \n", "        elements.append(element)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing entity {getattr(entity.dxf, 'handle', 'unknown')}: {e}\")\n", "\n", "print(f\"Extracted metadata for {len(elements)} elements\")\n", "\n", "# Convert to DataFrame\n", "if elements:\n", "    df = pd.DataFrame([element.to_dict() for element in elements])\n", "    df = MetadataSchema.standardize_dataframe(df)\n", "    \n", "    print(f\"\\nDataFrame shape: {df.shape}\")\n", "    print(f\"Element types: {df['element_type'].value_counts().to_dict()}\")\n", "    \n", "    # Display sample data\n", "    print(f\"\\nSample data:\")\n", "    display(df.head())\n", "else:\n", "    df = MetadataSchema.create_empty_dataframe()\n", "    print(\"No elements extracted\")"]}, {"cell_type": "markdown", "id": "6b44aa1a", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["## Coordinate Transformation"]}, {"cell_type": "code", "execution_count": null, "id": "b03818b6", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Transform coordinates if requested and data is available\n", "if coordinate_transform and len(df) > 0 and COORDINATE_TRANSFORM_SUPPORT:\n", "    try:\n", "        # Create coordinate transformer\n", "        transformer = Transformer.from_crs(coordinate_system, target_crs, always_xy=True)\n", "        print(f\"Transforming coordinates: {coordinate_system} -> {target_crs}\")\n", "        \n", "        # Transform coordinates for each element\n", "        transformed_coords = []\n", "        for _, row in df.iterrows():\n", "            try:\n", "                lon, lat = transformer.transform(row['x_local'], row['y_local'])\n", "                transformed_coords.append({'longitude': lon, 'latitude': lat, 'elevation': row['z_local']})\n", "            except Exception as e:\n", "                print(f\"Error transforming coordinates for element {row['element_id']}: {e}\")\n", "                transformed_coords.append({'longitude': None, 'latitude': None, 'elevation': None})\n", "        \n", "        # Add transformed coordinates to DataFrame\n", "        transform_df = pd.DataFrame(transformed_coords)\n", "        df['longitude'] = transform_df['longitude']\n", "        df['latitude'] = transform_df['latitude']\n", "        df['elevation'] = transform_df['elevation']\n", "        \n", "        print(f\"Coordinate transformation completed for {len(df)} elements\")\n", "        \n", "        # Display coordinate bounds\n", "        if df['longitude'].notna().any():\n", "            print(f\"Coordinate bounds:\")\n", "            print(f\"  Longitude: {df['longitude'].min():.6f} to {df['longitude'].max():.6f}\")\n", "            print(f\"  Latitude: {df['latitude'].min():.6f} to {df['latitude'].max():.6f}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Coordinate transformation failed: {e}\")\n", "        print(\"Continuing without coordinate transformation\")\n", "        \n", "elif coordinate_transform and not COORDINATE_TRANSFORM_SUPPORT:\n", "    print(\"Coordinate transformation requested but pyproj not available\")\n", "    print(\"Install pyproj for coordinate transformation support\")\n", "    \n", "elif len(df) == 0:\n", "    print(\"No data available for coordinate transformation\")"]}, {"cell_type": "markdown", "id": "6a37dfe3", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "id": "5b2bf7f2", "metadata": {"papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# Export results in multiple formats\n", "if len(df) > 0:\n", "    # Create exporter\n", "    exporter = MetadataExporter(current_run_path, site_name, \"CAD\")\n", "    \n", "    # Prepare metadata for JSON export\n", "    extraction_metadata = {\n", "        'extraction_parameters': {\n", "            'site_name': site_name,\n", "            'project_type': project_type,\n", "            'coordinate_system': coordinate_system,\n", "            'target_crs': target_crs,\n", "            'pile_name_pattern': pile_name_pattern,\n", "            'include_attributes': include_attributes,\n", "            'include_geometry': include_geometry,\n", "            'coordinate_transform': coordinate_transform\n", "        },\n", "        'source_file_info': {\n", "            'filename': cad_file_path.name,\n", "            'file_size': cad_file_path.stat().st_size,\n", "            'dxf_version': doc.dxfversion if doc else 'unknown'\n", "        },\n", "        'processing_info': {\n", "            'total_insert_entities': len(insert_entities),\n", "            'extracted_elements': len(df),\n", "            'extraction_timestamp': timestamp\n", "        }\n", "    }\n", "    \n", "    # Export in all requested formats\n", "    exported_files = exporter.export_all_formats(df, extraction_metadata)\n", "    \n", "    print(f\"\\nExport completed:\")\n", "    for format_type, file_path in exported_files.items():\n", "        print(f\"  {format_type.upper()}: {file_path}\")\n", "    \n", "    # Also save to main preprocessing directory\n", "    main_csv_path = preprocessing_path / f\"{site_name}_cad_metadata.csv\"\n", "    df.to_csv(main_csv_path, index=False)\n", "    print(f\"  Main CSV: {main_csv_path}\")\n", "    \n", "else:\n", "    print(\"No data to export\")\n", "\n", "print(f\"\\nCAD metadata extraction completed for {site_name}\")\n", "print(f\"Results saved to: {current_run_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "papermill": {"default_parameters": {}, "duration": 1.103868, "end_time": "2025-06-15T11:19:47.691730", "environment_variables": {}, "exception": true, "input_path": "metadata_extraction_cad.ipynb", "output_path": "output_runs/McCarthy_cad_20250615_164946_executed.ipynb", "parameters": {"coordinate_system": "EPSG:32612", "coordinate_transform": true, "export_csv": true, "export_json": true, "export_parquet": true, "include_attributes": true, "include_geometry": true, "pile_name_pattern": "PILE_(\\d+)", "project_type": "USA", "site_name": "<PERSON>", "target_crs": "EPSG:4326"}, "start_time": "2025-06-15T11:19:46.587862", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}