#!/usr/bin/env python3
"""
Compare and analyze metadata extraction results from CAD and IFC sources.

This script analyzes and compares the results from CAD and IFC metadata
extraction for the same site, providing insights into data consistency
and completeness.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from datetime import datetime
import argparse

def load_metadata_csv(file_path):
    """Load metadata CSV file."""
    try:
        df = pd.read_csv(file_path)
        print(f"Loaded {len(df)} records from {file_path}")
        return df
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def load_metadata_json(file_path):
    """Load metadata JSON file."""
    try:
        with open(file_path, 'r') as f:
            metadata = json.load(f)
        return metadata
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def find_site_metadata(site_name, output_dir="output_runs"):
    """
    Find all metadata files for a specific site.
    
    Parameters:
    -----------
    site_name : str
        Name of the site to analyze
    output_dir : str
        Directory containing output runs
        
    Returns:
    --------
    dict : Dictionary of method -> list of run directories
    """
    
    output_path = Path(output_dir)
    site_runs = {
        'CAD': [],
        'IFC': []
    }
    
    # Find all directories for this site
    for run_dir in output_path.glob(f"{site_name}_*"):
        if run_dir.is_dir():
            dir_name = run_dir.name
            
            if '_cad_' in dir_name:
                site_runs['CAD'].append(run_dir)
            elif '_ifc_' in dir_name:
                site_runs['IFC'].append(run_dir)
    
    return site_runs

def extract_run_summary(run_dir, method):
    """
    Extract summary information from a run directory.
    
    Parameters:
    -----------
    run_dir : Path
        Path to run directory
    method : str
        Method type (CAD or IFC)
        
    Returns:
    --------
    dict : Dictionary of extracted summary information
    """
    
    # Find metadata files
    csv_files = list(run_dir.glob(f"*_{method.lower()}_metadata.csv"))
    json_files = list(run_dir.glob(f"*_{method.lower()}_metadata.json"))
    
    if not csv_files:
        return None
    
    # Load CSV data
    df = load_metadata_csv(csv_files[0])
    if df is None:
        return None
    
    # Load JSON metadata if available
    json_metadata = None
    if json_files:
        json_metadata = load_metadata_json(json_files[0])
    
    # Extract summary statistics
    summary = {
        'run_dir': run_dir.name,
        'method': method,
        'total_elements': len(df),
        'element_types': df['element_type'].value_counts().to_dict(),
        'coordinate_bounds': {
            'x_min': float(df['x_local'].min()) if len(df) > 0 else None,
            'x_max': float(df['x_local'].max()) if len(df) > 0 else None,
            'y_min': float(df['y_local'].min()) if len(df) > 0 else None,
            'y_max': float(df['y_local'].max()) if len(df) > 0 else None,
            'z_min': float(df['z_local'].min()) if len(df) > 0 else None,
            'z_max': float(df['z_local'].max()) if len(df) > 0 else None
        },
        'has_geographic_coords': df['longitude'].notna().any() if 'longitude' in df.columns else False,
        'has_materials': df['material_name'].notna().any() if 'material_name' in df.columns else False,
        'has_geometry': any(df[col].notna().any() for col in ['width', 'height', 'depth'] if col in df.columns),
        'source_file': df['source_file'].iloc[0] if len(df) > 0 else None,
        'extraction_timestamp': json_metadata['extraction_info']['timestamp'] if json_metadata else None
    }
    
    return summary

def compare_site_metadata(site_name, output_dir="output_runs"):
    """
    Compare metadata extraction results for a specific site.
    
    Parameters:
    -----------
    site_name : str
        Name of the site to analyze
    output_dir : str
        Directory containing output runs
        
    Returns:
    --------
    dict : Comparison results
    """
    
    site_runs = find_site_metadata(site_name, output_dir)
    comparison = {
        'site_name': site_name,
        'analysis_timestamp': datetime.now().isoformat(),
        'methods': {}
    }
    
    for method, run_dirs in site_runs.items():
        if run_dirs:
            # Use the most recent run
            latest_run = max(run_dirs, key=lambda x: x.stat().st_mtime)
            summary = extract_run_summary(latest_run, method)
            if summary:
                comparison['methods'][method] = summary
    
    return comparison

def plot_metadata_comparison(comparison_data, site_name):
    """
    Create comparison plots for metadata extraction results.
    
    Parameters:
    -----------
    comparison_data : dict
        Comparison results from compare_site_metadata
    site_name : str
        Name of the site
    """
    
    methods = list(comparison_data['methods'].keys())
    if len(methods) < 2:
        print(f"Need at least 2 methods for comparison. Found: {methods}")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'Metadata Extraction Comparison - {site_name}', fontsize=16)
    
    # Element count comparison
    element_counts = [comparison_data['methods'][method]['total_elements'] for method in methods]
    axes[0, 0].bar(methods, element_counts)
    axes[0, 0].set_title('Total Elements Extracted')
    axes[0, 0].set_ylabel('Element Count')
    
    # Coordinate bounds comparison
    bounds_data = []
    for method in methods:
        bounds = comparison_data['methods'][method]['coordinate_bounds']
        if bounds['x_min'] is not None:
            x_range = bounds['x_max'] - bounds['x_min']
            y_range = bounds['y_max'] - bounds['y_min']
            bounds_data.append({'Method': method, 'X Range': x_range, 'Y Range': y_range})
    
    if bounds_data:
        bounds_df = pd.DataFrame(bounds_data)
        x = np.arange(len(methods))
        width = 0.35
        
        axes[0, 1].bar(x - width/2, bounds_df['X Range'], width, label='X Range', alpha=0.8)
        axes[0, 1].bar(x + width/2, bounds_df['Y Range'], width, label='Y Range', alpha=0.8)
        axes[0, 1].set_title('Coordinate Range Comparison')
        axes[0, 1].set_ylabel('Range (units)')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(methods)
        axes[0, 1].legend()
    
    # Feature availability comparison
    features = ['has_geographic_coords', 'has_materials', 'has_geometry']
    feature_labels = ['Geographic Coords', 'Materials', 'Geometry']
    
    feature_data = []
    for method in methods:
        method_data = comparison_data['methods'][method]
        for feature, label in zip(features, feature_labels):
            feature_data.append({
                'Method': method,
                'Feature': label,
                'Available': method_data.get(feature, False)
            })
    
    feature_df = pd.DataFrame(feature_data)
    feature_pivot = feature_df.pivot(index='Feature', columns='Method', values='Available')
    
    sns.heatmap(feature_pivot, annot=True, cmap='RdYlGn', cbar_kws={'label': 'Available'}, ax=axes[1, 0])
    axes[1, 0].set_title('Feature Availability')
    
    # Element type distribution
    element_type_text = ""
    for method in methods:
        element_types = comparison_data['methods'][method]['element_types']
        element_type_text += f"{method}:\n"
        for elem_type, count in element_types.items():
            element_type_text += f"  {elem_type}: {count}\n"
        element_type_text += "\n"
    
    axes[1, 1].text(0.1, 0.9, element_type_text, transform=axes[1, 1].transAxes, 
                    verticalalignment='top', fontfamily='monospace', fontsize=10)
    axes[1, 1].set_title('Element Type Distribution')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    
    # Save plot
    output_path = Path(output_dir) / f"{site_name}_metadata_comparison.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Comparison plot saved to: {output_path}")
    
    plt.show()

def generate_comparison_report(site_name, output_dir="output_runs"):
    """
    Generate a comprehensive comparison report for a site.
    
    Parameters:
    -----------
    site_name : str
        Name of the site to analyze
    output_dir : str
        Directory containing output runs
    """
    
    print(f"\n=== Metadata Extraction Comparison Report ===")
    print(f"Site: {site_name}")
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    comparison_data = compare_site_metadata(site_name, output_dir)
    
    if not comparison_data['methods']:
        print(f"No metadata extraction results found for site: {site_name}")
        return
    
    # Summary statistics
    methods = list(comparison_data['methods'].keys())
    print(f"\nMethods compared: {', '.join(methods)}")
    
    # Method performance comparison
    print(f"\nMethod Performance:")
    for method in methods:
        method_data = comparison_data['methods'][method]
        print(f"\n{method}:")
        print(f"  Total elements: {method_data['total_elements']:,}")
        print(f"  Source file: {method_data['source_file']}")
        print(f"  Geographic coordinates: {'Yes' if method_data['has_geographic_coords'] else 'No'}")
        print(f"  Material information: {'Yes' if method_data['has_materials'] else 'No'}")
        print(f"  Geometric properties: {'Yes' if method_data['has_geometry'] else 'No'}")
        
        # Coordinate bounds
        bounds = method_data['coordinate_bounds']
        if bounds['x_min'] is not None:
            print(f"  Coordinate bounds:")
            print(f"    X: {bounds['x_min']:.2f} to {bounds['x_max']:.2f}")
            print(f"    Y: {bounds['y_min']:.2f} to {bounds['y_max']:.2f}")
            print(f"    Z: {bounds['z_min']:.2f} to {bounds['z_max']:.2f}")
    
    # Data consistency analysis
    if len(methods) >= 2:
        print(f"\nData Consistency Analysis:")
        
        # Compare element counts
        element_counts = [comparison_data['methods'][method]['total_elements'] for method in methods]
        if max(element_counts) > 0:
            consistency_ratio = min(element_counts) / max(element_counts)
            print(f"  Element count consistency: {consistency_ratio:.2f} ({min(element_counts)} / {max(element_counts)})")
        
        # Compare coordinate bounds
        bounds_overlap = True
        for method1, method2 in [(methods[0], methods[1])]:
            bounds1 = comparison_data['methods'][method1]['coordinate_bounds']
            bounds2 = comparison_data['methods'][method2]['coordinate_bounds']
            
            if all(b is not None for b in [bounds1['x_min'], bounds1['x_max'], bounds2['x_min'], bounds2['x_max']]):
                x_overlap = not (bounds1['x_max'] < bounds2['x_min'] or bounds2['x_max'] < bounds1['x_min'])
                y_overlap = not (bounds1['y_max'] < bounds2['y_min'] or bounds2['y_max'] < bounds1['y_min'])
                bounds_overlap = x_overlap and y_overlap
        
        print(f"  Coordinate bounds overlap: {'Yes' if bounds_overlap else 'No'}")
    
    # Recommendations
    print(f"\nRecommendations:")
    
    # Find method with most elements
    if methods:
        best_count_method = max(methods, key=lambda m: comparison_data['methods'][m]['total_elements'])
        print(f"- Most comprehensive extraction: {best_count_method} ({comparison_data['methods'][best_count_method]['total_elements']} elements)")
    
    # Find method with most features
    feature_scores = {}
    for method in methods:
        method_data = comparison_data['methods'][method]
        score = sum([
            method_data['has_geographic_coords'],
            method_data['has_materials'],
            method_data['has_geometry']
        ])
        feature_scores[method] = score
    
    if feature_scores:
        best_feature_method = max(feature_scores.keys(), key=lambda m: feature_scores[m])
        print(f"- Most feature-rich extraction: {best_feature_method} (score: {feature_scores[best_feature_method]}/3)")
    
    # Create visualization
    plot_metadata_comparison(comparison_data, site_name)
    
    # Save detailed results
    results_file = Path(output_dir) / f"{site_name}_metadata_comparison.json"
    with open(results_file, 'w') as f:
        json.dump(comparison_data, f, indent=2, default=str)
    print(f"\nDetailed results saved to: {results_file}")

def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Compare metadata extraction results')
    parser.add_argument('site_name', help='Name of the site to analyze')
    parser.add_argument('--output-dir', default='output_runs', help='Output directory (default: output_runs)')
    
    args = parser.parse_args()
    
    generate_comparison_report(args.site_name, args.output_dir)

if __name__ == "__main__":
    main()
