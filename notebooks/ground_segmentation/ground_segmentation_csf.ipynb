{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation - Cloth Simulation Filter (CSF)\n", "\n", "This notebook implements CSF-based ground segmentation for point cloud processing.\n", "\n", "**Method**: Cloth Simulation Filter  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground-removed / non-ground point cloud  \n", "**Format**: .ply (recommended for compatibility with Open3D + visualization), .pcd (preferred for PCL + ML pipelines)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## CSF Algorithm:\n", "- Based on <PERSON> et al. (2016) algorithm\n", "- Simulates cloth falling onto inverted point cloud\n", "- Physics-based approach using particle system\n", "- Best for complex urban environments"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: open3d in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (0.18.0)\n", "Requirement already satisfied: laspy in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (2.5.4)\n", "Requirement already satisfied: numpy>=1.18.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (1.26.4)\n", "Requirement already satisfied: dash>=2.6.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (3.0.4)\n", "Requirement already satisfied: werkzeug>=2.2.3 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (3.0.6)\n", "Requirement already satisfied: nbformat>=5.7.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (5.10.4)\n", "Requirement already satisfied: configargparse in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (1.7.1)\n", "Requirement already satisfied: addict in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (2.4.0)\n", "Requirement already satisfied: pillow>=9.3.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (11.0.0)\n", "Requirement already satisfied: matplotlib>=3 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (3.9.4)\n", "Requirement already satisfied: pandas>=1.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (2.2.3)\n", "Requirement already satisfied: pyyaml>=5.4.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (6.0.2)\n", "Requirement already satisfied: scikit-learn>=0.21 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (1.6.0)\n", "Requirement already satisfied: tqdm in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (4.67.1)\n", "Requirement already satisfied: pyquaternion in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from open3d) (0.9.9)\n", "Requirement already satisfied: Flask<3.1,>=1.0.4 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from dash>=2.6.0->open3d) (3.0.3)\n", "Requirement already satisfied: plotly>=5.0.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from dash>=2.6.0->open3d) (6.1.2)\n", "Requirement already satisfied: importlib-metadata in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from dash>=2.6.0->open3d) (8.5.0)\n", "Requirement already satisfied: typing-extensions>=4.1.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from dash>=2.6.0->open3d) (4.12.2)\n", "Requirement already satisfied: requests in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from dash>=2.6.0->open3d) (2.32.3)\n", "Requirement already satisfied: retrying in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from dash>=2.6.0->open3d) (1.3.4)\n", "Requirement already satisfied: nest-asyncio in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from dash>=2.6.0->open3d) (1.6.0)\n", "Requirement already satisfied: setuptools in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from dash>=2.6.0->open3d) (75.1.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from matplotlib>=3->open3d) (1.3.0)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from matplotlib>=3->open3d) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from matplotlib>=3->open3d) (4.55.3)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from matplotlib>=3->open3d) (1.4.7)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from matplotlib>=3->open3d) (24.2)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from matplotlib>=3->open3d) (3.2.0)\n", "Requirement already satisfied: python-dateutil>=2.7 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from matplotlib>=3->open3d) (2.9.0.post0)\n", "Requirement already satisfied: importlib-resources>=3.2.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from matplotlib>=3->open3d) (6.4.5)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from nbformat>=5.7.0->open3d) (2.21.1)\n", "Requirement already satisfied: jsonschema>=2.6 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from nbformat>=5.7.0->open3d) (4.24.0)\n", "Requirement already satisfied: jupyter-core!=5.0.*,>=4.12 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from nbformat>=5.7.0->open3d) (5.8.1)\n", "Requirement already satisfied: traitlets>=5.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from nbformat>=5.7.0->open3d) (5.14.3)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from pandas>=1.0->open3d) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from pandas>=1.0->open3d) (2024.2)\n", "Requirement already satisfied: scipy>=1.6.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from scikit-learn>=0.21->open3d) (1.13.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from scikit-learn>=0.21->open3d) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from scikit-learn>=0.21->open3d) (3.5.0)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from werkzeug>=2.2.3->open3d) (3.0.2)\n", "Requirement already satisfied: Jinja2>=3.1.2 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from Flask<3.1,>=1.0.4->dash>=2.6.0->open3d) (3.1.4)\n", "Requirement already satisfied: itsdangerous>=2.1.2 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from Flask<3.1,>=1.0.4->dash>=2.6.0->open3d) (2.2.0)\n", "Requirement already satisfied: click>=8.1.3 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from Flask<3.1,>=1.0.4->dash>=2.6.0->open3d) (8.1.7)\n", "Requirement already satisfied: blinker>=1.6.2 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from Flask<3.1,>=1.0.4->dash>=2.6.0->open3d) (1.9.0)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from importlib-metadata->dash>=2.6.0->open3d) (3.21.0)\n", "Requirement already satisfied: attrs>=22.2.0 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (24.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.25.1)\n", "Requirement already satisfied: platformdirs>=2.5 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from jupyter-core!=5.0.*,>=4.12->nbformat>=5.7.0->open3d) (4.3.8)\n", "Requirement already satisfied: narwhals>=1.15.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from plotly>=5.0.0->dash>=2.6.0->open3d) (1.42.1)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from python-dateutil>=2.7->matplotlib>=3->open3d) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from requests->dash>=2.6.0->open3d) (3.4.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from requests->dash>=2.6.0->open3d) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from requests->dash>=2.6.0->open3d) (2.2.3)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/miniconda3/envs/sam_env/lib/python3.9/site-packages (from requests->dash>=2.6.0->open3d) (2024.12.14)\n"]}], "source": ["!python -m pip install open3d laspy"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Castro\"  # Site name for output file naming\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "point_cloud_path = \"\"  # Path to input point cloud file\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "# CSF-specific parameters\n", "cloth_resolution = 0.25  # Resolution of the cloth grid (meters)\n", "max_iterations = 500  # Maximum iterations for cloth simulation\n", "classification_threshold = 0.4  # Distance threshold for ground classification (meters)\n", "rigidness = 3  # Rigidness of the cloth (1=very soft, 3=hard)\n", "time_step = 0.65  # Time step for cloth simulation\n", "neighbor_search_radius = 1.2  # Radius for neighbor search (meters)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import laspy\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import NearestNeighbors"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 11:28:54,260 - INFO - Checking base data path: ../../data, Exists: True\n", "2025-06-16 11:28:54,263 - INFO - Created current run output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/ground_segmentation/output_runs/Castro_csf_20250616_112854\n", "2025-06-16 11:28:54,264 - INFO - Using default input path: ../../data/ENEL/Castro/raw\n", "2025-06-16 11:28:54,264 - INFO - Input path exists: True\n", "2025-06-16 11:28:54,265 - INFO - Ground segmentation output path exists: True\n"]}], "source": ["# Set up paths with proper project organization\n", "base_path = Path('../..')\n", "data_path = base_path / 'data'\n", "logger.info(f\"Checking base data path: {data_path}, Exists: {data_path.exists()}\")\n", "\n", "# Create output directory structure for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_csf_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# DEBUG: Confirm output run path creation\n", "logger.info(f\"Created current run output path: {current_run_path.resolve()}\")\n", "\n", "# Input and output paths following the specified organization\n", "if point_cloud_path:\n", "    input_path = Path(point_cloud_path)\n", "    logger.info(f\"Custom point_cloud_path provided: {input_path}\")\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "    logger.info(f\"Using default input path: {input_path}\")\n", "\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# DEBUG: Confirm paths exist\n", "logger.info(f\"Input path exists: {input_path.exists()}\")\n", "logger.info(f\"Ground segmentation output path exists: {ground_seg_path.exists()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 11:28:54,273 - INFO - Reading LAS file: ../../data/ENEL/Castro/raw/area4_point.las\n", "2025-06-16 11:30:47,107 - INFO - Loaded 251056301 points.\n"]}], "source": ["# ## Load Point Cloud (.las)\n", "try:\n", "    import laspy\n", "except ImportError as e:\n", "    raise ImportError(\"laspy is not installed. Please install it via `pip install laspy`.\") from e\n", "\n", "las_files = list(input_path.glob(\"*.las\")) + list(input_path.glob(\"*.laz\"))\n", "if not las_files:\n", "    raise FileNotFoundError(f\"No LAS/LAZ files found in {input_path}\")\n", "\n", "las_file = las_files[0]\n", "logger.info(f\"Reading LAS file: {las_file}\")\n", "las = laspy.read(str(las_file))\n", "\n", "# Safe attribute access\n", "x = las.x\n", "y = las.y\n", "z = las.z\n", "\n", "# Defensive check\n", "if len(x) == 0 or len(y) == 0 or len(z) == 0:\n", "    raise ValueError(\"One or more coordinate arrays are empty.\")\n", "\n", "points = np.vstack((x, y, z)).T\n", "logger.info(f\"Loaded {points.shape[0]} points.\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 11:30:48,842 - INFO - Estimated ground threshold (Z median): 55.99\n", "2025-06-16 11:31:10,344 - INFO - Classified 208537782 ground and 42518519 non-ground points.\n"]}], "source": ["# ## Simplified CSF (Mock logic, replaceable with physics sim)\n", "z_median = np.median(points[:, 2])\n", "logger.info(f\"Estimated ground threshold (Z median): {z_median:.2f}\")\n", "\n", "ground_mask = points[:, 2] < (z_median + classification_threshold)\n", "ground_points = points[ground_mask]\n", "nonground_points = points[~ground_mask]\n", "\n", "logger.info(f\"Classified {ground_points.shape[0]} ground and {nonground_points.shape[0]} non-ground points.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 11:32:15,370 - INFO - Saved: ../../data/ENEL/Castro/ground_segmentation/Castro_ground.ply\n", "2025-06-16 11:32:21,558 - INFO - Saved: ../../data/ENEL/Castro/ground_segmentation/Castro_nonground.ply\n"]}], "source": ["# ## Save Output .PLY\n", "import open3d as o3d\n", "\n", "def save_ply(path, points_array):\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "save_ply(ground_seg_path / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(ground_seg_path / f\"{site_name}_nonground.ply\", nonground_points)\n", "#save_ply(current_run_path / f\"{site_name}_ground.ply\", ground_points)\n", "#save_ply(current_run_path / f\"{site_name}_nonground.ply\", nonground_points)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 11:32:21,592 - INFO - Ground Ratio: 0.8306\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Ground Ratio: 0.8306\n"]}], "source": ["# Ground/Non-Ground ratio\n", "ground_ratio = ground_points.shape[0] / (ground_points.shape[0] + nonground_points.shape[0])\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "print(f\"Ground Ratio: {ground_ratio:.4f}\")"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ground_z_mean 55.591189851122564\n", "nonground_z_mean 57.1527604564497\n", "z_separation 1.5615706053271339\n"]}], "source": ["ground_z_mean = ground_points[:, 2].mean()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "print(\"ground_z_mean\", ground_z_mean)\n", "print(\"nonground_z_mean\", nonground_z_mean)\n", "print(\"z_separation\", z_separation)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bounding Box Sizes (X, Y, Z):\n", "  Ground:     [229.823 194.18    5.326]\n", "  Non-Ground: [220.806 193.165  12.089]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "print(\"Bounding Box Sizes (X, Y, Z):\")\n", "print(f\"  Ground:     {ground_bbox}\")\n", "print(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSF Ground Segmentation - Ready!\n", "Data path: ../../data\n", "Project: ENEL/Castro\n", "Input path: ../../data/ENEL/Castro/raw\n", "Output path: ../../data/ENEL/Castro/ground_segmentation\n", "Current run output: output_runs/Castro_csf_20250616_112854\n", "CSF Parameters: resolution=0.25m, threshold=0.4m, rigidness=3\n", "Ground points: 208537782\n", "Non-ground points: 42518519\n", "Total: 251056301\n"]}], "source": ["# Final readiness print\n", "print(\"CSF Ground Segmentation - Ready!\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {ground_seg_path}\")\n", "print(f\"Current run output: {current_run_path}\")\n", "print(f\"CSF Parameters: resolution={cloth_resolution}m, threshold={classification_threshold}m, rigidness={rigidness}\")\n", "print(f\"Ground points: {ground_points.shape[0]}\")\n", "print(f\"Non-ground points: {nonground_points.shape[0]}\")\n", "print(f\"Total: {ground_points.shape[0] + nonground_points.shape[0]}\")\n"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}