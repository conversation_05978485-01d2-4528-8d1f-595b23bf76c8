# Papermill parameters - these will be injected by Papermill
site_name = "Castro"  # Site name for output file naming
buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)
point_cloud_path = ""  # Path to input point cloud file
project_type = "ENEL"  # Options: "ENEL", "USA"

# RANSAC-specific parameters
distance_threshold = 0.2  # Maximum distance for a point to be considered an inlier (meters)
num_iterations = 1000     # Number of RANSAC iterations to perform
min_inliers_ratio = 0.05  # Minimum ratio of inliers to accept a plane
early_stop_ratio = 0.6    # Ratio of inliers to total points for early stopping

# Processing parameters
max_points_processing = 1000000  # Maximum points to process (for performance)
random_seed = 42                 # Random seed for reproducible results

# Import libraries
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import logging
from pathlib import Path
from datetime import datetime
from tqdm import tqdm

# Set random seed for reproducible results
np.random.seed(random_seed)

print(f"Libraries imported successfully. Random seed set to {random_seed}")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info(f"RANSAC Ground Segmentation initialized for site: {site_name}")
logger.info(f"Parameters - Distance threshold: {distance_threshold}m, Iterations: {num_iterations}")

# Save parameters to JSON for reproducibility
parameters = {
    "run_info": {
        "timestamp": timestamp,
        "site_name": site_name,
        "project_type": project_type,
        "method": "RANSAC",
        "notebook_version": "1.1"
    },
    "ransac_parameters": {
        "distance_threshold": distance_threshold,
        "num_iterations": num_iterations,
        "min_inliers_ratio": min_inliers_ratio,
        "early_stop_ratio": early_stop_ratio
    },
    "processing_parameters": {
        "max_points_processing": max_points_processing,
        "buffer_radius": buffer_radius,
        "visualization_enabled": visualization_enabled
    },
    "paths": {
        "input_path": str(input_path),
        "output_path": str(ground_seg_path),
        "run_output_path": str(current_run_path)
    }
}

# Save parameters to file
params_file = current_run_path / "parameters.json"
with open(params_file, 'w') as f:
    json.dump(parameters, f, indent=2)

print(f"Parameters saved to: {params_file}")

# Set up paths with proper project organization
base_path = Path('../..')
data_path = base_path / 'data'
logger.info(f"Checking base data path: {data_path}, Exists: {data_path.exists()}")

# Create output directory structure for this run with method-specific naming
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_runs_path = Path('output_runs')
current_run_path = output_runs_path / f'{site_name}_ransac_{timestamp}'
current_run_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Created current run output path: {current_run_path.resolve()}")

# Input and output paths following the specified organization
if point_cloud_path:
    input_path = Path(point_cloud_path)
    logger.info(f"Custom point_cloud_path provided: {input_path}")
    if not input_path.exists():
        raise FileNotFoundError(f"Input path does not exist: {input_path}")
else:
    raw_path = data_path / project_type / site_name / 'raw'
    input_path = raw_path
    logger.info(f"Using default input path: {input_path}")

ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
ground_seg_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Input path exists: {input_path.exists()}")
logger.info(f"Ground segmentation output path exists: {ground_seg_path.exists()}")

# Load Point Cloud data
try:
    import laspy
except ImportError as e:
    raise ImportError("laspy is not installed. Please install it via `pip install laspy`.") from e

las_files = list(input_path.glob("*.las")) + list(input_path.glob("*.laz"))
if not las_files:
    raise FileNotFoundError(f"No LAS/LAZ files found in {input_path}")

las_file = las_files[0]
logger.info(f"Reading LAS file: {las_file}")
las = laspy.read(str(las_file))

# Extract coordinate arrays
x = las.x
y = las.y
z = las.z

# Validate data integrity
if len(x) == 0 or len(y) == 0 or len(z) == 0:
    raise ValueError("One or more coordinate arrays are empty.")

# Create point array
points = np.vstack((x, y, z)).T
original_point_count = points.shape[0]
logger.info(f"Loaded {original_point_count:,} points from {las_file.name}")

# Display basic statistics
print(f"Point cloud statistics:")
print(f"  Total points: {original_point_count:,}")
print(f"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)")
print(f"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)")
print(f"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)")

import numpy as np
import time
from tqdm import tqdm

print(f"Running RANSAC ground detection...")
print(f"Parameters: threshold={distance_threshold}m, iterations={num_iterations}, min_ratio={min_inliers_ratio}")

# Downsample first
points = points[np.random.choice(points.shape[0], size=min(1_000_000, points.shape[0]), replace=False)]

# Recompute point count AFTER downsampling
n_points = points.shape[0]
min_inliers = int(n_points * min_inliers_ratio)

best_plane_params = None
best_inliers = []
max_inliers = 0

start_time = time.time()

for i in tqdm(range(num_iterations), desc="RANSAC iterations"):
    # Randomly sample 3 points
    sample_indices = np.random.choice(n_points, 3, replace=False)
    p1, p2, p3 = points[sample_indices]

    # Compute plane normal
    v1 = p2 - p1
    v2 = p3 - p1
    normal = np.cross(v1, v2)

    norm = np.linalg.norm(normal)
    if norm < 1e-6:
        continue  # Skip degenerate planes

    normal = normal / norm

    # Enforce upward-facing normal
    if normal[2] < 0:
        normal = -normal

    # Plane equation: ax + by + cz + d = 0
    d = -np.dot(normal, p1)
    plane_params = np.append(normal, d)

    # Distance of all points to the plane
    distances = np.abs(np.dot(points, plane_params[:3]) + d)

    # Find inliers within threshold
    inliers = np.where(distances < distance_threshold)[0]
    n_inliers = len(inliers)

    if n_inliers > max_inliers and n_inliers >= min_inliers:
        best_plane_params = plane_params
        best_inliers = inliers
        max_inliers = n_inliers

        inlier_ratio = n_inliers / n_points
        if inlier_ratio > early_stop_ratio:
            print(f"Early stopping at iteration {i+1}: Found {n_inliers:,} ground points ({inlier_ratio:.2f} ratio)")
            break

end_time = time.time()

if best_plane_params is not None:
    print(f"RANSAC completed in {end_time - start_time:.2f} seconds")
    print(f"Ground points: {max_inliers:,} ({max_inliers / n_points:.2f} ratio)")
    print(f"Plane: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0")

    ground_points = points[best_inliers]
    nonground_points = np.delete(points, best_inliers, axis=0)
else:
    raise ValueError("RANSAC failed to find a valid ground plane.")


# Save segmented point clouds with method-specific naming
import open3d as o3d

def save_ply(path, points_array, method_name=""):
    """Save point cloud with method-specific naming for comparison."""
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(path), pc)
    logger.info(f"Saved: {path}")

# Save with RANSAC method identifier
method_name = "ransac"
save_ply(ground_seg_path / f"{site_name}_{method_name}_ground.ply", ground_points, method_name)
save_ply(ground_seg_path / f"{site_name}_{method_name}_nonground.ply", nonground_points, method_name)

# Also save to current run directory for this specific execution
save_ply(current_run_path / f"{site_name}_{method_name}_ground.ply", ground_points, method_name)
save_ply(current_run_path / f"{site_name}_{method_name}_nonground.ply", nonground_points, method_name)

print(f"\nRANSAC segmentation outputs saved:")
print(f"  Ground points: {len(ground_points):,}")
print(f"  Non-ground points: {len(nonground_points):,}")
print(f"  Method identifier: {method_name}")

# Calculate ground to non-ground ratio
ground_ratio = ground_points.shape[0] / (ground_points.shape[0] + nonground_points.shape[0])
logger.info(f"RANSAC Ground Ratio: {ground_ratio:.4f}")
print(f"RANSAC Ground Ratio: {ground_ratio:.4f}")
print(f"\nRANSAC Segmentation Summary:")
print(f"  Total points processed: {ground_points.shape[0] + nonground_points.shape[0]:,}")
print(f"  Ground points: {ground_points.shape[0]:,} ({ground_ratio:.1%})")
print(f"  Non-ground points: {nonground_points.shape[0]:,} ({1-ground_ratio:.1%})")

# Calculate elevation statistics for ground and non-ground points
ground_z_mean = ground_points[:, 2].mean()
ground_z_std = ground_points[:, 2].std()
nonground_z_mean = nonground_points[:, 2].mean()
nonground_z_std = nonground_points[:, 2].std()
z_separation = nonground_z_mean - ground_z_mean

print(f"\nRANSAC Elevation Analysis:")
print(f"  Ground elevation - Mean: {ground_z_mean:.3f}m, Std: {ground_z_std:.3f}m")
print(f"  Non-ground elevation - Mean: {nonground_z_mean:.3f}m, Std: {nonground_z_std:.3f}m")
print(f"  Vertical separation: {z_separation:.3f}m")

# Calculate elevation ranges
ground_z_range = ground_points[:, 2].max() - ground_points[:, 2].min()
nonground_z_range = nonground_points[:, 2].max() - nonground_points[:, 2].min()
print(f"  Ground elevation range: {ground_z_range:.3f}m")
print(f"  Non-ground elevation range: {nonground_z_range:.3f}m")

# Bounding Box Stats
def bounding_box_stats(points):
    min_bound = np.min(points, axis=0)
    max_bound = np.max(points, axis=0)
    return max_bound - min_bound

ground_bbox = bounding_box_stats(ground_points)
nonground_bbox = bounding_box_stats(nonground_points)

print("Bounding Box Sizes (X, Y, Z):")
print(f"  Ground:     {ground_bbox}")
print(f"  Non-Ground: {nonground_bbox}")

# Height (Z) Distribution Plot
plt.figure(figsize=(10, 5))
plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')
plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')
plt.legend()
plt.title("Z-Height Distribution")
plt.xlabel("Z (Elevation)")
plt.ylabel("Point Count")
plt.grid(True)
plt.show()

# Final readiness print
print("RANSAC Ground Segmentation - Ready!")
print(f"Data path: {data_path}")
print(f"Project: {project_type}/{site_name}")
print(f"Input path: {input_path}")
print(f"Output path: {ground_seg_path}")
print(f"Current run output: {current_run_path}")
print(f"RANSAC Parameters: threshold={distance_threshold}m, iterations={num_iterations}")

# Visualize the results
def show_pointcloud(points, color):
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.paint_uniform_color(color)
    return pcd

ground_vis = show_pointcloud(ground_points, [0.2, 0.8, 0.2])
nonground_vis = show_pointcloud(nonground_points, [0.8, 0.2, 0.2])

o3d.visualization.draw_geometries([ground_vis, nonground_vis])
