{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation: RANSAC + PMF Sequential Processing\n", "\n", "**Date**: June 2025  \n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Overview\n", "\n", "This notebook implements a sequential ground segmentation approach that combines RANSAC and Progressive Morphological Filter (PMF) methods:\n", "\n", "1. **RANSAC First**: Detects the primary ground plane using RANSAC\n", "2. **PMF Refinement**: Applies PMF to the non-ground points to recover additional ground points\n", "3. **Combined Results**: Merges both ground classifications for improved accuracy\n", "\n", "## Benefits of Sequential Approach\n", "\n", "- **RANSAC**: Excellent for detecting dominant planar ground surfaces\n", "- **PMF**: Better at handling terrain variations and non-planar ground features\n", "- **Combined**: Leverages strengths of both methods for comprehensive ground detection\n", "\n", "## Parameters Tracking\n", "\n", "All parameters are saved to JSON files for reproducibility and comparison across runs."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters cell\n", "# These parameters can be overridden when running with papermill\n", "\n", "# Site and project configuration\n", "site_name = \"Castro\"  # Site identifier\n", "data_path = \"../../data\"  # Base data directory path\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "point_cloud_path = \"\"  # Path to input point cloud file\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "# RANSAC parameters\n", "ransac_distance_threshold = 0.2  # Maximum distance for a point to be considered an inlier (meters)\n", "ransac_num_iterations = 1000     # Number of RANSAC iterations to perform\n", "ransac_min_inliers_ratio = 0.05  # Minimum ratio of inliers to accept a plane\n", "ransac_early_stop_ratio = 0.6    # Ratio of inliers to total points for early stopping\n", "\n", "# PMF parameters\n", "pmf_cell_size = 1.0  # Grid cell size for rasterization (meters)\n", "pmf_max_window_size = 33  # Maximum window size for morphological operations\n", "pmf_slope = 0.15  # Slope parameter for terrain (radians)\n", "pmf_max_distance = 2.5  # Maximum distance threshold (meters)\n", "pmf_initial_distance = 0.5  # Initial distance threshold (meters)\n", "\n", "# Processing parameters\n", "max_points_processing = 1000000  # Maximum points to process (for performance)\n", "visualization_enabled = True     # Enable 3D visualizations\n", "save_intermediate_results = True # Save intermediate point clouds\n", "\n", "# Output parameters\n", "save_ground_points = True        # Save ground points to PLY file\n", "save_nonground_points = True     # Save non-ground points to PLY file\n", "generate_summary_stats = True    # Generate summary statistics\n", "create_visualizations = True     # Create and save visualization plots"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "from scipy import ndimage\n", "from scipy.ndimage import grey_erosion, grey_dilation\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(f\"RANSAC+PMF Ground Segmentation initialized for site: {site_name}\")\n", "logger.info(f\"RANSAC Parameters - Distance threshold: {ransac_distance_threshold}m, Iterations: {ransac_num_iterations}\")\n", "logger.info(f\"PMF Parameters - Cell size: {pmf_cell_size}m, Max window: {pmf_max_window_size}, Slope: {pmf_slope}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup paths and create output directories\n", "data_path = Path(data_path)\n", "input_path = data_path / project_type / site_name / \"raw\"\n", "ground_seg_path = data_path / project_type / site_name / \"ground_segmentation\"\n", "\n", "# Create timestamped output directory\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "current_run_path = Path(f\"output_runs/{site_name}_ransac_pmf_{timestamp}\")\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Create output directories\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {ground_seg_path}\")\n", "print(f\"Current run output: {current_run_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save parameters to JSON for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"RANSAC+PMF Sequential\",\n", "        \"notebook_version\": \"1.0\"\n", "    },\n", "    \"ransac_parameters\": {\n", "        \"distance_threshold\": ransac_distance_threshold,\n", "        \"num_iterations\": ransac_num_iterations,\n", "        \"min_inliers_ratio\": ransac_min_inliers_ratio,\n", "        \"early_stop_ratio\": ransac_early_stop_ratio\n", "    },\n", "    \"pmf_parameters\": {\n", "        \"cell_size\": pmf_cell_size,\n", "        \"max_window_size\": pmf_max_window_size,\n", "        \"slope\": pmf_slope,\n", "        \"max_distance\": pmf_max_distance,\n", "        \"initial_distance\": pmf_initial_distance\n", "    },\n", "    \"processing_parameters\": {\n", "        \"max_points_processing\": max_points_processing,\n", "        \"buffer_radius\": buffer_radius,\n", "        \"visualization_enabled\": visualization_enabled,\n", "        \"save_intermediate_results\": save_intermediate_results\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(current_run_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = current_run_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {params_file}\")\n", "print(\"\\nRun Configuration:\")\n", "print(f\"  RANSAC: threshold={ransac_distance_threshold}m, iterations={ransac_num_iterations}\")\n", "print(f\"  PMF: cell_size={pmf_cell_size}m, max_window={pmf_max_window_size}, slope={pmf_slope}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading and Preprocessing\n", "\n", "Load point cloud data and prepare for processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load point cloud data\n", "def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from PLY file.\"\"\"\n", "    points = []\n", "    with open(file_path, 'r') as f:\n", "        # Skip header\n", "        for line in f:\n", "            if line.startswith('end_header'):\n", "                break\n", "        \n", "        # Read points\n", "        for line in f:\n", "            parts = line.strip().split()\n", "            if len(parts) >= 3:\n", "                x, y, z = float(parts[0]), float(parts[1]), float(parts[2])\n", "                points.append([x, y, z])\n", "    \n", "    return np.array(points)\n", "\n", "# Find and load point cloud file\n", "if point_cloud_path:\n", "    pc_file = Path(point_cloud_path)\n", "else:\n", "    # Auto-detect point cloud file\n", "    ply_files = list(input_path.glob(\"*.ply\"))\n", "    if not ply_files:\n", "        raise FileNotFoundError(f\"No PLY files found in {input_path}\")\n", "    pc_file = ply_files[0]\n", "\n", "print(f\"Loading point cloud: {pc_file}\")\n", "points = load_point_cloud(pc_file)\n", "original_point_count = len(points)\n", "\n", "print(f\"Loaded {original_point_count:,} points\")\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "\n", "# Downsample if necessary\n", "if len(points) > max_points_processing:\n", "    indices = np.random.choice(len(points), max_points_processing, replace=False)\n", "    points = points[indices]\n", "    print(f\"Downsampled to {len(points):,} points for processing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: RANSAC Ground Detection\n", "\n", "Apply RANSAC to detect the primary ground plane."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ransac_ground_detection(points, distance_threshold, num_iterations, min_inliers_ratio, early_stop_ratio):\n", "    \"\"\"RANSAC-based ground plane detection.\"\"\"\n", "    n_points = len(points)\n", "    min_inliers = int(n_points * min_inliers_ratio)\n", "    early_stop_inliers = int(n_points * early_stop_ratio)\n", "    \n", "    best_plane_params = None\n", "    best_inliers = []\n", "    max_inliers = 0\n", "    \n", "    start_time = time.time()\n", "    \n", "    for i in tqdm(range(num_iterations), desc=\"RANSAC iterations\"):\n", "        # Randomly sample 3 points\n", "        sample_indices = np.random.choice(n_points, 3, replace=False)\n", "        p1, p2, p3 = points[sample_indices]\n", "        \n", "        # Compute plane normal\n", "        v1 = p2 - p1\n", "        v2 = p3 - p1\n", "        normal = np.cross(v1, v2)\n", "        \n", "        # Skip if degenerate\n", "        if np.linalg.norm(normal) < 1e-6:\n", "            continue\n", "        \n", "        normal = normal / np.linalg.norm(normal)\n", "        \n", "        # Ensure upward-facing normal\n", "        if normal[2] < 0:\n", "            normal = -normal\n", "        \n", "        # Compute plane equation: ax + by + cz + d = 0\n", "        d = -np.dot(normal, p1)\n", "        plane_params = np.append(normal, d)\n", "        \n", "        # Distance of all points to the plane\n", "        distances = np.abs(np.dot(points, plane_params[:3]) + d)\n", "        \n", "        # Find inliers within threshold\n", "        inliers = np.where(distances < distance_threshold)[0]\n", "        n_inliers = len(inliers)\n", "        \n", "        if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "            best_plane_params = plane_params\n", "            best_inliers = inliers\n", "            max_inliers = n_inliers\n", "            \n", "            # Early stopping if we have enough inliers\n", "            if n_inliers >= early_stop_inliers:\n", "                print(f\"Early stopping at iteration {i+1} with {n_inliers} inliers\")\n", "                break\n", "    \n", "    elapsed_time = time.time() - start_time\n", "    \n", "    return best_plane_params, best_inliers, elapsed_time\n", "\n", "# Run RANSAC ground detection\n", "print(f\"Running RANSAC ground detection...\")\n", "print(f\"Parameters: threshold={ransac_distance_threshold}m, iterations={ransac_num_iterations}, min_ratio={ransac_min_inliers_ratio}\")\n", "\n", "plane_params, ransac_ground_indices, ransac_time = ransac_ground_detection(\n", "    points, ransac_distance_threshold, ransac_num_iterations, \n", "    ransac_min_inliers_ratio, ransac_early_stop_ratio\n", ")\n", "\n", "if plane_params is None:\n", "    raise RuntimeError(\"RANSAC failed to find a valid ground plane\")\n", "\n", "# Create ground/non-ground masks\n", "ransac_ground_mask = np.zeros(len(points), dtype=bool)\n", "ransac_ground_mask[ransac_ground_indices] = True\n", "ransac_nonground_mask = ~ransac_ground_mask\n", "\n", "ransac_ground_points = points[ransac_ground_mask]\n", "ransac_nonground_points = points[ransac_nonground_mask]\n", "\n", "print(f\"\\nRANSAC Results:\")\n", "print(f\"  Processing time: {ransac_time:.2f} seconds\")\n", "print(f\"  Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"  Non-ground points: {len(ransac_nonground_points):,} ({len(ransac_nonground_points)/len(points)*100:.1f}%)\")\n", "print(f\"  Plane equation: {plane_params[0]:.3f}x + {plane_params[1]:.3f}y + {plane_params[2]:.3f}z + {plane_params[3]:.3f} = 0\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: PMF Processing on Non-Ground Points\n", "\n", "Apply Progressive Morphological Filter to the non-ground points from RANSAC to recover additional ground points."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pmf_ground_detection(points, cell_size, max_window_size, slope):\n", "    \"\"\"Progressive Morphological Filter for ground detection.\"\"\"\n", "    if len(points) == 0:\n", "        return np.array([], dtype=bool)\n", "    \n", "    # Grid the point cloud (2D raster)\n", "    min_xy = np.min(points[:, :2], axis=0)\n", "    max_xy = np.max(points[:, :2], axis=0)\n", "    dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)\n", "    \n", "    if dims[0] <= 0 or dims[1] <= 0:\n", "        return np.array([False] * len(points))\n", "    \n", "    grid = np.full(dims, np.nan)\n", "    \n", "    # Populate raster with lowest Z value per cell\n", "    for i, (x, y, z) in enumerate(points):\n", "        xi = int((x - min_xy[0]) / cell_size)\n", "        yi = int((y - min_xy[1]) / cell_size)\n", "        if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "            if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:\n", "                grid[xi, yi] = z\n", "    \n", "    # Fill holes\n", "    filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)\n", "    \n", "    # Morphological opening (erosion then dilation)\n", "    opened = grey_dilation(grey_erosion(filled_grid, size=max_window_size), size=max_window_size)\n", "    \n", "    # Ground mask based on slope threshold\n", "    z_diff = filled_grid - opened\n", "    ground_mask_2d = z_diff < slope\n", "    \n", "    # Reconstruct full ground point mask\n", "    ground_mask = []\n", "    for x, y, z in points:\n", "        xi = int((x - min_xy[0]) / cell_size)\n", "        yi = int((y - min_xy[1]) / cell_size)\n", "        if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "            ground_mask.append(ground_mask_2d[xi, yi])\n", "        else:\n", "            ground_mask.append(False)\n", "    \n", "    return np.array(ground_mask)\n", "\n", "# Apply PMF to non-ground points from RANSAC\n", "print(f\"\\nApplying PMF to {len(ransac_nonground_points):,} non-ground points from RANSAC...\")\n", "print(f\"PMF Parameters: cell_size={pmf_cell_size}m, max_window={pmf_max_window_size}, slope={pmf_slope}\")\n", "\n", "pmf_start_time = time.time()\n", "pmf_ground_mask_subset = pmf_ground_detection(\n", "    ransac_nonground_points, pmf_cell_size, pmf_max_window_size, pmf_slope\n", ")\n", "pmf_time = time.time() - pmf_start_time\n", "\n", "# Extract additional ground points found by PMF\n", "pmf_additional_ground_points = ransac_nonground_points[pmf_ground_mask_subset]\n", "pmf_remaining_nonground_points = ransac_nonground_points[~pmf_ground_mask_subset]\n", "\n", "print(f\"\\nPMF Results:\")\n", "print(f\"  Processing time: {pmf_time:.2f} seconds\")\n", "print(f\"  Additional ground points found: {len(pmf_additional_ground_points):,}\")\n", "print(f\"  Remaining non-ground points: {len(pmf_remaining_nonground_points):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Combine Results\n", "\n", "Merge <PERSON> and PMF results for final ground segmentation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine RANSAC ground points with PMF additional ground points\n", "combined_ground_points = np.vstack([\n", "    ransac_ground_points,\n", "    pmf_additional_ground_points\n", "])\n", "\n", "combined_nonground_points = pmf_remaining_nonground_points\n", "\n", "# Calculate final statistics\n", "total_processing_time = ransac_time + pmf_time\n", "final_ground_ratio = len(combined_ground_points) / len(points)\n", "final_nonground_ratio = len(combined_nonground_points) / len(points)\n", "\n", "print(f\"\\nCombined Results (RANSAC + PMF):\")\n", "print(f\"  Total processing time: {total_processing_time:.2f} seconds\")\n", "print(f\"  Final ground points: {len(combined_ground_points):,} ({final_ground_ratio*100:.1f}%)\")\n", "print(f\"  Final non-ground points: {len(combined_nonground_points):,} ({final_nonground_ratio*100:.1f}%)\")\n", "print(f\"  \\nBreakdown:\")\n", "print(f\"    RANSAC ground: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"    PMF additional: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"    Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)\")\n", "\n", "# Save results summary\n", "results_summary = {\n", "    \"processing_info\": {\n", "        \"total_points\": len(points),\n", "        \"original_points\": original_point_count,\n", "        \"processing_time_seconds\": total_processing_time,\n", "        \"ransac_time_seconds\": ransac_time,\n", "        \"pmf_time_seconds\": pmf_time\n", "    },\n", "    \"ransac_results\": {\n", "        \"ground_points\": len(ransac_ground_points),\n", "        \"ground_ratio\": len(ransac_ground_points) / len(points),\n", "        \"plane_equation\": plane_params.tolist() if plane_params is not None else None\n", "    },\n", "    \"pmf_results\": {\n", "        \"additional_ground_points\": len(pmf_additional_ground_points),\n", "        \"additional_ground_ratio\": len(pmf_additional_ground_points) / len(points)\n", "    },\n", "    \"combined_results\": {\n", "        \"final_ground_points\": len(combined_ground_points),\n", "        \"final_nonground_points\": len(combined_nonground_points),\n", "        \"final_ground_ratio\": final_ground_ratio,\n", "        \"final_nonground_ratio\": final_nonground_ratio\n", "    }\n", "}\n", "\n", "# Save results to JSON\n", "results_file = current_run_path / \"results_summary.json\"\n", "with open(results_file, 'w') as f:\n", "    json.dump(results_summary, f, indent=2)\n", "\n", "print(f\"\\nResults summary saved to: {results_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Save Results\n", "\n", "Save the segmented point clouds and processing results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_points_to_ply(points, filename, colors=None):\n", "    \"\"\"Save points to PLY file with optional colors.\"\"\"\n", "    with open(filename, 'w') as f:\n", "        # Write header\n", "        f.write(\"ply\\n\")\n", "        f.write(\"format ascii 1.0\\n\")\n", "        f.write(f\"element vertex {len(points)}\\n\")\n", "        f.write(\"property float x\\n\")\n", "        f.write(\"property float y\\n\")\n", "        f.write(\"property float z\\n\")\n", "        if colors is not None:\n", "            f.write(\"property uchar red\\n\")\n", "            f.write(\"property uchar green\\n\")\n", "            f.write(\"property uchar blue\\n\")\n", "        f.write(\"end_header\\n\")\n", "        \n", "        # Write points\n", "        for i, point in enumerate(points):\n", "            if colors is not None:\n", "                f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {colors[i][0]} {colors[i][1]} {colors[i][2]}\\n\")\n", "            else:\n", "                f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f}\\n\")\n", "\n", "# Save ground and non-ground points\n", "if save_ground_points:\n", "    ground_file = ground_seg_path / \"ransac_pmf_ground.ply\"\n", "    save_points_to_ply(combined_ground_points, ground_file)\n", "    print(f\"Ground points saved to: {ground_file}\")\n", "    \n", "    # Also save to run directory\n", "    run_ground_file = current_run_path / \"ground_points.ply\"\n", "    save_points_to_ply(combined_ground_points, run_ground_file)\n", "\n", "if save_nonground_points:\n", "    nonground_file = ground_seg_path / \"ransac_pmf_nonground.ply\"\n", "    save_points_to_ply(combined_nonground_points, nonground_file)\n", "    print(f\"Non-ground points saved to: {nonground_file}\")\n", "    \n", "    # Also save to run directory\n", "    run_nonground_file = current_run_path / \"nonground_points.ply\"\n", "    save_points_to_ply(combined_nonground_points, run_nonground_file)\n", "\n", "# Save intermediate results if requested\n", "if save_intermediate_results:\n", "    # RANSAC-only results\n", "    ransac_ground_file = current_run_path / \"ransac_only_ground.ply\"\n", "    ransac_nonground_file = current_run_path / \"ransac_only_nonground.ply\"\n", "    save_points_to_ply(ransac_ground_points, ransac_ground_file)\n", "    save_points_to_ply(ransac_nonground_points, ransac_nonground_file)\n", "    \n", "    # PMF additional results\n", "    if len(pmf_additional_ground_points) > 0:\n", "        pmf_additional_file = current_run_path / \"pmf_additional_ground.ply\"\n", "        save_points_to_ply(pmf_additional_ground_points, pmf_additional_file)\n", "    \n", "    print(f\"Intermediate results saved to: {current_run_path}\")\n", "\n", "print(f\"\\nAll results saved successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Visualization and Analysis\n", "\n", "Create visualizations to compare the different segmentation results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if create_visualizations:\n", "    # Create comparison visualization\n", "    fig = plt.figure(figsize=(20, 15))\n", "    \n", "    # Subsample for visualization\n", "    vis_sample_size = 50000\n", "    if len(points) > vis_sample_size:\n", "        vis_indices = np.random.choice(len(points), vis_sample_size, replace=False)\n", "        vis_points = points[vis_indices]\n", "        vis_ransac_ground = ransac_ground_mask[vis_indices]\n", "        \n", "        # Create combined mask for visualization\n", "        vis_combined_ground = np.zeros(len(vis_points), dtype=bool)\n", "        \n", "        # <PERSON> ground points\n", "        vis_combined_ground[vis_ransac_ground] = True\n", "        \n", "        # <PERSON> additional ground points\n", "        for i, point in enumerate(vis_points):\n", "            if not vis_ransac_ground[i]:  # Only check non-RANSAC points\n", "                # Check if this point is in PMF additional ground\n", "                distances = np.linalg.norm(pmf_additional_ground_points - point, axis=1)\n", "                if len(distances) > 0 and np.min(distances) < 0.1:  # Small tolerance\n", "                    vis_combined_ground[i] = True\n", "    else:\n", "        vis_points = points\n", "        vis_ransac_ground = ransac_ground_mask\n", "        vis_combined_ground = np.zeros(len(points), dtype=bool)\n", "        vis_combined_ground[ransac_ground_indices] = True\n", "        \n", "        # Add PMF points\n", "        for point in pmf_additional_ground_points:\n", "            distances = np.linalg.norm(points - point, axis=1)\n", "            closest_idx = np.argmin(distances)\n", "            if distances[closest_idx] < 0.1:\n", "                vis_combined_ground[closest_idx] = True\n", "    \n", "    # Plot 1: Original point cloud\n", "    ax1 = fig.add_subplot(2, 3, 1, projection='3d')\n", "    ax1.scatter(vis_points[:, 0], vis_points[:, 1], vis_points[:, 2], \n", "                c=vis_points[:, 2], cmap='viridis', s=1, alpha=0.6)\n", "    ax1.set_title('Original Point Cloud')\n", "    ax1.set_xlabel('X')\n", "    ax1.set_ylabel('Y')\n", "    ax1.set_zlabel('Z')\n", "    \n", "    # Plot 2: RANSAC results\n", "    ax2 = fig.add_subplot(2, 3, 2, projection='3d')\n", "    ground_vis = vis_points[vis_ransac_ground]\n", "    nonground_vis = vis_points[~vis_ransac_ground]\n", "    if len(ground_vis) > 0:\n", "        ax2.scatter(ground_vis[:, 0], ground_vis[:, 1], ground_vis[:, 2], \n", "                    c='brown', s=1, alpha=0.6, label='Ground')\n", "    if len(nonground_vis) > 0:\n", "        ax2.scatter(nonground_vis[:, 0], nonground_vis[:, 1], nonground_vis[:, 2], \n", "                    c='green', s=1, alpha=0.6, label='Non-ground')\n", "    ax2.set_title('RANSAC Results')\n", "    ax2.set_xlabel('X')\n", "    ax2.set_ylabel('Y')\n", "    ax2.set_zlabel('Z')\n", "    ax2.legend()\n", "    \n", "    # Plot 3: Combined results\n", "    ax3 = fig.add_subplot(2, 3, 3, projection='3d')\n", "    combined_ground_vis = vis_points[vis_combined_ground]\n", "    combined_nonground_vis = vis_points[~vis_combined_ground]\n", "    if len(combined_ground_vis) > 0:\n", "        ax3.scatter(combined_ground_vis[:, 0], combined_ground_vis[:, 1], combined_ground_vis[:, 2], \n", "                    c='brown', s=1, alpha=0.6, label='Ground')\n", "    if len(combined_nonground_vis) > 0:\n", "        ax3.scatter(combined_nonground_vis[:, 0], combined_nonground_vis[:, 1], combined_nonground_vis[:, 2], \n", "                    c='green', s=1, alpha=0.6, label='Non-ground')\n", "    ax3.set_title('RANSAC + PMF Results')\n", "    ax3.set_xlabel('X')\n", "    ax3.set_ylabel('Y')\n", "    ax3.set_zlabel('Z')\n", "    ax3.legend()\n", "    \n", "    # Plot 4: Height distribution comparison\n", "    ax4 = fig.add_subplot(2, 3, 4)\n", "    ax4.hist(ransac_ground_points[:, 2], bins=50, alpha=0.7, label='RANSAC Ground', color='brown')\n", "    if len(pmf_additional_ground_points) > 0:\n", "        ax4.hist(pmf_additional_ground_points[:, 2], bins=50, alpha=0.7, label='PMF Additional', color='orange')\n", "    ax4.hist(combined_nonground_points[:, 2], bins=50, alpha=0.7, label='Non-ground', color='green')\n", "    ax4.set_title('Height Distribution')\n", "    ax4.set_xlabel('Z (meters)')\n", "    ax4.set_ylabel('Point Count')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    # Plot 5: Processing time comparison\n", "    ax5 = fig.add_subplot(2, 3, 5)\n", "    methods = ['RANSAC', 'PMF', 'Total']\n", "    times = [ransac_time, pmf_time, total_processing_time]\n", "    bars = ax5.bar(methods, times, color=['blue', 'orange', 'red'])\n", "    ax5.set_title('Processing Time Comparison')\n", "    ax5.set_ylabel('Time (seconds)')\n", "    for bar, time_val in zip(bars, times):\n", "        ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n", "                f'{time_val:.2f}s', ha='center', va='bottom')\n", "    ax5.grid(True, alpha=0.3)\n", "    \n", "    # Plot 6: Point count comparison\n", "    ax6 = fig.add_subplot(2, 3, 6)\n", "    categories = ['RANSAC\\nGround', 'PMF\\nAdditional', 'Final\\nNon-ground']\n", "    counts = [len(ransac_ground_points), len(pmf_additional_ground_points), len(combined_nonground_points)]\n", "    colors = ['brown', 'orange', 'green']\n", "    bars = ax6.bar(categories, counts, color=colors)\n", "    ax6.set_title('Point Count Breakdown')\n", "    ax6.set_ylabel('Point Count')\n", "    for bar, count in zip(bars, counts):\n", "        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01, \n", "                f'{count:,}', ha='center', va='bottom', rotation=0)\n", "    ax6.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save visualization\n", "    viz_file = current_run_path / \"ransac_pmf_comparison.png\"\n", "    plt.savefig(viz_file, dpi=300, bbox_inches='tight')\n", "    print(f\"Visualization saved to: {viz_file}\")\n", "    \n", "    plt.show()\n", "\n", "print(f\"\\nProcessing complete! Check {current_run_path} for all outputs.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}