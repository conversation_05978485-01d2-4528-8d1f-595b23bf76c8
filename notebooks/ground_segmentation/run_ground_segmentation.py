#!/usr/bin/env python3
"""
Example script to run ground segmentation notebook with <PERSON><PERSON>.

This script demonstrates how to execute the ground segmentation notebook
with different parameter configurations using Papermill.
"""

import papermill as pm
from datetime import datetime
from pathlib import Path

def run_ground_segmentation(
    site_name="Trino",
    segmentation_method="RANSAC",
    point_cloud_path="",
    buffer_radius=50.0,
    project_type="ENEL",
    # RANSAC parameters
    ransac_distance_threshold=0.2,
    ransac_num_iterations=1000,
    ransac_min_inliers_ratio=0.05,
    # PMF parameters
    pmf_cell_size=1.0,
    pmf_max_window_size=33,
    pmf_slope=0.15,
    # CSF parameters
    csf_cloth_resolution=0.5,
    csf_classification_threshold=0.5
):
    """
    Execute ground segmentation notebook with specified parameters.
    
    Parameters:
    -----------
    site_name : str
        Name of the site/project
    segmentation_method : str
        Ground segmentation method ("RANSAC", "PMF", "CSF")
    point_cloud_path : str
        Path to input point cloud file
    buffer_radius : float
        Buffer radius for spatial filtering (meters)
    project_type : str
        Project type ("ENEL", "USA")
    
    Returns:
    --------
    str : Path to output notebook
    """
    
    # Create timestamp for this run
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Define input and output paths
    input_notebook = "ground_removal.ipynb"
    output_notebook = f"output_runs/{site_name}_{timestamp}_executed.ipynb"
    
    # Ensure output directory exists
    Path("output_runs").mkdir(exist_ok=True)
    
    # Parameters to inject
    parameters = {
        'site_name': site_name,
        'segmentation_method': segmentation_method,
        'point_cloud_path': point_cloud_path,
        'buffer_radius': buffer_radius,
        'project_type': project_type,
        'ransac_distance_threshold': ransac_distance_threshold,
        'ransac_num_iterations': ransac_num_iterations,
        'ransac_min_inliers_ratio': ransac_min_inliers_ratio,
        'pmf_cell_size': pmf_cell_size,
        'pmf_max_window_size': pmf_max_window_size,
        'pmf_slope': pmf_slope,
        'csf_cloth_resolution': csf_cloth_resolution,
        'csf_classification_threshold': csf_classification_threshold
    }
    
    print(f"Executing ground segmentation for site: {site_name}")
    print(f"Method: {segmentation_method}")
    print(f"Output notebook: {output_notebook}")
    
    # Execute notebook with parameters
    pm.execute_notebook(
        input_notebook,
        output_notebook,
        parameters=parameters,
        log_output=True
    )
    
    print(f"Execution completed. Results saved to: {output_notebook}")
    return output_notebook

def main():
    """Example usage of the ground segmentation runner."""
    
    # Example 1: RANSAC with default parameters
    print("=== Example 1: RANSAC Ground Segmentation ===")
    run_ground_segmentation(
        site_name="Trino_Test",
        segmentation_method="RANSAC",
        ransac_distance_threshold=0.15,
        ransac_num_iterations=1500
    )
    
    # Example 2: PMF with custom parameters
    print("\n=== Example 2: PMF Ground Segmentation ===")
    run_ground_segmentation(
        site_name="Castro_Test",
        segmentation_method="PMF",
        pmf_cell_size=0.5,
        pmf_slope=0.2
    )
    
    # Example 3: CSF with custom parameters
    print("\n=== Example 3: CSF Ground Segmentation ===")
    run_ground_segmentation(
        site_name="Giorgio_Test",
        segmentation_method="CSF",
        csf_cloth_resolution=0.3,
        csf_classification_threshold=0.3
    )

if __name__ == "__main__":
    main()
