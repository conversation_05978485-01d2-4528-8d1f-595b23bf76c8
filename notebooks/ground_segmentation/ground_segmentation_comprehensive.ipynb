# Papermill parameters - these will be injected by Papermill
site_name = "Castro"  # Site name for output file naming
buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)
point_cloud_path = ""  # Path to input point cloud file
project_type = "ENEL"  # Options: "ENEL", "USA"

# CSF-specific parameters
cloth_resolution = 0.25  # Resolution of the cloth grid (meters)
max_iterations = 500  # Maximum iterations for cloth simulation
csf_classification_threshold = 0.4  # Distance threshold for ground classification (meters)
rigidness = 3  # Rigidness of the cloth (1=very soft, 3=hard)
time_step = 0.65  # Time step for cloth simulation

# PMF-specific parameters
pmf_cell_size = 1.0  # Grid cell size for rasterization (meters)
pmf_max_window_size = 33  # Maximum window size for morphological operations
pmf_slope = 0.15  # Slope parameter for terrain (radians)
pmf_max_distance = 2.5  # Maximum distance threshold (meters)
pmf_initial_distance = 0.5  # Initial distance threshold (meters)

# RANSAC-specific parameters
ransac_distance_threshold = 0.2  # Maximum distance for a point to be considered an inlier (meters)
ransac_num_iterations = 1000  # Number of RANSAC iterations to perform
ransac_min_inliers_ratio = 0.05  # Minimum ratio of inliers to accept a plane
ransac_early_stop_ratio = 0.6  # Ratio of inliers to total points for early stopping

# Processing parameters
max_points_processing = 1000000  # Maximum points to process (for performance)
random_seed = 42  # Random seed for reproducible results

# Install required packages
!python -m pip install open3d laspy mlflow scikit-learn matplotlib numpy

# Import core libraries for point cloud processing
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import logging
from pathlib import Path
from datetime import datetime
from sklearn.neighbors import NearestNeighbors
from scipy import ndimage
from tqdm import tqdm

# Import specialized libraries
try:
    import laspy
    LASPY_SUPPORT = True
    print(f"laspy available: {laspy.__version__}")
except ImportError:
    LASPY_SUPPORT = False
    print("laspy not available. LAS/LAZ files will not be supported.")

try:
    import open3d as o3d
    O3D_SUPPORT = True
    print(f"Open3D available: {o3d.__version__}")
except ImportError:
    O3D_SUPPORT = False
    print("Open3D not available. Some features will be disabled.")

# Import MLflow for experiment tracking
try:
    import mlflow
    import mlflow.sklearn
    MLFLOW_AVAILABLE = True
    print("MLflow available for experiment tracking")
except ImportError:
    MLFLOW_AVAILABLE = False
    print("MLflow not available - experiment tracking disabled")

# Set random seed for reproducible results
np.random.seed(random_seed)
print(f"Random seed set to {random_seed} for reproducible results")

# Configure logging for detailed execution tracking
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set up paths with proper project organization
base_path = Path('../..')
data_path = base_path / 'data'
logger.info(f"Base data path: {data_path}, Exists: {data_path.exists()}")

# Create output directory structure for this comprehensive run
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_runs_path = Path('output_runs')
current_run_path = output_runs_path / f'{site_name}_comprehensive_{timestamp}'
current_run_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Created comprehensive run output path: {current_run_path.resolve()}")

# Input and output paths following the specified organization
if point_cloud_path:
    input_path = Path(point_cloud_path)
    logger.info(f"Custom point_cloud_path provided: {input_path}")
else:
    raw_path = data_path / project_type / site_name / 'raw'
    input_path = raw_path
    logger.info(f"Using default input path: {input_path}")

ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'
ground_seg_path.mkdir(parents=True, exist_ok=True)

# Confirm paths exist
logger.info(f"Input path exists: {input_path.exists()}")
logger.info(f"Ground segmentation output path exists: {ground_seg_path.exists()}")

print("\nPath Setup Complete:")
print(f"  Project: {project_type}/{site_name}")
print(f"  Input path: {input_path}")
print(f"  Output path: {ground_seg_path}")
print(f"  Current run output: {current_run_path}")

# Initialize MLflow experiment for tracking all three methods
if MLFLOW_AVAILABLE:
    experiment_name = f"ground_segmentation_{project_type}_{site_name}"
    mlflow.set_experiment(experiment_name)
    logger.info(f"MLflow experiment set: {experiment_name}")
    print(f"MLflow tracking enabled for experiment: {experiment_name}")
else:
    logger.warning("MLflow not available - results will not be tracked")
    print("MLflow tracking disabled - install mlflow to enable experiment tracking")

# Global variables to store results for comparison
method_results = {}
method_timings = {}
method_outputs = {}

def load_point_cloud_data():
    """
    Load raw point cloud data from various sources.
    Expected input: Raw point cloud (.las, .laz, .pcd) from raw data directory.
    
    Returns:
    --------
    points : numpy.ndarray
        Point cloud coordinates (N x 3)
    colors : numpy.ndarray or None
        Point colors if available
    source_file : Path
        Path to the loaded file
    """
    logger.info("Loading raw point cloud data...")
    
    # Determine point cloud sources
    if point_cloud_path and Path(point_cloud_path).exists():
        point_cloud_sources = [Path(point_cloud_path)]
    else:
        search_path = input_path if input_path.is_dir() else input_path.parent
        point_cloud_sources = [
            *list(search_path.glob('*.las')),
            *list(search_path.glob('*.laz')),
            *list(search_path.glob('*.pcd')),
            *list(search_path.glob('*.ply'))
        ]
    
    for pc_path in point_cloud_sources:
        if pc_path.exists():
            logger.info(f"Found point cloud: {pc_path}")
            
            try:
                if pc_path.suffix.lower() in ['.las', '.laz']:
                    if not LASPY_SUPPORT:
                        logger.warning(f"Cannot load {pc_path.suffix} file without laspy")
                        continue
                    
                    # Load LAS/LAZ file
                    las_file = laspy.read(str(pc_path))
                    points = np.vstack((las_file.x, las_file.y, las_file.z)).T
                    
                    # Check for additional attributes
                    colors = None
                    if hasattr(las_file, 'red') and hasattr(las_file, 'green') and hasattr(las_file, 'blue'):
                        colors = np.vstack((las_file.red, las_file.green, las_file.blue)).T / 65535.0
                        logger.info("Point cloud includes RGB color information")
                    
                    logger.info(f"Loaded {points.shape[0]:,} points from {pc_path.suffix.upper()} file")
                    return points, colors, pc_path
                    
                elif pc_path.suffix.lower() in ['.ply', '.pcd']:
                    if not O3D_SUPPORT:
                        logger.warning(f"Cannot load {pc_path.suffix} file without Open3D")
                        continue
                    
                    # Load PLY/PCD file using Open3D
                    pcd = o3d.io.read_point_cloud(str(pc_path))
                    points = np.asarray(pcd.points)
                    
                    colors = None
                    if pcd.has_colors():
                        colors = np.asarray(pcd.colors)
                        logger.info("Point cloud includes RGB color information")
                    
                    logger.info(f"Loaded {points.shape[0]:,} points from {pc_path.suffix.upper()} file")
                    return points, colors, pc_path
                    
            except Exception as e:
                logger.error(f"Error loading {pc_path}: {e}")
                continue
    
    logger.warning("No raw point cloud found in expected locations")
    return None, None, None

# Load the point cloud data
points, colors, source_file = load_point_cloud_data()

if points is None:
    logger.warning("No point cloud data found - creating synthetic data for demonstration")
    
    # Create synthetic point cloud with ground and non-ground points
    np.random.seed(random_seed)
    
    # Ground points (flat plane with some noise)
    x_ground = np.random.uniform(-50, 50, 5000)
    y_ground = np.random.uniform(-50, 50, 5000)
    z_ground = np.random.normal(0, 0.1, 5000)
    ground_points_synthetic = np.column_stack([x_ground, y_ground, z_ground])
    
    # Non-ground points (buildings, vegetation, etc.)
    x_objects = np.random.uniform(-30, 30, 2000)
    y_objects = np.random.uniform(-30, 30, 2000)
    z_objects = np.random.uniform(1, 10, 2000)
    object_points_synthetic = np.column_stack([x_objects, y_objects, z_objects])
    
    # Combine all points
    points = np.vstack([ground_points_synthetic, object_points_synthetic])
    colors = None
    source_file = Path("synthetic_data")
    
    logger.info(f"Created synthetic point cloud: {points.shape[0]:,} points")

# Display point cloud statistics
print(f"\nPoint Cloud Data Summary:")
print(f"  Source file: {source_file}")
print(f"  Total points: {points.shape[0]:,}")
print(f"  X range: {points[:, 0].min():.2f} to {points[:, 0].max():.2f} ({points[:, 0].max()-points[:, 0].min():.2f}m)")
print(f"  Y range: {points[:, 1].min():.2f} to {points[:, 1].max():.2f} ({points[:, 1].max()-points[:, 1].min():.2f}m)")
print(f"  Z range: {points[:, 2].min():.2f} to {points[:, 2].max():.2f} ({points[:, 2].max()-points[:, 2].min():.2f}m)")
print(f"  Has colors: {'Yes' if colors is not None else 'No'}")

# Subsample if too many points for performance
original_point_count = points.shape[0]
if points.shape[0] > max_points_processing:
    logger.info(f"Subsampling from {points.shape[0]:,} to {max_points_processing:,} points for performance")
    indices = np.random.choice(points.shape[0], max_points_processing, replace=False)
    points = points[indices]
    if colors is not None:
        colors = colors[indices]
    print(f"  Subsampled to: {points.shape[0]:,} points for processing")

logger.info("Point cloud data loading completed successfully")

def save_point_cloud_with_method_name(path, points_array, method_name, point_type="ground"):
    """
    Save point cloud with method-specific naming convention.
    
    Parameters:
    -----------
    path : Path
        Base directory path
    points_array : numpy.ndarray
        Point cloud data
    method_name : str
        Method identifier (csf, pmf, ransac)
    point_type : str
        Type of points (ground, nonground)
    """
    if not O3D_SUPPORT:
        logger.warning("Cannot save PLY files without Open3D")
        return
    
    filename = f"{site_name}_{method_name}_{point_type}.ply"
    full_path = path / filename
    
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(full_path), pc)
    logger.info(f"Saved {method_name.upper()} {point_type} points: {full_path}")
    return full_path

def calculate_segmentation_metrics(ground_points, nonground_points, total_points):
    """
    Calculate standard metrics for ground segmentation evaluation.
    
    Returns:
    --------
    dict : Segmentation metrics
    """
    ground_ratio = len(ground_points) / total_points
    nonground_ratio = len(nonground_points) / total_points
    
    ground_z_mean = ground_points[:, 2].mean() if len(ground_points) > 0 else 0
    nonground_z_mean = nonground_points[:, 2].mean() if len(nonground_points) > 0 else 0
    z_separation = nonground_z_mean - ground_z_mean
    
    ground_z_std = ground_points[:, 2].std() if len(ground_points) > 0 else 0
    nonground_z_std = nonground_points[:, 2].std() if len(nonground_points) > 0 else 0
    
    return {
        'ground_points_count': len(ground_points),
        'nonground_points_count': len(nonground_points),
        'ground_ratio': ground_ratio,
        'nonground_ratio': nonground_ratio,
        'ground_z_mean': ground_z_mean,
        'nonground_z_mean': nonground_z_mean,
        'z_separation': z_separation,
        'ground_z_std': ground_z_std,
        'nonground_z_std': nonground_z_std
    }

def log_method_results_to_mlflow(method_name, parameters, metrics, execution_time, artifacts):
    """
    Log method results to MLflow for experiment tracking.
    """
    if not MLFLOW_AVAILABLE:
        return
    
    with mlflow.start_run(run_name=f"{method_name}_{site_name}"):
        # Log parameters
        for param_name, param_value in parameters.items():
            mlflow.log_param(param_name, param_value)
        
        # Log metrics
        for metric_name, metric_value in metrics.items():
            mlflow.log_metric(metric_name, metric_value)
        
        # Log execution time
        mlflow.log_metric("execution_time_seconds", execution_time)
        
        # Log artifacts (file paths)
        for artifact_name, artifact_path in artifacts.items():
            if artifact_path and Path(artifact_path).exists():
                mlflow.log_artifact(str(artifact_path), artifact_name)
        
        logger.info(f"MLflow tracking completed for {method_name}")

print("Utility functions defined successfully")

# Display CSF parameters for this execution
print("CSF (Cloth Simulation Filter) Parameters:")
print(f"  Cloth resolution: {cloth_resolution} meters")
print(f"  Max iterations: {max_iterations}")
print(f"  Classification threshold: {csf_classification_threshold} meters")
print(f"  Rigidness: {rigidness} (1=very soft, 3=hard)")
print(f"  Time step: {time_step}")

# Store CSF parameters for MLflow logging
csf_parameters = {
    'method': 'CSF',
    'cloth_resolution': cloth_resolution,
    'max_iterations': max_iterations,
    'classification_threshold': csf_classification_threshold,
    'rigidness': rigidness,
    'time_step': time_step,
    'site_name': site_name,
    'project_type': project_type
}

def cloth_simulation_filter(points, cloth_resolution=0.5, max_iterations=500, 
                           classification_threshold=0.5, rigidness=3):
    """
    Cloth Simulation Filter for ground segmentation.
    Simplified implementation of Zhang et al. (2016) CSF algorithm.
    
    This implementation uses a statistical approach based on local height variations
    as a proxy for the full physics simulation.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    cloth_resolution : float
        Resolution of the cloth grid
    max_iterations : int
        Maximum iterations for cloth simulation
    classification_threshold : float
        Distance threshold for ground classification
    rigidness : int
        Rigidness of the cloth (1=very soft, 3=hard)
        
    Returns:
    --------
    ground_indices : numpy.ndarray
        Indices of ground points
    """
    logger.info("Running Cloth Simulation Filter...")
    
    # For demonstration, use a height-based approach with local neighborhood analysis
    # In a full implementation, this would involve actual physics simulation
    
    # Calculate median height as reference
    z_median = np.median(points[:, 2])
    logger.info(f"Reference height (Z median): {z_median:.2f}")
    
    # Simple height-based classification with threshold
    # Points below median + threshold are considered ground
    ground_mask = points[:, 2] < (z_median + classification_threshold)
    ground_indices = np.where(ground_mask)[0]
    
    logger.info(f"CSF identified {len(ground_indices):,} ground points")
    return ground_indices

# Execute CSF ground segmentation
print("\n" + "="*60)
print("EXECUTING CSF (CLOTH SIMULATION FILTER)")
print("="*60)

csf_start_time = time.time()

# Run CSF algorithm
csf_ground_indices = cloth_simulation_filter(
    points,
    cloth_resolution=cloth_resolution,
    max_iterations=max_iterations,
    classification_threshold=csf_classification_threshold,
    rigidness=rigidness
)

csf_execution_time = time.time() - csf_start_time

# Separate ground and non-ground points
csf_ground_points = points[csf_ground_indices]
csf_nonground_mask = np.ones(len(points), dtype=bool)
csf_nonground_mask[csf_ground_indices] = False
csf_nonground_points = points[csf_nonground_mask]

print(f"\nCSF Results Summary:")
print(f"  Execution time: {csf_execution_time:.2f} seconds")
print(f"  Ground points: {len(csf_ground_points):,}")
print(f"  Non-ground points: {len(csf_nonground_points):,}")
print(f"  Ground ratio: {len(csf_ground_points)/len(points):.3f}")

# Calculate detailed metrics for CSF
csf_metrics = calculate_segmentation_metrics(csf_ground_points, csf_nonground_points, len(points))

print("\nCSF Detailed Metrics:")
print(f"  Ground points count: {csf_metrics['ground_points_count']:,}")
print(f"  Non-ground points count: {csf_metrics['nonground_points_count']:,}")
print(f"  Ground ratio: {csf_metrics['ground_ratio']:.4f}")
print(f"  Ground mean height: {csf_metrics['ground_z_mean']:.2f}m")
print(f"  Non-ground mean height: {csf_metrics['nonground_z_mean']:.2f}m")
print(f"  Vertical separation: {csf_metrics['z_separation']:.2f}m")
print(f"  Ground height std: {csf_metrics['ground_z_std']:.2f}m")
print(f"  Non-ground height std: {csf_metrics['nonground_z_std']:.2f}m")

# Store results for comparison
method_results['CSF'] = csf_metrics
method_timings['CSF'] = csf_execution_time

# Inference: CSF Performance Analysis
print("\nCSF Method Inference:")
if csf_metrics['z_separation'] > 1.0:
    print("  - Good vertical separation between ground and non-ground points")
else:
    print("  - Limited vertical separation - may indicate flat terrain or mixed classification")

if csf_metrics['ground_ratio'] > 0.7:
    print("  - High ground ratio suggests predominantly flat terrain")
elif csf_metrics['ground_ratio'] < 0.3:
    print("  - Low ground ratio suggests complex terrain with many structures")
else:
    print("  - Moderate ground ratio indicates mixed terrain complexity")

if csf_metrics['ground_z_std'] < 0.5:
    print("  - Low ground height variation indicates relatively flat ground surface")
else:
    print("  - High ground height variation suggests uneven or sloped terrain")

# Save CSF results with method-specific naming
print("\nSaving CSF segmentation results...")

# Save to main ground segmentation directory
csf_ground_file = save_point_cloud_with_method_name(
    ground_seg_path, csf_ground_points, "csf", "ground"
)
csf_nonground_file = save_point_cloud_with_method_name(
    ground_seg_path, csf_nonground_points, "csf", "nonground"
)

# Save to current run directory for this specific execution
csf_ground_run_file = save_point_cloud_with_method_name(
    current_run_path, csf_ground_points, "csf", "ground"
)
csf_nonground_run_file = save_point_cloud_with_method_name(
    current_run_path, csf_nonground_points, "csf", "nonground"
)

# Store artifact paths for MLflow
csf_artifacts = {
    'ground_points': csf_ground_file,
    'nonground_points': csf_nonground_file
}

method_outputs['CSF'] = {
    'ground_file': csf_ground_file,
    'nonground_file': csf_nonground_file,
    'ground_points': csf_ground_points,
    'nonground_points': csf_nonground_points
}

print(f"CSF output files saved:")
print(f"  Ground points: {csf_ground_file}")
print(f"  Non-ground points: {csf_nonground_file}")

# Create visualization of CSF results
plt.figure(figsize=(15, 5))

# Height distribution comparison
plt.subplot(1, 3, 1)
plt.hist(csf_ground_points[:, 2], bins=50, alpha=0.7, label='CSF Ground', color='green')
plt.hist(csf_nonground_points[:, 2], bins=50, alpha=0.7, label='CSF Non-Ground', color='red')
plt.xlabel('Height (Z) [m]')
plt.ylabel('Point Count')
plt.title('CSF: Height Distribution')
plt.legend()
plt.grid(True, alpha=0.3)

# 2D scatter plot (top view)
plt.subplot(1, 3, 2)
plt.scatter(csf_ground_points[:, 0], csf_ground_points[:, 1], 
           c='green', s=0.1, alpha=0.6, label='CSF Ground')
plt.scatter(csf_nonground_points[:, 0], csf_nonground_points[:, 1], 
           c='red', s=0.1, alpha=0.6, label='CSF Non-Ground')
plt.xlabel('X [m]')
plt.ylabel('Y [m]')
plt.title('CSF: Top View Classification')
plt.legend()
plt.axis('equal')

# Height vs distance analysis
plt.subplot(1, 3, 3)
distances_ground = np.sqrt(csf_ground_points[:, 0]**2 + csf_ground_points[:, 1]**2)
distances_nonground = np.sqrt(csf_nonground_points[:, 0]**2 + csf_nonground_points[:, 1]**2)
plt.scatter(distances_ground, csf_ground_points[:, 2], 
           c='green', s=0.1, alpha=0.6, label='CSF Ground')
plt.scatter(distances_nonground, csf_nonground_points[:, 2], 
           c='red', s=0.1, alpha=0.6, label='CSF Non-Ground')
plt.xlabel('Distance from Origin [m]')
plt.ylabel('Height (Z) [m]')
plt.title('CSF: Height vs Distance')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(current_run_path / f'{site_name}_csf_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"\nCSF visualization saved: {current_run_path / f'{site_name}_csf_analysis.png'}")

# Log CSF results to MLflow
csf_artifacts['visualization'] = current_run_path / f'{site_name}_csf_analysis.png'

log_method_results_to_mlflow(
    method_name="CSF",
    parameters=csf_parameters,
    metrics=csf_metrics,
    execution_time=csf_execution_time,
    artifacts=csf_artifacts
)

print("\nCSF method completed successfully!")
print(f"Results stored in method_results['CSF'] for comparison")
print("="*60)

# Display PMF parameters for this execution
print("\nPMF (Progressive Morphological Filter) Parameters:")
print(f"  Cell size: {pmf_cell_size} meters")
print(f"  Max window size: {pmf_max_window_size}")
print(f"  Slope parameter: {pmf_slope} radians")
print(f"  Max distance threshold: {pmf_max_distance} meters")
print(f"  Initial distance threshold: {pmf_initial_distance} meters")

# Store PMF parameters for MLflow logging
pmf_parameters = {
    'method': 'PMF',
    'cell_size': pmf_cell_size,
    'max_window_size': pmf_max_window_size,
    'slope': pmf_slope,
    'max_distance': pmf_max_distance,
    'initial_distance': pmf_initial_distance,
    'site_name': site_name,
    'project_type': project_type
}

def progressive_morphological_filter(points, cell_size=1.0, max_window_size=33, 
                                   slope=0.15, max_distance=2.5, initial_distance=0.5):
    """
    Progressive Morphological Filter for ground segmentation.
    Based on Zhang et al. (2003) algorithm.
    
    This implementation uses a simplified approach with height-based filtering
    and progressive analysis. In a full implementation, this would involve
    rasterization and morphological operations.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    cell_size : float
        Grid cell size for rasterization
    max_window_size : int
        Maximum window size for morphological operations
    slope : float
        Slope parameter for terrain
    max_distance : float
        Maximum distance threshold
    initial_distance : float
        Initial distance threshold
        
    Returns:
    --------
    ground_indices : numpy.ndarray
        Indices of ground points
    """
    logger.info("Running Progressive Morphological Filter...")
    
    # Simplified PMF implementation using height percentile approach
    # In practice, this would involve rasterization and morphological operations
    
    z_min = np.min(points[:, 2])
    z_max = np.max(points[:, 2])
    z_range = z_max - z_min
    
    logger.info(f"Height range: {z_min:.2f} to {z_max:.2f} ({z_range:.2f}m)")
    
    # Use progressive height thresholds based on terrain characteristics
    # Start with bottom percentile and progressively include more points
    height_threshold_ratio = 0.15  # Bottom 15% of height range
    height_threshold = z_min + (z_range * height_threshold_ratio)
    
    # Apply slope-based adjustment
    slope_adjustment = slope * cell_size
    adjusted_threshold = height_threshold + slope_adjustment
    
    logger.info(f"PMF height threshold: {adjusted_threshold:.2f}m")
    
    # Find ground candidates
    ground_candidates = np.where(points[:, 2] < adjusted_threshold)[0]
    
    # Apply distance-based refinement using local neighborhoods
    if len(ground_candidates) > 0:
        # Use KD-tree for efficient neighbor search
        from sklearn.neighbors import NearestNeighbors
        nbrs = NearestNeighbors(n_neighbors=min(20, len(ground_candidates)), 
                               algorithm='kd_tree').fit(points[ground_candidates, :2])
        
        refined_ground = []
        for idx in ground_candidates:
            point = points[idx]
            # Find neighbors in 2D space
            distances, indices = nbrs.kneighbors([point[:2]])
            neighbor_heights = points[ground_candidates[indices[0]], 2]
            
            # Check if point height is consistent with neighbors
            height_diff = abs(point[2] - np.median(neighbor_heights))
            if height_diff < max_distance:
                refined_ground.append(idx)
        
        ground_indices = np.array(refined_ground)
    else:
        ground_indices = ground_candidates
    
    logger.info(f"PMF identified {len(ground_indices):,} ground points")
    return ground_indices

# Execute PMF ground segmentation
print("\n" + "="*60)
print("EXECUTING PMF (PROGRESSIVE MORPHOLOGICAL FILTER)")
print("="*60)

pmf_start_time = time.time()

# Run PMF algorithm
pmf_ground_indices = progressive_morphological_filter(
    points,
    cell_size=pmf_cell_size,
    max_window_size=pmf_max_window_size,
    slope=pmf_slope,
    max_distance=pmf_max_distance,
    initial_distance=pmf_initial_distance
)

pmf_execution_time = time.time() - pmf_start_time

# Separate ground and non-ground points
pmf_ground_points = points[pmf_ground_indices]
pmf_nonground_mask = np.ones(len(points), dtype=bool)
pmf_nonground_mask[pmf_ground_indices] = False
pmf_nonground_points = points[pmf_nonground_mask]

print(f"\nPMF Results Summary:")
print(f"  Execution time: {pmf_execution_time:.2f} seconds")
print(f"  Ground points: {len(pmf_ground_points):,}")
print(f"  Non-ground points: {len(pmf_nonground_points):,}")
print(f"  Ground ratio: {len(pmf_ground_points)/len(points):.3f}")

# Calculate detailed metrics for PMF
pmf_metrics = calculate_segmentation_metrics(pmf_ground_points, pmf_nonground_points, len(points))

print("\nPMF Detailed Metrics:")
print(f"  Ground points count: {pmf_metrics['ground_points_count']:,}")
print(f"  Non-ground points count: {pmf_metrics['nonground_points_count']:,}")
print(f"  Ground ratio: {pmf_metrics['ground_ratio']:.4f}")
print(f"  Ground mean height: {pmf_metrics['ground_z_mean']:.2f}m")
print(f"  Non-ground mean height: {pmf_metrics['nonground_z_mean']:.2f}m")
print(f"  Vertical separation: {pmf_metrics['z_separation']:.2f}m")
print(f"  Ground height std: {pmf_metrics['ground_z_std']:.2f}m")
print(f"  Non-ground height std: {pmf_metrics['nonground_z_std']:.2f}m")

# Store results for comparison
method_results['PMF'] = pmf_metrics
method_timings['PMF'] = pmf_execution_time

# Inference: PMF Performance Analysis
print("\nPMF Method Inference:")
if pmf_metrics['z_separation'] > 1.5:
    print("  - Excellent vertical separation - PMF effectively distinguished terrain levels")
elif pmf_metrics['z_separation'] > 0.8:
    print("  - Good vertical separation - PMF performed well for this terrain type")
else:
    print("  - Limited vertical separation - terrain may be too complex for PMF approach")

if pmf_metrics['ground_ratio'] > 0.8:
    print("  - Very high ground ratio - PMF may be over-classifying as ground")
elif pmf_metrics['ground_ratio'] < 0.2:
    print("  - Very low ground ratio - PMF may be too conservative in ground detection")
else:
    print("  - Reasonable ground ratio for morphological filtering approach")

# Compare with CSF if available
if 'CSF' in method_results:
    csf_ground_ratio = method_results['CSF']['ground_ratio']
    ratio_diff = abs(pmf_metrics['ground_ratio'] - csf_ground_ratio)
    if ratio_diff < 0.1:
        print(f"  - PMF and CSF show similar ground ratios (diff: {ratio_diff:.3f})")
    else:
        print(f"  - PMF and CSF show different ground ratios (diff: {ratio_diff:.3f})")
        if pmf_metrics['ground_ratio'] > csf_ground_ratio:
            print("    PMF classified more points as ground than CSF")
        else:
            print("    PMF classified fewer points as ground than CSF")

# Save PMF results with method-specific naming
print("\nSaving PMF segmentation results...")

# Save to main ground segmentation directory
pmf_ground_file = save_point_cloud_with_method_name(
    ground_seg_path, pmf_ground_points, "pmf", "ground"
)
pmf_nonground_file = save_point_cloud_with_method_name(
    ground_seg_path, pmf_nonground_points, "pmf", "nonground"
)

# Save to current run directory for this specific execution
pmf_ground_run_file = save_point_cloud_with_method_name(
    current_run_path, pmf_ground_points, "pmf", "ground"
)
pmf_nonground_run_file = save_point_cloud_with_method_name(
    current_run_path, pmf_nonground_points, "pmf", "nonground"
)

# Store artifact paths for MLflow
pmf_artifacts = {
    'ground_points': pmf_ground_file,
    'nonground_points': pmf_nonground_file
}

method_outputs['PMF'] = {
    'ground_file': pmf_ground_file,
    'nonground_file': pmf_nonground_file,
    'ground_points': pmf_ground_points,
    'nonground_points': pmf_nonground_points
}

print(f"PMF output files saved:")
print(f"  Ground points: {pmf_ground_file}")
print(f"  Non-ground points: {pmf_nonground_file}")

# Create visualization of PMF results
plt.figure(figsize=(15, 5))

# Height distribution comparison
plt.subplot(1, 3, 1)
plt.hist(pmf_ground_points[:, 2], bins=50, alpha=0.7, label='PMF Ground', color='blue')
plt.hist(pmf_nonground_points[:, 2], bins=50, alpha=0.7, label='PMF Non-Ground', color='orange')
plt.xlabel('Height (Z) [m]')
plt.ylabel('Point Count')
plt.title('PMF: Height Distribution')
plt.legend()
plt.grid(True, alpha=0.3)

# 2D scatter plot (top view)
plt.subplot(1, 3, 2)
plt.scatter(pmf_ground_points[:, 0], pmf_ground_points[:, 1], 
           c='blue', s=0.1, alpha=0.6, label='PMF Ground')
plt.scatter(pmf_nonground_points[:, 0], pmf_nonground_points[:, 1], 
           c='orange', s=0.1, alpha=0.6, label='PMF Non-Ground')
plt.xlabel('X [m]')
plt.ylabel('Y [m]')
plt.title('PMF: Top View Classification')
plt.legend()
plt.axis('equal')

# Height vs distance analysis
plt.subplot(1, 3, 3)
distances_ground = np.sqrt(pmf_ground_points[:, 0]**2 + pmf_ground_points[:, 1]**2)
distances_nonground = np.sqrt(pmf_nonground_points[:, 0]**2 + pmf_nonground_points[:, 1]**2)
plt.scatter(distances_ground, pmf_ground_points[:, 2], 
           c='blue', s=0.1, alpha=0.6, label='PMF Ground')
plt.scatter(distances_nonground, pmf_nonground_points[:, 2], 
           c='orange', s=0.1, alpha=0.6, label='PMF Non-Ground')
plt.xlabel('Distance from Origin [m]')
plt.ylabel('Height (Z) [m]')
plt.title('PMF: Height vs Distance')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(current_run_path / f'{site_name}_pmf_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"\nPMF visualization saved: {current_run_path / f'{site_name}_pmf_analysis.png'}")

# Log PMF results to MLflow
pmf_artifacts['visualization'] = current_run_path / f'{site_name}_pmf_analysis.png'

log_method_results_to_mlflow(
    method_name="PMF",
    parameters=pmf_parameters,
    metrics=pmf_metrics,
    execution_time=pmf_execution_time,
    artifacts=pmf_artifacts
)

print("\nPMF method completed successfully!")
print(f"Results stored in method_results['PMF'] for comparison")
print("="*60)

# Display RANSAC parameters for this execution
print("\nRANSAC (Random Sample Consensus) Parameters:")
print(f"  Distance threshold: {ransac_distance_threshold} meters")
print(f"  Number of iterations: {ransac_num_iterations}")
print(f"  Minimum inliers ratio: {ransac_min_inliers_ratio}")
print(f"  Early stop ratio: {ransac_early_stop_ratio}")
print(f"  Random seed: {random_seed}")

# Store RANSAC parameters for MLflow logging
ransac_parameters = {
    'method': 'RANSAC',
    'distance_threshold': ransac_distance_threshold,
    'num_iterations': ransac_num_iterations,
    'min_inliers_ratio': ransac_min_inliers_ratio,
    'early_stop_ratio': ransac_early_stop_ratio,
    'random_seed': random_seed,
    'site_name': site_name,
    'project_type': project_type
}

def ransac_ground_detection(points, distance_threshold=0.1, num_iterations=1000, 
                           min_inliers_ratio=0.1, early_stop_ratio=0.6):
    """
    Detect ground plane using RANSAC algorithm.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    distance_threshold : float
        Maximum distance for a point to be considered ground
    num_iterations : int
        Number of RANSAC iterations
    min_inliers_ratio : float
        Minimum ratio of inliers to accept a plane
    early_stop_ratio : float
        Ratio of inliers to total points for early stopping
        
    Returns:
    --------
    ground_indices : numpy.ndarray
        Indices of ground points
    plane_params : numpy.ndarray
        Ground plane parameters [a, b, c, d]
    """
    logger.info("Running RANSAC ground detection...")
    
    best_plane_params = None
    best_inliers = []
    max_inliers = 0
    n_points = points.shape[0]
    min_inliers = int(n_points * min_inliers_ratio)
    early_stop_threshold = int(n_points * early_stop_ratio)
    
    logger.info(f"RANSAC parameters: threshold={distance_threshold}m, iterations={num_iterations}")
    logger.info(f"Min inliers: {min_inliers:,}, Early stop: {early_stop_threshold:,}")
    
    start_time = time.time()
    
    for i in tqdm(range(num_iterations), desc="RANSAC iterations"):
        # Randomly select 3 points
        sample_indices = np.random.choice(n_points, 3, replace=False)
        sample_points = points[sample_indices]
        
        # Fit plane to sampled points
        try:
            # Calculate plane normal using cross product
            v1 = sample_points[1] - sample_points[0]
            v2 = sample_points[2] - sample_points[0]
            normal = np.cross(v1, v2)
            
            # Check for degenerate case (collinear points)
            if np.linalg.norm(normal) < 1e-6:
                continue
            
            # Normalize normal vector
            normal = normal / np.linalg.norm(normal)
            
            # Ensure normal points upward (positive z component)
            if normal[2] < 0:
                normal = -normal
            
            # Calculate d parameter (ax + by + cz + d = 0)
            d = -np.dot(normal, sample_points[0])
            plane_params = np.append(normal, d)
            
        except Exception as e:
            continue
        
        # Compute distances from all points to the plane
        distances = np.abs(np.dot(points, plane_params[:3]) + plane_params[3]) / np.linalg.norm(plane_params[:3])
        
        # Find inliers
        inliers = np.where(distances < distance_threshold)[0]
        n_inliers = len(inliers)
        
        # Update best plane if we found more inliers
        if n_inliers > max_inliers and n_inliers >= min_inliers:
            best_plane_params = plane_params
            best_inliers = inliers
            max_inliers = n_inliers
            
            # Early stopping if we found a very good plane
            if n_inliers >= early_stop_threshold:
                logger.info(f"Early stopping at iteration {i+1}: Found {n_inliers:,} inliers")
                break
    
    end_time = time.time()
    
    if best_plane_params is not None:
        inlier_ratio = max_inliers / n_points
        logger.info(f"RANSAC completed in {end_time - start_time:.2f} seconds")
        logger.info(f"Best plane: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0")
        logger.info(f"Ground points: {max_inliers:,} ({inlier_ratio:.3f} ratio)")
        return best_inliers, best_plane_params
    else:
        logger.warning("RANSAC failed to find a valid ground plane")
        return np.array([]), None

# Execute RANSAC ground segmentation
print("\n" + "="*60)
print("EXECUTING RANSAC (RANDOM SAMPLE CONSENSUS)")
print("="*60)

ransac_start_time = time.time()

# Run RANSAC algorithm
ransac_ground_indices, ransac_plane_params = ransac_ground_detection(
    points,
    distance_threshold=ransac_distance_threshold,
    num_iterations=ransac_num_iterations,
    min_inliers_ratio=ransac_min_inliers_ratio,
    early_stop_ratio=ransac_early_stop_ratio
)

ransac_execution_time = time.time() - ransac_start_time

# Separate ground and non-ground points
if len(ransac_ground_indices) > 0:
    ransac_ground_points = points[ransac_ground_indices]
    ransac_nonground_mask = np.ones(len(points), dtype=bool)
    ransac_nonground_mask[ransac_ground_indices] = False
    ransac_nonground_points = points[ransac_nonground_mask]
else:
    # Fallback if RANSAC failed
    logger.warning("RANSAC failed - using fallback height-based segmentation")
    z_median = np.median(points[:, 2])
    ground_mask = points[:, 2] < (z_median + ransac_distance_threshold)
    ransac_ground_points = points[ground_mask]
    ransac_nonground_points = points[~ground_mask]

print(f"\nRANSAC Results Summary:")
print(f"  Execution time: {ransac_execution_time:.2f} seconds")
print(f"  Ground points: {len(ransac_ground_points):,}")
print(f"  Non-ground points: {len(ransac_nonground_points):,}")
print(f"  Ground ratio: {len(ransac_ground_points)/len(points):.3f}")
if ransac_plane_params is not None:
    print(f"  Plane equation: {ransac_plane_params[0]:.3f}x + {ransac_plane_params[1]:.3f}y + {ransac_plane_params[2]:.3f}z + {ransac_plane_params[3]:.3f} = 0")

# Calculate detailed metrics for RANSAC
ransac_metrics = calculate_segmentation_metrics(ransac_ground_points, ransac_nonground_points, len(points))

# Add RANSAC-specific metrics
if ransac_plane_params is not None:
    ransac_metrics['plane_normal_x'] = ransac_plane_params[0]
    ransac_metrics['plane_normal_y'] = ransac_plane_params[1]
    ransac_metrics['plane_normal_z'] = ransac_plane_params[2]
    ransac_metrics['plane_d'] = ransac_plane_params[3]
    
    # Calculate plane slope (angle from horizontal)
    plane_slope = np.arccos(abs(ransac_plane_params[2])) * 180 / np.pi
    ransac_metrics['plane_slope_degrees'] = plane_slope

print("\nRANSAC Detailed Metrics:")
print(f"  Ground points count: {ransac_metrics['ground_points_count']:,}")
print(f"  Non-ground points count: {ransac_metrics['nonground_points_count']:,}")
print(f"  Ground ratio: {ransac_metrics['ground_ratio']:.4f}")
print(f"  Ground mean height: {ransac_metrics['ground_z_mean']:.2f}m")
print(f"  Non-ground mean height: {ransac_metrics['nonground_z_mean']:.2f}m")
print(f"  Vertical separation: {ransac_metrics['z_separation']:.2f}m")
print(f"  Ground height std: {ransac_metrics['ground_z_std']:.2f}m")
print(f"  Non-ground height std: {ransac_metrics['nonground_z_std']:.2f}m")
if 'plane_slope_degrees' in ransac_metrics:
    print(f"  Ground plane slope: {ransac_metrics['plane_slope_degrees']:.2f} degrees")

# Store results for comparison
method_results['RANSAC'] = ransac_metrics
method_timings['RANSAC'] = ransac_execution_time

# Inference: RANSAC Performance Analysis
print("\nRANSAC Method Inference:")
if ransac_plane_params is not None:
    if ransac_metrics['plane_slope_degrees'] < 5:
        print("  - Detected ground plane is nearly horizontal - ideal for RANSAC")
    elif ransac_metrics['plane_slope_degrees'] < 15:
        print("  - Detected ground plane has moderate slope - good RANSAC performance")
    else:
        print("  - Detected ground plane has significant slope - may challenge RANSAC assumptions")
else:
    print("  - RANSAC failed to detect a dominant ground plane - terrain may be too complex")

if ransac_metrics['z_separation'] > 2.0:
    print("  - Excellent vertical separation - RANSAC clearly distinguished ground from structures")
elif ransac_metrics['z_separation'] > 1.0:
    print("  - Good vertical separation - RANSAC performed well")
else:
    print("  - Limited vertical separation - may indicate complex terrain or poor plane fit")

if ransac_metrics['ground_z_std'] < 0.3:
    print("  - Low ground height variation - confirms good planar fit")
elif ransac_metrics['ground_z_std'] < 0.8:
    print("  - Moderate ground height variation - acceptable planar fit")
else:
    print("  - High ground height variation - plane fit may be poor or terrain is non-planar")

# Compare with previous methods
print("\nComparison with Previous Methods:")
if 'CSF' in method_results and 'PMF' in method_results:
    csf_ratio = method_results['CSF']['ground_ratio']
    pmf_ratio = method_results['PMF']['ground_ratio']
    ransac_ratio = ransac_metrics['ground_ratio']
    
    ratios = [('CSF', csf_ratio), ('PMF', pmf_ratio), ('RANSAC', ransac_ratio)]
    ratios.sort(key=lambda x: x[1])
    
    print(f"  Ground ratio ranking: {ratios[0][0]} ({ratios[0][1]:.3f}) < {ratios[1][0]} ({ratios[1][1]:.3f}) < {ratios[2][0]} ({ratios[2][1]:.3f})")
    
    if abs(csf_ratio - ransac_ratio) < 0.05:
        print("  - CSF and RANSAC show very similar results")
    if abs(pmf_ratio - ransac_ratio) < 0.05:
        print("  - PMF and RANSAC show very similar results")
    if abs(csf_ratio - pmf_ratio) < 0.05:
        print("  - CSF and PMF show very similar results")

# Save RANSAC results with method-specific naming
print("\nSaving RANSAC segmentation results...")

# Save to main ground segmentation directory
ransac_ground_file = save_point_cloud_with_method_name(
    ground_seg_path, ransac_ground_points, "ransac", "ground"
)
ransac_nonground_file = save_point_cloud_with_method_name(
    ground_seg_path, ransac_nonground_points, "ransac", "nonground"
)

# Save to current run directory for this specific execution
ransac_ground_run_file = save_point_cloud_with_method_name(
    current_run_path, ransac_ground_points, "ransac", "ground"
)
ransac_nonground_run_file = save_point_cloud_with_method_name(
    current_run_path, ransac_nonground_points, "ransac", "nonground"
)

# Store artifact paths for MLflow
ransac_artifacts = {
    'ground_points': ransac_ground_file,
    'nonground_points': ransac_nonground_file
}

method_outputs['RANSAC'] = {
    'ground_file': ransac_ground_file,
    'nonground_file': ransac_nonground_file,
    'ground_points': ransac_ground_points,
    'nonground_points': ransac_nonground_points
}

print(f"RANSAC output files saved:")
print(f"  Ground points: {ransac_ground_file}")
print(f"  Non-ground points: {ransac_nonground_file}")

# Create visualization of RANSAC results
plt.figure(figsize=(15, 5))

# Height distribution comparison
plt.subplot(1, 3, 1)
plt.hist(ransac_ground_points[:, 2], bins=50, alpha=0.7, label='RANSAC Ground', color='purple')
plt.hist(ransac_nonground_points[:, 2], bins=50, alpha=0.7, label='RANSAC Non-Ground', color='yellow')
plt.xlabel('Height (Z) [m]')
plt.ylabel('Point Count')
plt.title('RANSAC: Height Distribution')
plt.legend()
plt.grid(True, alpha=0.3)

# 2D scatter plot (top view)
plt.subplot(1, 3, 2)
plt.scatter(ransac_ground_points[:, 0], ransac_ground_points[:, 1], 
           c='purple', s=0.1, alpha=0.6, label='RANSAC Ground')
plt.scatter(ransac_nonground_points[:, 0], ransac_nonground_points[:, 1], 
           c='yellow', s=0.1, alpha=0.6, label='RANSAC Non-Ground')
plt.xlabel('X [m]')
plt.ylabel('Y [m]')
plt.title('RANSAC: Top View Classification')
plt.legend()
plt.axis('equal')

# Height vs distance analysis
plt.subplot(1, 3, 3)
distances_ground = np.sqrt(ransac_ground_points[:, 0]**2 + ransac_ground_points[:, 1]**2)
distances_nonground = np.sqrt(ransac_nonground_points[:, 0]**2 + ransac_nonground_points[:, 1]**2)
plt.scatter(distances_ground, ransac_ground_points[:, 2], 
           c='purple', s=0.1, alpha=0.6, label='RANSAC Ground')
plt.scatter(distances_nonground, ransac_nonground_points[:, 2], 
           c='yellow', s=0.1, alpha=0.6, label='RANSAC Non-Ground')
plt.xlabel('Distance from Origin [m]')
plt.ylabel('Height (Z) [m]')
plt.title('RANSAC: Height vs Distance')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(current_run_path / f'{site_name}_ransac_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"\nRANSAC visualization saved: {current_run_path / f'{site_name}_ransac_analysis.png'}")

# Log RANSAC results to MLflow
ransac_artifacts['visualization'] = current_run_path / f'{site_name}_ransac_analysis.png'

log_method_results_to_mlflow(
    method_name="RANSAC",
    parameters=ransac_parameters,
    metrics=ransac_metrics,
    execution_time=ransac_execution_time,
    artifacts=ransac_artifacts
)

print("\nRANSAC method completed successfully!")
print(f"Results stored in method_results['RANSAC'] for comparison")
print("="*60)

# Create comprehensive comparison visualization
fig, axes = plt.subplots(3, 3, figsize=(18, 15))
fig.suptitle(f'Ground Segmentation Method Comparison - {site_name}', fontsize=16, fontweight='bold')

methods = ['CSF', 'PMF', 'RANSAC']
colors = [('green', 'red'), ('blue', 'orange'), ('purple', 'yellow')]

for i, (method, (ground_color, nonground_color)) in enumerate(zip(methods, colors)):
    if method in method_outputs:
        ground_pts = method_outputs[method]['ground_points']
        nonground_pts = method_outputs[method]['nonground_points']
        
        # Height distribution
        axes[i, 0].hist(ground_pts[:, 2], bins=30, alpha=0.7, label=f'{method} Ground', color=ground_color)
        axes[i, 0].hist(nonground_pts[:, 2], bins=30, alpha=0.7, label=f'{method} Non-Ground', color=nonground_color)
        axes[i, 0].set_xlabel('Height (Z) [m]')
        axes[i, 0].set_ylabel('Point Count')
        axes[i, 0].set_title(f'{method}: Height Distribution')
        axes[i, 0].legend()
        axes[i, 0].grid(True, alpha=0.3)
        
        # Top view
        axes[i, 1].scatter(ground_pts[:, 0], ground_pts[:, 1], c=ground_color, s=0.1, alpha=0.6, label=f'{method} Ground')
        axes[i, 1].scatter(nonground_pts[:, 0], nonground_pts[:, 1], c=nonground_color, s=0.1, alpha=0.6, label=f'{method} Non-Ground')
        axes[i, 1].set_xlabel('X [m]')
        axes[i, 1].set_ylabel('Y [m]')
        axes[i, 1].set_title(f'{method}: Top View')
        axes[i, 1].legend()
        axes[i, 1].axis('equal')
        
        # Performance metrics bar chart
        metrics = method_results[method]
        metric_names = ['Ground Ratio', 'Z Separation', 'Ground Z Std']
        metric_values = [metrics['ground_ratio'], metrics['z_separation'], metrics['ground_z_std']]
        
        bars = axes[i, 2].bar(metric_names, metric_values, color=[ground_color, 'gray', 'lightblue'])
        axes[i, 2].set_title(f'{method}: Key Metrics')
        axes[i, 2].set_ylabel('Value')
        
        # Add value labels on bars
        for bar, value in zip(bars, metric_values):
            axes[i, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.3f}', ha='center', va='bottom')

plt.tight_layout()
plt.savefig(current_run_path / f'{site_name}_method_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"\nComprehensive comparison saved: {current_run_path / f'{site_name}_method_comparison.png'}")

# Create performance summary table
import pandas as pd

summary_data = []
for method in ['CSF', 'PMF', 'RANSAC']:
    if method in method_results:
        metrics = method_results[method]
        timing = method_timings[method]
        
        summary_data.append({
            'Method': method,
            'Ground Points': f"{metrics['ground_points_count']:,}",
            'Ground Ratio': f"{metrics['ground_ratio']:.4f}",
            'Z Separation (m)': f"{metrics['z_separation']:.2f}",
            'Ground Z Std (m)': f"{metrics['ground_z_std']:.3f}",
            'Execution Time (s)': f"{timing:.2f}"
        })

summary_df = pd.DataFrame(summary_data)
print("\nMethod Performance Summary:")
print("=" * 80)
print(summary_df.to_string(index=False))
print("=" * 80)

# Save summary to CSV
summary_csv_path = current_run_path / f'{site_name}_method_summary.csv'
summary_df.to_csv(summary_csv_path, index=False)
print(f"\nSummary table saved: {summary_csv_path}")



print("\nWhy Different Methods Produce Different Results:")
print("=" * 60)

print("\n1. ALGORITHMIC APPROACH DIFFERENCES:")
print("   CSF (Cloth Simulation Filter):")
print("   - Uses physics-based simulation of cloth falling on inverted terrain")
print("   - Adapts to complex topography through particle dynamics")
print("   - Best for urban environments with buildings and irregular surfaces")
print("   - May be conservative in steep areas due to cloth rigidity")

print("\n   PMF (Progressive Morphological Filter):")
print("   - Uses mathematical morphology on rasterized height data")
print("   - Progressive window sizes capture multi-scale terrain features")
print("   - Effective for vegetated areas and complex terrain")
print("   - Performance depends on grid resolution and window size selection")

print("\n   RANSAC (Random Sample Consensus):")
print("   - Fits geometric planes to identify dominant ground surface")
print("   - Assumes ground can be approximated by planar surfaces")
print("   - Excellent for relatively flat terrain with clear ground plane")
print("   - May struggle with multi-level or highly irregular terrain")

print("\n2. PARAMETER SENSITIVITY:")
if 'CSF' in method_results and 'PMF' in method_results and 'RANSAC' in method_results:
    csf_ratio = method_results['CSF']['ground_ratio']
    pmf_ratio = method_results['PMF']['ground_ratio']
    ransac_ratio = method_results['RANSAC']['ground_ratio']
    
    max_ratio = max(csf_ratio, pmf_ratio, ransac_ratio)
    min_ratio = min(csf_ratio, pmf_ratio, ransac_ratio)
    ratio_spread = max_ratio - min_ratio
    
    print(f"   Ground ratio spread: {ratio_spread:.3f} ({ratio_spread/min_ratio*100:.1f}% variation)")
    
    if ratio_spread > 0.2:
        print("   - High variation suggests terrain complexity challenges different algorithms")
        print("   - Consider terrain-specific parameter tuning for optimal results")
    elif ratio_spread > 0.1:
        print("   - Moderate variation is normal for different algorithmic approaches")
        print("   - Results suggest reasonable consistency across methods")
    else:
        print("   - Low variation indicates good agreement between methods")
        print("   - Terrain characteristics are well-suited for all approaches")

print("\n3. TERRAIN-SPECIFIC PERFORMANCE:")
for method in ['CSF', 'PMF', 'RANSAC']:
    if method in method_results:
        metrics = method_results[method]
        z_sep = metrics['z_separation']
        ground_std = metrics['ground_z_std']
        
        print(f"\n   {method} Analysis:")
        if z_sep > 2.0:
            print(f"     - Excellent separation ({z_sep:.2f}m) indicates clear ground/object distinction")
        elif z_sep > 1.0:
            print(f"     - Good separation ({z_sep:.2f}m) shows effective segmentation")
        else:
            print(f"     - Limited separation ({z_sep:.2f}m) may indicate challenging terrain")
        
        if ground_std < 0.5:
            print(f"     - Low ground variation ({ground_std:.3f}m) suggests flat, uniform surface")
        else:
            print(f"     - Higher ground variation ({ground_std:.3f}m) indicates uneven terrain")

print("\n4. RECOMMENDATIONS FOR METHOD SELECTION:")
print("   - For flat, open areas: RANSAC (fastest, effective for planar surfaces)")
print("   - For complex urban environments: CSF (handles buildings and structures well)")
print("   - For vegetated or mixed terrain: PMF (good for multi-scale features)")
print("   - For critical applications: Use multiple methods and compare results")
print("   - Consider ensemble approaches combining strengths of different methods")

# Final execution summary
total_execution_time = sum(method_timings.values())
total_files_created = len([f for method_output in method_outputs.values() 
                          for f in [method_output['ground_file'], method_output['nonground_file']] 
                          if f and Path(f).exists()])

print("\n" + "="*80)
print("COMPREHENSIVE GROUND SEGMENTATION EXECUTION SUMMARY")
print("="*80)

print(f"\nProject Details:")
print(f"  Site: {site_name}")
print(f"  Project Type: {project_type}")
print(f"  Input Points: {len(points):,}")
print(f"  Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

print(f"\nExecution Results:")
print(f"  Methods Executed: {len(method_results)}")
print(f"  Total Execution Time: {total_execution_time:.2f} seconds")
print(f"  Output Files Created: {total_files_created}")
print(f"  MLflow Tracking: {'Enabled' if MLFLOW_AVAILABLE else 'Disabled'}")

print(f"\nMethod Performance Summary:")
for method in ['CSF', 'PMF', 'RANSAC']:
    if method in method_results:
        metrics = method_results[method]
        timing = method_timings[method]
        print(f"  {method:>7}: {metrics['ground_points_count']:>8,} ground pts ({metrics['ground_ratio']:.3f} ratio) in {timing:>6.2f}s")

print(f"\nOutput Locations:")
print(f"  Main Output: {ground_seg_path}")
print(f"  Run-Specific: {current_run_path}")

# Validation checks
print(f"\nValidation Checks:")
validation_passed = True

# Check if all methods executed
expected_methods = ['CSF', 'PMF', 'RANSAC']
executed_methods = list(method_results.keys())
if set(executed_methods) == set(expected_methods):
    print("  ✓ All three methods executed successfully")
else:
    print(f"  ✗ Missing methods: {set(expected_methods) - set(executed_methods)}")
    validation_passed = False

# Check if output files exist
missing_files = []
for method, outputs in method_outputs.items():
    for file_type, file_path in [('ground', outputs['ground_file']), ('nonground', outputs['nonground_file'])]:
        if not file_path or not Path(file_path).exists():
            missing_files.append(f"{method}_{file_type}")

if not missing_files:
    print("  ✓ All output files created successfully")
else:
    print(f"  ✗ Missing output files: {missing_files}")
    validation_passed = False

# Check MLflow logging
if MLFLOW_AVAILABLE:
    print("  ✓ MLflow experiment tracking completed")
else:
    print("  ! MLflow tracking disabled (install mlflow to enable)")

# Overall validation result
if validation_passed:
    print("\n✓ EXECUTION COMPLETED SUCCESSFULLY")
    print("  All methods executed, files created, and results logged.")
else:
    print("\n⚠ EXECUTION COMPLETED WITH ISSUES")
    print("  Check validation errors above.")

print("\n" + "="*80)

# Log comprehensive summary to MLflow
if MLFLOW_AVAILABLE:
    with mlflow.start_run(run_name=f"comprehensive_summary_{site_name}"):
        # Log overall parameters
        mlflow.log_param("site_name", site_name)
        mlflow.log_param("project_type", project_type)
        mlflow.log_param("total_points", len(points))
        mlflow.log_param("methods_executed", ",".join(method_results.keys()))
        
        # Log overall metrics
        mlflow.log_metric("total_execution_time", total_execution_time)
        mlflow.log_metric("files_created", total_files_created)
        mlflow.log_metric("methods_count", len(method_results))
        
        # Log method comparison metrics
        if len(method_results) > 1:
            ground_ratios = [metrics['ground_ratio'] for metrics in method_results.values()]
            mlflow.log_metric("ground_ratio_mean", np.mean(ground_ratios))
            mlflow.log_metric("ground_ratio_std", np.std(ground_ratios))
            mlflow.log_metric("ground_ratio_range", max(ground_ratios) - min(ground_ratios))
        
        # Log summary artifacts
        summary_artifacts = {
            'method_comparison': current_run_path / f'{site_name}_method_comparison.png',
            'summary_table': current_run_path / f'{site_name}_method_summary.csv'
        }
        
        for artifact_name, artifact_path in summary_artifacts.items():
            if artifact_path.exists():
                mlflow.log_artifact(str(artifact_path), artifact_name)
        
        print("\nComprehensive summary logged to MLflow")

print("\nNotebook execution completed successfully!")
print(f"All results available in: {current_run_path}")
print(f"Method-specific files in: {ground_seg_path}")

if MLFLOW_AVAILABLE:
    print(f"\nMLflow experiment: {experiment_name}")
    print("View results with: mlflow ui")