{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔍 RANSAC Plane Detection for Ground Segmentation\n", "\n", "This notebook implements the Random Sample Consensus (RANSAC) algorithm for detecting planes in point cloud data, with a focus on ground segmentation and solar panel analysis. It demonstrates how to:\n", "\n", "1. Load raw or aligned point clouds\n", "2. Implement and apply RANSAC for ground plane detection\n", "3. Detect multiple planes (ground, solar panels, buildings)\n", "4. Visualize the detected planes\n", "5. Export segmented point clouds\n", "\n", "**Stage**: Ground Segmentation / Plane Detection  \n", "**Input Data**: Raw or aligned point cloud  \n", "**Output**: Segmented point clouds by plane  \n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** December 2024  \n", "**Project:** Energy Inspection 3D"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Installation\n", "\n", "First, let's install the necessary dependencies and import required libraries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "# Uncomment and run this cell if you need to install the packages\n", "\n", "# !pip install numpy matplotlib open3d scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import random\n", "import time\n", "import os\n", "import logging\n", "import sys\n", "from sklearn.neighbors import NearestNeighbors\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Try to import Open3D for point cloud visualization\n", "try:\n", "    import open3d as o3d\n", "    O3D_SUPPORT = True\n", "    logger.info(\"Open3D support is available.\")\n", "except ImportError:\n", "    logger.warning(\"open3d not installed. Some visualizations may not be available.\")\n", "    O3D_SUPPORT = False\n", "\n", "# Check if running in Google Colab\n", "try:\n", "    from google.colab import drive\n", "    drive.mount('/content/gdrive')\n", "    IN_COLAB = True\n", "    logger.info(\"Google Drive mounted successfully.\")\n", "except ImportError:\n", "    IN_COLAB = False\n", "    logger.info(\"Not running in Google Colab. Using local file system.\")\n", "\n", "# Add the src directory to the path so we can import our modules\n", "module_path = os.path.abspath(os.path.join(os.path.dirname(os.getcwd()), '..'))\n", "if module_path not in sys.path:\n", "    sys.path.append(module_path)\n", "    print(f\"Added {module_path} to sys.path\")\n", "\n", "# Import our RANSAC implementation\n", "try:\n", "    from src.plane_detection.ransac import fit_plane, compute_plane_distance, ransac_plane_detection, detect_multiple_planes, save_planes_data\n", "    RANSAC_MODULE_AVAILABLE = True\n", "    print(\"Successfully imported RANSAC module from src.plane_detection.ransac\")\n", "except ImportError as e:\n", "    RANSAC_MODULE_AVAILABLE = False\n", "    print(f\"Could not import RANSAC module: {e}\")\n", "    print(\"Will use the notebook implementation instead.\")\n", "\n", "# Print version information\n", "print(\"NumPy version:\", np.__version__)\n", "if O3D_SUPPORT:\n", "    print(\"Open3D version:\", o3d.__version__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Point Cloud Loading and Preprocessing\n", "\n", "Now let's define functions to load and preprocess point clouds. We'll use these functions to load an aligned point cloud for plane detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"\n", "    Load a point cloud from a file.\n", "    \n", "    Parameters:\n", "    -----------\n", "    file_path : str\n", "        Path to the point cloud file\n", "        \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    metadata : dict\n", "        Metadata about the point cloud\n", "    \"\"\"\n", "    try:\n", "        # Get file extension\n", "        _, ext = os.path.splitext(file_path)\n", "        ext = ext.lower()\n", "        \n", "        # Load based on file type\n", "        if ext == '.npy':\n", "            # Load from NumPy file\n", "            points = np.load(file_path)\n", "            logger.info(f\"Loaded {points.shape[0]} points from NumPy file\")\n", "            return points, {\"source\": \"numpy\", \"path\": file_path}\n", "            \n", "        elif ext in ['.ply', '.pcd', '.obj']:\n", "            if not O3D_SUPPORT:\n", "                logger.error(f\"Cannot load {ext} file without Open3D\")\n", "                return None, {\"error\": f\"Cannot load {ext} file without Open3D\"}\n", "                \n", "            # Load using Open3D\n", "            if ext == '.ply':\n", "                pcd = o3d.io.read_point_cloud(file_path)\n", "            elif ext == '.pcd':\n", "                pcd = o3d.io.read_point_cloud(file_path)\n", "            elif ext == '.obj':\n", "                mesh = o3d.io.read_triangle_mesh(file_path)\n", "                pcd = mesh.sample_points_uniformly(number_of_points=100000)\n", "                \n", "            # Convert to numpy array\n", "            points = np.asarray(pcd.points)\n", "            logger.info(f\"Loaded {points.shape[0]} points from {ext} file\")\n", "            return points, {\"source\": \"open3d\", \"path\": file_path}\n", "            \n", "        else:\n", "            logger.error(f\"Unsupported file format: {ext}\")\n", "            return None, {\"error\": f\"Unsupported file format: {ext}\"}\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"Error loading point cloud: {e}\")\n", "        return None, {\"error\": str(e)}\n", "\n", "def preprocess_point_cloud(points, downsample=True, voxel_size=0.05, remove_outliers=True):\n", "    \"\"\"\n", "    Preprocess a point cloud by downsampling and removing outliers.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    downsample : bool\n", "        Whether to downsample the point cloud\n", "    voxel_size : float\n", "        Voxel size for downsampling\n", "    remove_outliers : bool\n", "        Whether to remove outliers\n", "        \n", "    Returns:\n", "    --------\n", "    processed_points : numpy.n<PERSON><PERSON>\n", "        Preprocessed point cloud data\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.warning(\"Open3D not available, skipping preprocessing\")\n", "        return points\n", "        \n", "    try:\n", "        # Convert to Open3D point cloud\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        \n", "        # Downsample\n", "        if downsample:\n", "            pcd = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "            logger.info(f\"Downsampled to {len(pcd.points)} points with voxel size {voxel_size}\")\n", "        \n", "        # Remove outliers\n", "        if remove_outliers:\n", "            pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "            logger.info(f\"Removed outliers, {len(pcd.points)} points remaining\")\n", "        \n", "        # Convert back to numpy array\n", "        processed_points = np.asarray(pcd.points)\n", "        return processed_points\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error preprocessing point cloud: {e}\")\n", "        return points"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths to your point cloud files\n", "if IN_COLAB:\n", "    # Google Drive paths\n", "    base_path = '/content/gdrive/MyDrive/pc-experiment'\n", "else:\n", "    # Local paths - adjust as needed\n", "    base_path = '../data/pc-experiment'\n", "\n", "# Create output directory if it doesn't exist\n", "output_dir = os.path.join(base_path, 'output')\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Path to aligned point cloud - adjust this to your specific file\n", "aligned_pc_path = os.path.join(output_dir, 'aligned_pointcloud.ply')\n", "\n", "# Check if the aligned point cloud exists\n", "if os.path.exists(aligned_pc_path):\n", "    print(f\"Found aligned point cloud at {aligned_pc_path}\")\n", "else:\n", "    print(f\"Aligned point cloud not found at {aligned_pc_path}\")\n", "    print(\"You may need to run the alignment notebook first or adjust the path.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. RANSAC Algorithm Implementation\n", "\n", "Now let's implement the RANSAC algorithm for plane detection. The algorithm works as follows:\n", "\n", "1. Randomly select a minimal subset of points (3 points for a plane)\n", "2. Compute the plane parameters using the selected points\n", "3. Determine the set of points that are within a threshold distance from the plane (inliers)\n", "4. If the number of inliers is sufficiently large, re-estimate the plane parameters using all inliers\n", "5. Repeat steps 1-4 for a predetermined number of iterations\n", "6. Select the plane with the largest number of inliers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fit_plane(points):\n", "    \"\"\"\n", "    Fit a plane to a set of points using least squares.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Points to fit a plane to (N x 3)\n", "        \n", "    Returns:\n", "    --------\n", "    plane_params : numpy.n<PERSON><PERSON>\n", "        Plane parameters [a, b, c, d] where ax + by + cz + d = 0\n", "    \"\"\"\n", "    # Ensure we have at least 3 points\n", "    if points.shape[0] < 3:\n", "        return None\n", "    \n", "    # Center the points\n", "    centroid = np.mean(points, axis=0)\n", "    centered_points = points - centroid\n", "    \n", "    # Compute the covariance matrix\n", "    cov = np.dot(centered_points.T, centered_points) / points.shape[0]\n", "    \n", "    # Compute the eigenvectors and eigenvalues\n", "    eigenvalues, eigenvectors = np.linalg.eigh(cov)\n", "    \n", "    # The normal vector is the eigenvector corresponding to the smallest eigenvalue\n", "    normal = eigenvectors[:, 0]\n", "    \n", "    # Ensure the normal vector points upward (positive z)\n", "    if normal[2] < 0:\n", "        normal = -normal\n", "    \n", "    # Compute the d parameter\n", "    d = -np.dot(normal, centroid)\n", "    \n", "    # Return the plane parameters [a, b, c, d]\n", "    return np.append(normal, d)\n", "\n", "def compute_plane_distance(points, plane_params):\n", "    \"\"\"\n", "    Compute the distance from points to a plane.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Points to compute distance for (N x 3)\n", "    plane_params : numpy.n<PERSON><PERSON>\n", "        Plane parameters [a, b, c, d] where ax + by + cz + d = 0\n", "        \n", "    Returns:\n", "    --------\n", "    distances : numpy.ndarray\n", "        Distances from points to the plane\n", "    \"\"\"\n", "    # Extract plane parameters\n", "    a, b, c, d = plane_params\n", "    \n", "    # Compute the distance\n", "    # Distance = |ax + by + cz + d| / sqrt(a^2 + b^2 + c^2)\n", "    numerator = np.abs(np.dot(points, plane_params[:3]) + d)\n", "    denominator = np.sqrt(a**2 + b**2 + c**2)\n", "    \n", "    return numerator / denominator\n", "\n", "def ransac_plane_detection(points, distance_threshold=0.01, num_iterations=1000, min_inliers=100, early_stop_ratio=0.8):\n", "    \"\"\"\n", "    Detect planes in a point cloud using RANSAC.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    distance_threshold : float\n", "        Maximum distance for a point to be considered an inlier\n", "    num_iterations : int\n", "        Number of iterations to perform\n", "    min_inliers : int\n", "        Minimum number of inliers required to accept a plane\n", "    early_stop_ratio : float\n", "        Ratio of inliers to total points for early stopping\n", "        \n", "    Returns:\n", "    --------\n", "    best_plane_params : numpy.n<PERSON><PERSON>\n", "        Parameters of the best plane [a, b, c, d]\n", "    best_inliers : numpy.n<PERSON><PERSON>\n", "        Indices of inlier points for the best plane\n", "    \"\"\"\n", "    # Initialize variables\n", "    best_plane_params = None\n", "    best_inliers = []\n", "    max_inliers = 0\n", "    n_points = points.shape[0]\n", "    \n", "    # Start timer\n", "    start_time = time.time()\n", "    \n", "    # Run RANSAC iterations\n", "    for i in range(num_iterations):\n", "        # Randomly select 3 points\n", "        sample_indices = random.sample(range(n_points), 3)\n", "        sample_points = points[sample_indices]\n", "        \n", "        # Fit a plane to the sampled points\n", "        plane_params = fit_plane(sample_points)\n", "        if plane_params is None:\n", "            continue\n", "        \n", "        # Compute distances from all points to the plane\n", "        distances = compute_plane_distance(points, plane_params)\n", "        \n", "        # Find inliers\n", "        inliers = np.where(distances < distance_threshold)[0]\n", "        n_inliers = len(inliers)\n", "        \n", "        # Update best plane if we found more inliers\n", "        if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "            # Refit the plane using all inliers\n", "            refined_plane_params = fit_plane(points[inliers])\n", "            if refined_plane_params is not None:\n", "                best_plane_params = refined_plane_params\n", "                best_inliers = inliers\n", "                max_inliers = n_inliers\n", "                \n", "                # Log progress\n", "                if i % 100 == 0 or i == num_iterations - 1:\n", "                    logger.info(f\"Iteration {i+1}/{num_iterations}: Found {n_inliers} inliers\")\n", "                \n", "                # Early stopping if we found a very good plane\n", "                inlier_ratio = n_inliers / n_points\n", "                if inlier_ratio > early_stop_ratio:\n", "                    logger.info(f\"Early stopping at iteration {i+1}: Found {n_inliers} inliers ({inlier_ratio:.2f} ratio)\")\n", "                    break\n", "    \n", "    # End timer\n", "    end_time = time.time()\n", "    logger.info(f\"RANSAC completed in {end_time - start_time:.2f} seconds\")\n", "    logger.info(f\"Best plane has {max_inliers} inliers ({max_inliers / n_points:.2f} ratio)\")\n", "    \n", "    return best_plane_params, best_inliers\n", "\n", "def detect_multiple_planes(points, max_planes=10, distance_threshold=0.01, num_iterations=1000, min_inliers=100, min_ratio=0.05):\n", "    \"\"\"\n", "    Detect multiple planes in a point cloud using RANSAC.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    max_planes : int\n", "        Maximum number of planes to detect\n", "    distance_threshold : float\n", "        Maximum distance for a point to be considered an inlier\n", "    num_iterations : int\n", "        Number of iterations to perform for each plane\n", "    min_inliers : int\n", "        Minimum number of inliers required to accept a plane\n", "    min_ratio : float\n", "        Minimum ratio of inliers to remaining points\n", "        \n", "    Returns:\n", "    --------\n", "    planes : list of dict\n", "        List of detected planes, each containing:\n", "        - 'equation': Plane equation coefficients [a, b, c, d]\n", "        - 'inliers': Indices of inlier points in the original point cloud\n", "        - 'points': Inlier points\n", "    \"\"\"\n", "    # Make a copy of the points\n", "    remaining_points = np.copy(points)\n", "    original_indices = np.arange(points.shape[0])\n", "    planes = []\n", "    \n", "    for i in range(max_planes):\n", "        # Check if we have enough points left\n", "        if remaining_points.shape[0] < min_inliers:\n", "            logger.info(f\"Stopping after {i} planes: Not enough points left\")\n", "            break\n", "            \n", "        # Detect a plane\n", "        logger.info(f\"\\nDetecting plane {i+1}/{max_planes}...\")\n", "        plane_params, inliers = ransac_plane_detection(\n", "            remaining_points, \n", "            distance_threshold=distance_threshold,\n", "            num_iterations=num_iterations,\n", "            min_inliers=min_inliers\n", "        )\n", "        \n", "        # Check if we found a plane\n", "        if plane_params is None or len(inliers) < min_inliers:\n", "            logger.info(f\"No more planes found with at least {min_inliers} inliers\")\n", "            break\n", "            \n", "        # Check if the plane has enough inliers relative to remaining points\n", "        inlier_ratio = len(inliers) / remaining_points.shape[0]\n", "        if inlier_ratio < min_ratio:\n", "            logger.info(f\"Stopping: Plane {i+1} has only {inlier_ratio:.2f} inlier ratio (below {min_ratio})\")\n", "            break\n", "            \n", "        # Get the original indices of the inliers\n", "        original_inliers = original_indices[inliers]\n", "        \n", "        # Add the plane to the list\n", "        planes.append({\n", "            'equation': plane_params,\n", "            'inliers': original_inliers,\n", "            'points': points[original_inliers]\n", "        })\n", "        \n", "        # Remove inliers from the remaining points\n", "        mask = np.ones(remaining_points.shape[0], dtype=bool)\n", "        mask[inliers] = False\n", "        remaining_points = remaining_points[mask]\n", "        original_indices = original_indices[mask]\n", "        \n", "        logger.info(f\"Plane {i+1} detected with {len(inliers)} inliers\")\n", "        logger.info(f\"Remaining points: {remaining_points.shape[0]}\")\n", "    \n", "    logger.info(f\"\\nDetected {len(planes)} planes in total\")\n", "    return planes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Visualization Functions\n", "\n", "Now let's define functions to visualize the point cloud and detected planes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_point_cloud(points, title=\"Point Cloud\"):\n", "    \"\"\"\n", "    Visualize a point cloud using Open3D.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    title : str\n", "        Window title\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.warning(\"Open3D not available, skipping visualization\")\n", "        return\n", "        \n", "    # Create Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Visualize\n", "    o3d.visualization.draw_geometries([pcd], window_name=title)\n", "\n", "def visualize_planes(points, planes, title=\"Detected Planes\"):\n", "    \"\"\"\n", "    Visualize detected planes in a point cloud using Open3D.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    planes : list of dict\n", "        List of detected planes, each containing:\n", "        - 'equation': Plane equation coefficients [a, b, c, d]\n", "        - 'inliers': Indices of inlier points\n", "        - 'points': Inlier points\n", "    title : str\n", "        Window title\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.warning(\"Open3D not available, skipping visualization\")\n", "        return\n", "        \n", "    # Create a list of geometries to visualize\n", "    geometries = []\n", "    \n", "    # Add the original point cloud with gray color\n", "    original_pcd = o3d.geometry.PointCloud()\n", "    original_pcd.points = o3d.utility.Vector3dVector(points)\n", "    original_pcd.paint_uniform_color([0.5, 0.5, 0.5])  # Gray color\n", "    geometries.append(original_pcd)\n", "    \n", "    # Define colors for planes\n", "    colors = [\n", "        [1, 0, 0],    # Red\n", "        [0, 1, 0],    # Green\n", "        [0, 0, 1],    # Blue\n", "        [1, 1, 0],    # Yellow\n", "        [1, 0, 1],    # <PERSON><PERSON><PERSON>\n", "        [0, 1, 1],    # <PERSON><PERSON>\n", "        [0.5, 0.5, 0], # <PERSON>\n", "        [0.5, 0, 0.5], # Purple\n", "        [0, 0.5, 0.5], # <PERSON><PERSON>\n", "        [1, 0.5, 0]   # Orange\n", "    ]\n", "    \n", "    # Add each plane with a different color\n", "    for i, plane in enumerate(planes):\n", "        plane_pcd = o3d.geometry.PointCloud()\n", "        plane_pcd.points = o3d.utility.Vector3dVector(plane['points'])\n", "        plane_pcd.paint_uniform_color(colors[i % len(colors)])\n", "        geometries.append(plane_pcd)\n", "    \n", "    # Visualize\n", "    o3d.visualization.draw_geometries(geometries, window_name=title)\n", "\n", "def visualize_plane_matplotlib(points, plane_params, inliers=None, ax=None, title=\"Detected Plane\"):\n", "    \"\"\"\n", "    Visualize a plane in a point cloud using Matplotlib.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    plane_params : numpy.n<PERSON><PERSON>\n", "        Plane parameters [a, b, c, d]\n", "    inliers : numpy.ndarray\n", "        Indices of inlier points\n", "    ax : matplotlib.axes.Axes\n", "        Axes to plot on\n", "    title : str\n", "        Plot title\n", "    \"\"\"\n", "    # Create a new figure if ax is not provided\n", "    if ax is None:\n", "        fig = plt.figure(figsize=(10, 8))\n", "        ax = fig.add_subplot(111, projection='3d')\n", "    \n", "    # Extract plane parameters\n", "    a, b, c, d = plane_params\n", "    \n", "    # Plot all points in gray\n", "    ax.scatter(points[:, 0], points[:, 1], points[:, 2], c='gray', s=1, alpha=0.5)\n", "    \n", "    # Plot inliers in red\n", "    if inliers is not None:\n", "        ax.scatter(points[inliers, 0], points[inliers, 1], points[inliers, 2], c='red', s=2)\n", "    \n", "    # Create a meshgrid for the plane\n", "    if abs(c) > 1e-10:  # Avoid division by zero\n", "        # Get the range of x and y coordinates\n", "        x_min, x_max = np.min(points[:, 0]), np.max(points[:, 0])\n", "        y_min, y_max = np.min(points[:, 1]), np.max(points[:, 1])\n", "        \n", "        # Create a meshgrid\n", "        xx, yy = np.meshgrid(np.linspace(x_min, x_max, 10), np.linspace(y_min, y_max, 10))\n", "        \n", "        # Calculate z values for the plane\n", "        zz = (-a * xx - b * yy - d) / c\n", "        \n", "        # Plot the plane\n", "        ax.plot_surface(xx, yy, zz, alpha=0.2, color='blue')\n", "    \n", "    # Set labels and title\n", "    ax.set_xlabel('X')\n", "    ax.set_ylabel('Y')\n", "    ax.set_zlabel('Z')\n", "    ax.set_title(title)\n", "    \n", "    return ax"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Load and Process Point Cloud\n", "\n", "Now let's load the aligned point cloud and preprocess it for plane detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the aligned point cloud\n", "print(\"\\n=== Loading Aligned Point Cloud ===\\n\")\n", "\n", "# Check if the aligned point cloud exists\n", "if os.path.exists(aligned_pc_path):\n", "    # Load the point cloud\n", "    aligned_points, metadata = load_point_cloud(aligned_pc_path)\n", "    if aligned_points is not None:\n", "        print(f\"Successfully loaded aligned point cloud with {aligned_points.shape[0]} points\")\n", "    else:\n", "        print(f\"Failed to load aligned point cloud from {aligned_pc_path}\")\n", "        # Create a synthetic point cloud for testing\n", "        print(\"Creating a synthetic point cloud for testing...\")\n", "        aligned_points = np.random.rand(10000, 3) * 10\n", "else:\n", "    print(f\"Aligned point cloud not found at {aligned_pc_path}\")\n", "    print(\"Creating a synthetic point cloud for testing...\")\n", "    # Create a synthetic point cloud with multiple planes for testing\n", "    aligned_points = np.random.rand(10000, 3) * 10\n", "\n", "# Preprocess the point cloud\n", "print(\"\\n=== Preprocessing Point Cloud ===\\n\")\n", "processed_points = preprocess_point_cloud(\n", "    aligned_points,\n", "    downsample=True,\n", "    voxel_size=0.05,\n", "    remove_outliers=True\n", ")\n", "print(f\"Preprocessed point cloud has {processed_points.shape[0]} points\")\n", "\n", "# Visualize the preprocessed point cloud\n", "if O3D_SUPPORT and not IN_COLAB:\n", "    print(\"\\n=== Visualizing Preprocessed Point Cloud ===\\n\")\n", "    visualize_point_cloud(processed_points, title=\"Preprocessed Point Cloud\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Detect Planes using RANSAC\n", "\n", "Now let's apply the RANSAC algorithm to detect planes in the point cloud."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Detect planes using RANSAC\n", "print(\"\\n=== Detecting Planes using RANSAC ===\\n\")\n", "\n", "# Set RANSAC parameters\n", "distance_threshold = 0.02  # Maximum distance for a point to be considered an inlier\n", "num_iterations = 1000      # Number of iterations to perform\n", "min_inliers = 100          # Minimum number of inliers required to accept a plane\n", "max_planes = 10            # Maximum number of planes to detect\n", "min_ratio = 0.05           # Minimum ratio of inliers to remaining points\n", "\n", "# Create a subdirectory for RANSAC output\n", "ransac_output_dir = os.path.join(output_dir, 'ransac')\n", "os.makedirs(ransac_output_dir, exist_ok=True)\n", "print(f\"RANSAC output directory: {ransac_output_dir}\")\n", "\n", "# Detect multiple planes using either the module or notebook implementation\n", "if RANSAC_MODULE_AVAILABLE:\n", "    print(\"Using modular RANSAC implementation from src.plane_detection.ransac\")\n", "    # Detect planes using the modular implementation\n", "    detected_planes = detect_multiple_planes(\n", "        processed_points,\n", "        max_planes=max_planes,\n", "        distance_threshold=distance_threshold,\n", "        num_iterations=num_iterations,\n", "        min_inliers=min_inliers,\n", "        min_ratio=min_ratio\n", "    )\n", "    \n", "    # Save the planes data using the modular implementation\n", "    planes_file = save_planes_data(\n", "        detected_planes,\n", "        ransac_output_dir,\n", "        save_point_clouds=True,\n", "        point_cloud_format='ply'\n", "    )\n", "    print(f\"Saved detected planes to {planes_file}\")\n", "else:\n", "    print(\"Using notebook RANSAC implementation\")\n", "    # Detect multiple planes using the notebook implementation\n", "    detected_planes = detect_multiple_planes(\n", "        processed_points,\n", "        max_planes=max_planes,\n", "        distance_threshold=distance_threshold,\n", "        num_iterations=num_iterations,\n", "        min_inliers=min_inliers,\n", "        min_ratio=min_ratio\n", "    )\n", "\n", "# Print information about detected planes\n", "print(f\"\\nDetected {len(detected_planes)} planes:\")\n", "for i, plane in enumerate(detected_planes):\n", "    a, b, c, d = plane['equation']\n", "    n_inliers = len(plane['inliers'])\n", "    inlier_ratio = n_inliers / processed_points.shape[0]\n", "    print(f\"Plane {i+1}: {n_inliers} inliers ({inlier_ratio:.2f} ratio)\")\n", "    print(f\"  Equation: {a:.4f}x + {b:.4f}y + {c:.4f}z + {d:.4f} = 0\")\n", "    print(f\"  Normal vector: [{a:.4f}, {b:.4f}, {c:.4f}]\")\n", "    print(f\"  Original indices preserved: {len(plane['inliers'])} indices\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Visualize Detected Planes\n", "\n", "Now let's visualize the detected planes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the detected planes using Open3D\n", "if O3D_SUPPORT and not IN_COLAB:\n", "    print(\"\\n=== Visualizing Detected Planes with Open3D ===\\n\")\n", "    visualize_planes(processed_points, detected_planes, title=\"Detected Planes\")\n", "\n", "# Visualize the detected planes using Matplotlib\n", "print(\"\\n=== Visualizing Detected Planes with Matplotlib ===\\n\")\n", "\n", "# Create a figure with subplots for each plane\n", "n_planes = len(detected_planes)\n", "if n_planes > 0:\n", "    # Determine the grid size\n", "    n_cols = min(3, n_planes)\n", "    n_rows = (n_planes + n_cols - 1) // n_cols\n", "    \n", "    # Create the figure\n", "    fig = plt.figure(figsize=(5*n_cols, 4*n_rows))\n", "    \n", "    # Plot each plane\n", "    for i, plane in enumerate(detected_planes):\n", "        ax = fig.add_subplot(n_rows, n_cols, i+1, projection='3d')\n", "        visualize_plane_matplotlib(\n", "            processed_points,\n", "            plane['equation'],\n", "            plane['inliers'],\n", "            ax=ax,\n", "            title=f\"Plane {i+1}: {len(plane['inliers'])} inliers\"\n", "        )\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"No planes detected to visualize.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Save Detected Planes\n", "\n", "Finally, let's save the detected planes for further analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the detected planes (if not already saved by the modular implementation)\n", "print(\"\\n=== Saving Detected Planes ===\\n\")\n", "\n", "if not RANSAC_MODULE_AVAILABLE:\n", "    # Create a dictionary to store the plane information\n", "    planes_data = {\n", "        'num_planes': len(detected_planes),\n", "        'planes': [],\n", "        'metadata': {\n", "            'coordinate_system': {\n", "                'description': 'Right-handed coordinate system with Z-axis pointing up',\n", "                'x_axis': 'East',\n", "                'y_axis': 'North',\n", "                'z_axis': 'Up',\n", "                'units': 'meters'\n", "            },\n", "            'creation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')\n", "        }\n", "    }\n", "    \n", "    # Add information for each plane\n", "    for i, plane in enumerate(detected_planes):\n", "        plane_info = {\n", "            'equation': plane['equation'].tolist(),\n", "            'num_inliers': len(plane['inliers']),\n", "            'inlier_ratio': len(plane['inliers']) / processed_points.shape[0],\n", "            'original_indices': plane['inliers'].tolist()  # Save the original indices\n", "        }\n", "        planes_data['planes'].append(plane_info)\n", "    \n", "    # Save the planes data as a NumPy file\n", "    planes_file = os.path.join(ransac_output_dir, 'detected_planes.npy')\n", "    np.save(planes_file, planes_data)\n", "    print(f\"Saved detected planes to {planes_file}\")\n", "    \n", "    # Save each plane's points as a separate PLY file\n", "    if O3D_SUPPORT:\n", "        for i, plane in enumerate(detected_planes):\n", "            # Create an Open3D point cloud for the plane\n", "            plane_pcd = o3d.geometry.PointCloud()\n", "            plane_pcd.points = o3d.utility.Vector3dVector(plane['points'])\n", "            \n", "            # Save the point cloud\n", "            plane_file = os.path.join(ransac_output_dir, f'plane_{i+1}.ply')\n", "            o3d.io.write_point_cloud(plane_file, plane_pcd)\n", "            print(f\"Saved plane {i+1} to {plane_file}\")\n", "            \n", "            # Also save the original indices for each plane\n", "            indices_file = os.path.join(ransac_output_dir, f'plane_{i+1}_indices.npy')\n", "            np.save(indices_file, plane['inliers'])\n", "            print(f\"Saved plane {i+1} indices to {indices_file}\")\n", "else:\n", "    print(\"Planes already saved using the modular implementation.\")\n", "    print(f\"Planes data file: {planes_file}\")\n", "    print(f\"Individual plane files saved in: {ransac_output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Conc<PERSON>\n", "\n", "In this notebook, we've implemented and used the RANSAC algorithm for detecting planes in point cloud data. We've demonstrated how to:\n", "\n", "1. Load and preprocess an aligned point cloud\n", "2. Use our modular RANSAC implementation from `src.plane_detection.ransac`\n", "3. Detect multiple planes in the point cloud\n", "4. Visualize the detected planes using Open3D and Matplotlib\n", "5. Save the detected planes with preserved original point indices\n", "\n", "Key improvements in our implementation:\n", "\n", "- **Modular Implementation**: We've created a reusable RANSAC module in `src.plane_detection.ransac`\n", "- **Original Point Indices Preservation**: We now preserve the original point indices throughout the pipeline\n", "- **Metadata Preservation**: We include coordinate system information and other metadata in the output files\n", "- **Improved File Organization**: We save all outputs in a structured directory hierarchy\n", "\n", "This implementation can be used as a foundation for more advanced plane detection and analysis tasks, such as:\n", "\n", "- Detecting solar panels in point cloud data\n", "- Analyzing the orientation and tilt of detected planes\n", "- Clustering points into distinct planes representing individual solar panels\n", "- Extracting geometric features for anomaly detection\n", "\n", "Next steps include:\n", "\n", "1. Using the DBSCAN implementation in `src.plane_detection.dbscan` to segment planes into individual panels\n", "2. Running the complete pipeline using `src.plane_detection.run_pipeline`\n", "3. Developing geometric feature extractors for solar panel analysis\n", "4. Integrating with the anomaly detection pipeline"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}