{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Geometric Analysis of Solar Panels\n",
    "\n",
    "This notebook implements geometric analysis of solar panels detected in point cloud data. It demonstrates how to:\n",
    "\n",
    "1. Load segmented panels from DBSCAN clustering\n",
    "2. Calculate tilt and azimuth angles for each panel\n",
    "3. Measure panel dimensions and area\n",
    "4. Group panels into rows and columns\n",
    "5. Calculate distances between adjacent panels\n",
    "6. Visualize the geometric properties\n",
    "7. Export the results for further analysis\n",
    "\n",
    "**Author:** Preetam Balijepalli  \n",
    "**Date:** June 2024"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Setup and Installation\n",
    "\n",
    "First, let's install the necessary dependencies and import required libraries."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Install required packages\n",
    "# Uncomment and run this cell if you need to install the packages\n",
    "\n",
    "# !pip install numpy matplotlib open3d scikit-learn pandas scipy"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "from mpl_toolkits.mplot3d import Axes3D\n",
    "import pandas as pd\n",
    "import os\n",
    "import logging\n",
    "from scipy.cluster.hierarchy import fcluster, linkage\n",
    "from scipy.spatial import ConvexHull, distance\n",
    "from sklearn.decomposition import PCA\n",
    "import math\n",
    "\n",
    "# Configure logging\n",
    "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n",
    "logger = logging.getLogger(__name__)\n",
    "\n",
    "# Try to import Open3D for point cloud visualization\n",
    "try:\n",
    "    import open3d as o3d\n",
    "    O3D_SUPPORT = True\n",
    "    logger.info(\"Open3D support is available.\")\n",
    "except ImportError:\n",
    "    logger.warning(\"open3d not installed. Some visualizations may not be available.\")\n",
    "    O3D_SUPPORT = False\n",
    "\n",
    "# Check if running in Google Colab\n",
    "try:\n",
    "    from google.colab import drive\n",
    "    drive.mount('/content/gdrive')\n",
    "    IN_COLAB = True\n",
    "    logger.info(\"Google Drive mounted successfully.\")\n",
    "except ImportError:\n",
    "    IN_COLAB = False\n",
    "    logger.info(\"Not running in Google Colab. Using local file system.\")\n",
    "\n",
    "# Print version information\n",
    "print(\"NumPy version:\", np.__version__)\n",
    "print(\"Pandas version:\", pd.__version__)\n",
    "print(\"SciPy version:\", scipy.__version__)\n",
    "if O3D_SUPPORT:\n",
    "    print(\"Open3D version:\", o3d.__version__)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Loading Segmented Panels\n",
    "\n",
    "Now let's load the panels segmented by the DBSCAN clustering algorithm. We'll load both the panel information (saved as a NumPy file) and the individual panel point clouds (saved as PLY files)."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Define paths to your files\n",
    "if IN_COLAB:\n",
    "    # Google Drive paths\n",
    "    base_path = '/content/gdrive/MyDrive/pc-experiment'\n",
    "else:\n",
    "    # Local paths - adjust as needed\n",
    "    base_path = '../data/pc-experiment'\n",
    "\n",
    "# Create output directory if it doesn't exist\n",
    "output_dir = os.path.join(base_path, 'output')\n",
    "os.makedirs(output_dir, exist_ok=True)\n",
    "\n",
    "# Path to segmented panels file\n",
    "panels_file = os.path.join(output_dir, 'segmented_panels.npy')\n",
    "\n",
    "# Check if the panels file exists\n",
    "if os.path.exists(panels_file):\n",
    "    print(f\"Found segmented panels file at {panels_file}\")\n",
    "    # Load the panels data\n",
    "    panels_data = np.load(panels_file, allow_pickle=True).item()\n",
    "    print(f\"Loaded data for {panels_data['num_clusters']} panels\")\n",
    "    \n",
    "    # Validate the input data format\n",
    "    print(\"\\n=== Validating Input Data ===\\n\")\n",
    "    \n",
    "    # Check for required keys\n",
    "    required_keys = ['num_clusters', 'clusters']\n",
    "    missing_keys = [key for key in required_keys if key not in panels_data]\n",
    "    if missing_keys:\n",
    "        print(f\"Warning: Missing required keys in panels data: {missing_keys}\")\n",
    "    \n",
    "    # Check for metadata\n",
    "    if 'metadata' in panels_data:\n",
    "        print(\"Metadata found in panels data\")\n",
    "        \n",
    "        # Check coordinate system information\n",
    "        if 'coordinate_system' in panels_data['metadata']:\n",
    "            coord_sys = panels_data['metadata']['coordinate_system']\n",
    "            print(f\"Coordinate system: {coord_sys.get('description', 'No description')}\")\n",
    "            print(f\"Units: {coord_sys.get('units', 'Not specified')}\")\n",
    "            \n",
    "            # Validate coordinate system assumptions\n",
    "            if coord_sys.get('z_axis') != 'Up':\n",
    "                print(f\"Warning: Z-axis is '{coord_sys.get('z_axis')}', but analysis assumes 'Up'\")\n",
    "            if coord_sys.get('y_axis') != 'North':\n",
    "                print(f\"Warning: Y-axis is '{coord_sys.get('y_axis')}', but azimuth calculation assumes 'North'\")\n",
    "        else:\n",
    "            print(\"Warning: No coordinate system information found in metadata\")\n",
    "    else:\n",
    "        print(\"Warning: No metadata found in panels data. Using default assumptions:\")\n",
    "        print(\"  - Coordinate system: Right-handed with Z-axis pointing up\")\n",
    "        print(\"  - Units: meters\")\n",
    "        print(\"  - Y-axis: North\")\n",
    "        print(\"  - X-axis: East\")\n",
    "        \n",
    "        # Add default metadata\n",
    "        panels_data['metadata'] = {\n",
    "            'coordinate_system': {\n",
    "                'description': 'Right-handed coordinate system with Z-axis pointing up',\n",
    "                'x_axis': 'East',\n",
    "                'y_axis': 'North',\n",
    "                'z_axis': 'Up',\n",
    "                'units': 'meters'\n",
    "            }\n",
    "        }\n",
    "    \n",
    "    # Check panel data\n",
    "    if panels_data['num_clusters'] > 0:\n",
    "        # Check for required panel properties\n",
    "        required_panel_props = ['centroid', 'normal', 'num_points']\n",
    "        sample_panel = panels_data['clusters'][0]\n",
    "        missing_props = [prop for prop in required_panel_props if prop not in sample_panel]\n",
    "        if missing_props:\n",
    "            print(f\"Warning: Missing required properties in panel data: {missing_props}\")\n",
    "        \n",
    "        # Check for quality metrics\n",
    "        if 'quality_metrics' in sample_panel:\n",
    "            print(\"Quality metrics found in panel data\")\n",
    "            \n",
    "            # Filter out low-quality panels\n",
    "            quality_threshold = 0.5  # Minimum confidence score\n",
    "            original_count = len(panels_data['clusters'])\n",
    "            panels_data['clusters'] = [\n",
    "                panel for panel in panels_data['clusters'] \n",
    "                if panel.get('quality_metrics', {}).get('confidence_score', 0) >= quality_threshold\n",
    "            ]\n",
    "            panels_data['num_clusters'] = len(panels_data['clusters'])\n",
    "            \n",
    "            if panels_data['num_clusters'] < original_count:\n",
    "                print(f\"Filtered out {original_count - panels_data['num_clusters']} low-quality panels\")\n",
    "                print(f\"Remaining panels: {panels_data['num_clusters']}\")\n",
    "        else:\n",
    "            print(\"Warning: No quality metrics found in panel data\")\n",
    "    \n",
    "else:\n",
    "    print(f\"Segmented panels file not found at {panels_file}\")\n",
    "    print(\"You may need to run the DBSCAN notebook first or adjust the path.\")\n",
    "    # Create dummy data for testing\n",
    "    panels_data = {\n",
    "        'num_clusters': 0, \n",
    "        'clusters': [],\n",
    "        'metadata': {\n",
    "            'coordinate_system': {\n",
    "                'description': 'Right-handed coordinate system with Z-axis pointing up',\n",
    "                'x_axis': 'East',\n",
    "                'y_axis': 'North',\n",
    "                'z_axis': 'Up',\n",
    "                'units': 'meters'\n",
    "            }\n",
    "        }\n",
    "    }"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def load_panel_points(output_dir, num_panels):\n",
    "    \"\"\"\n",
    "    Load point clouds for each segmented panel.\n",
    "    \n",
    "    Parameters:\n",
    "    -----------\n",
    "    output_dir : str\n",
    "        Directory containing the panel point cloud files\n",
    "    num_panels : int\n",
    "        Number of panels to load\n",
    "        \n",
    "    Returns:\n",
    "    --------\n",
    "    panel_points : list of numpy.ndarray\n",
    "        List of point clouds for each panel\n",
    "    \"\"\"\n",
    "    if not O3D_SUPPORT:\n",
    "        logger.warning(\"Open3D not available, cannot load panel point clouds\")\n",
    "        return []\n",
    "        \n",
    "    panel_points = []\n",
    "    \n",
    "    for i in range(num_panels):\n",
    "        panel_file = os.path.join(output_dir, f'panel_{i+1}.ply')\n",
    "        if os.path.exists(panel_file):\n",
    "            # Load the point cloud\n",
    "            pcd = o3d.io.read_point_cloud(panel_file)\n",
    "            points = np.asarray(pcd.points)\n",
    "            panel_points.append(points)\n",
    "            logger.info(f\"Loaded panel {i+1} with {points.shape[0]} points\")\n",
    "        else:\n",
    "            logger.warning(f\"Panel file not found: {panel_file}\")\n",
    "    \n",
    "    return panel_points\n",
    "\n",
    "# Load the panel point clouds\n",
    "panel_points = load_panel_points(output_dir, panels_data['num_clusters'])\n",
    "\n",
    "# If no panels were loaded, create synthetic data for testing\n",
    "if len(panel_points) == 0:\n",
    "    print(\"Creating synthetic panel data for testing...\")\n",
    "    # Create 9 synthetic panels in a 3x3 grid\n",
    "    panel_points = []\n",
    "    panel_data = []\n",
    "    \n",
    "    for row in range(3):\n",
    "        for col in range(3):\n",
    "            # Create a grid of points on a plane with some tilt\n",
    "            x = np.linspace(-1, 1, 20) + col * 3\n",
    "            y = np.linspace(-1, 1, 20) + row * 3\n",
    "            xx, yy = np.meshgrid(x, y)\n",
    "            \n",
    "            # Add tilt (different for each panel)\n",
    "            tilt_x = 0.2 * (row - 1)  # Tilt along x-axis\n",
    "            tilt_y = 0.1 * (col - 1)  # Tilt along y-axis\n",
    "            zz = tilt_x * xx + tilt_y * yy + 0.1 * np.random.randn(xx.shape[0], xx.shape[1])\n",
    "            \n",
    "            # Combine into points\n",
    "            points = np.column_stack((xx.flatten(), yy.flatten(), zz.flatten()))\n",
    "            \n",
    "            # Calculate normal vector (perpendicular to the plane)\n",
    "            normal = np.array([-tilt_x, -tilt_y, 1])\n",
    "            normal = normal / np.linalg.norm(normal)\n",
    "            \n",
    "            # Add to lists\n",
    "            panel_points.append(points)\n",
    "            \n",
    "            # Create panel data\n",
    "            panel_info = {\n",
    "                'plane_id': row,\n",
    "                'cluster_id': col,\n",
    "                'num_points': points.shape[0],\n",
    "                'centroid': np.mean(points, axis=0).tolist(),\n",
    "                'normal': normal.tolist()\n",
    "            }\n",
    "            panel_data.append(panel_info)\n",
    "            \n",
    "            print(f\"Created synthetic panel at row {row}, column {col} with {points.shape[0]} points\")\n",
    "    \n",
    "    # Update panels_data\n",
    "    panels_data['num_clusters'] = len(panel_points)\n",
    "    panels_data['clusters'] = panel_data\n",
    "\n",
    "print(f\"\\nTotal panels loaded: {len(panel_points)}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Geometric Analysis Implementation\n",
    "\n",
    "Now let's implement the geometric analysis functions to calculate various properties of the solar panels."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class PanelGeometryAnalyzer:\n",
    "    def __init__(self, panel_points, panel_data, metadata=None):\n",
    "        \"\"\"\n",
    "        Initialize the analyzer with panel points and data.\n",
    "        \n",
    "        Parameters:\n",
    "        -----------\n",
    "        panel_points : list of numpy.ndarray\n",
    "            List of point clouds for each panel\n",
    "        panel_data : list of dict\n",
    "            List of panel metadata\n",
    "        metadata : dict, optional\n",
    "            Additional metadata about the panels, including coordinate system information\n",
    "        \"\"\"\n",
    "        self.panel_points = panel_points\n",
    "        self.panel_data = panel_data\n",
    "        self.panel_df = None\n",
    "        self.metadata = metadata or {}\n",
    "        \n",
    "        # Extract coordinate system information\n",
    "        self.coord_sys = self.metadata.get('coordinate_system', {})\n",
    "        \n",
    "        # Set up coordinate system vectors\n",
    "        self.vertical_axis = np.array([0, 0, 1])  # Default: Z-axis is up\n",
    "        self.north_axis = np.array([0, 1, 0])     # Default: Y-axis is north\n",
    "        self.east_axis = np.array([1, 0, 0])      # Default: X-axis is east\n",
    "        \n",
    "        # Override defaults if coordinate system is specified\n",
    "        if self.coord_sys:\n",
    "            logger.info(f\"Using coordinate system: {self.coord_sys.get('description', 'Not specified')}\")\n",
    "            # Custom coordinate system handling could be added here if needed\n",
    "        else:\n",
    "            logger.warning(\"No coordinate system information provided. Using default assumptions.\")\n",
    "    \n",
    "    def calculate_panel_dimensions(self, points, normal):\n",
    "        \"\"\"\n",
    "        Calculate the dimensions of a panel using PCA.\n",
    "        \n",
    "        Parameters:\n",
    "        -----------\n",
    "        points : numpy.ndarray\n",
    "            Point cloud for the panel\n",
    "        normal : numpy.ndarray\n",
    "            Normal vector of the panel\n",
    "            \n",
    "        Returns:\n",
    "        --------\n",
    "        length : float\n",
    "            Length of the panel (longest dimension)\n",
    "        width : float\n",
    "            Width of the panel (second longest dimension)\n",
    "        area : float\n",
    "            Area of the panel\n",
    "        \"\"\"\n",
    "        # Center the points\n",
    "        centroid = np.mean(points, axis=0)\n",
    "        centered_points = points - centroid\n",
    "        \n",
    "        # Project points onto the plane\n",
    "        # Create a basis for the plane\n",
    "        normal = np.array(normal)\n",
    "        normal = normal / np.linalg.norm(normal)\n",
    "        \n",
    "        # Find two vectors perpendicular to the normal\n",
    "        if abs(normal[0]) > abs(normal[1]):\n",
    "            v1 = np.array([-normal[2], 0, normal[0]])\n",
    "        else:\n",
    "            v1 = np.array([0, -normal[2], normal[1]])\n",
    "        v1 = v1 / np.linalg.norm(v1)\n",
    "        v2 = np.cross(normal, v1)\n",
    "        \n",
    "        # Project points onto the plane\n",
    "        projected_points = np.zeros((points.shape[0], 2))\n",
    "        for i in range(points.shape[0]):\n",
    "            p = centered_points[i]\n",
    "            projected_points[i, 0] = np.dot(p, v1)\n",
    "            projected_points[i, 1] = np.dot(p, v2)\n",
    "        \n",
    "        # Calculate the 2D convex hull\n",
    "        try:\n",
    "            hull = ConvexHull(projected_points)\n",
    "            hull_points = projected_points[hull.vertices]\n",
    "            \n",
    "            # Calculate the area of the convex hull\n",
    "            area = hull.volume  # In 2D, volume is area\n",
    "            \n",
    "            # Find the minimum area bounding rectangle\n",
    "            # This is a simplified approach - for a more accurate method, use a minimum area bounding rectangle algorithm\n",
    "            pca = PCA(n_components=2)\n",
    "            pca.fit(hull_points)\n",
    "            \n",
    "            # Transform hull points to PCA space\n",
    "            hull_points_pca = pca.transform(hull_points)\n",
    "            \n",
    "            # Get the min and max along each principal component\n",
    "            min_x, max_x = np.min(hull_points_pca[:, 0]), np.max(hull_points_pca[:, 0])\n",
    "            min_y, max_y = np.min(hull_points_pca[:, 1]), np.max(hull_points_pca[:, 1])\n",
    "            \n",
    "            # Calculate length and width\n",
    "            length = max_x - min_x\n",
    "            width = max_y - min_y\n",
    "            \n",
    "            # Ensure length is the longer dimension\n",
    "            if width > length:\n",
    "                length, width = width, length\n",
    "                \n",
    "            return length, width, area\n",
    "        except Exception as e:\n",
    "            logger.error(f\"Error calculating panel dimensions: {e}\")\n",
    "            return 0, 0, 0\n",
    "    \n",
    "    def calculate_tilt_azimuth(self, normal):\n",
    "        \"\"\"\n",
    "        Calculate tilt and azimuth angles from a normal vector.\n",
    "        \n",
    "        Parameters:\n",
    "        -----------\n",
    "        normal : numpy.ndarray\n",
    "            Normal vector of the panel\n",
    "            \n",
    "        Returns:\n",
    "        --------\n",
    "        tilt : float\n",
    "            Tilt angle in degrees (0 = horizontal, 90 = vertical)\n",
    "        azimuth : float\n",
    "            Azimuth angle in degrees (0 = North, 90 = East, 180 = South, 270 = West)\n",
    "        \"\"\"\n",
    "        normal = np.array(normal)\n",
    "        normal = normal / np.linalg.norm(normal)\n",
    "        \n",
    "        # Vertical reference vector (z-axis)\n",
    "        vertical = np.array([0, 0, 1])\n",
    "        \n",
    "        # Calculate tilt angle (angle between normal and vertical)\n",
    "        cos_tilt = np.dot(normal, vertical)\n",
    "        tilt = np.arccos(np.clip(cos_tilt, -1.0, 1.0)) * 180 / np.pi\n",
    "        \n",
    "        # For a horizontal panel, tilt = 0\n",
    "        # For a vertical panel, tilt = 90\n",
    "        tilt = 90 - tilt\n",
    "        \n",
    "        # Calculate azimuth (compass direction the panel faces)\n",
    "        # Project normal vector onto the horizontal plane\n",
    "        normal_xy = np.array([normal[0], normal[1], 0])\n",
    "        norm_xy = np.linalg.norm(normal_xy)\n",
    "        \n",
    "        if norm_xy < 1e-6:  # Normal is nearly vertical\n",
    "            azimuth = 0  # Default to North\n",
    "        else:\n",
    "            normal_xy = normal_xy / norm_xy\n",
    "            \n",
    "            # North reference vector (y-axis in our coordinate system)\n",
    "            north = np.array([0, 1, 0])\n",
    "            \n",
    "            # Calculate angle between normal_xy and north\n",
    "            cos_azimuth = np.dot(normal_xy, north)\n",
    "            azimuth = np.arccos(np.clip(cos_azimuth, -1.0, 1.0)) * 180 / np.pi\n",
    "            \n",
    "            # Adjust for the quadrant\n",
    "            if normal_xy[0] < 0:  # If x component is negative\n",
    "                azimuth = 360 - azimuth\n",
    "        \n",
    "        return tilt, azimuth

    def cluster_panels_into_rows(self, min_distance=0.5):
        \"\"\"
        Cluster panels into rows based on their centroids.

        Parameters:
        -----------
        min_distance : float
            Minimum distance between rows

        Returns:
        --------
        row_labels : numpy.ndarray
            Row label for each panel
        \"\"\"
        # Extract centroids
        centroids = np.array([np.array(panel['centroid']) for panel in self.panel_data])

        # Use hierarchical clustering on the y-coordinates (assuming y is the row direction)
        Z = linkage(centroids[:, 1].reshape(-1, 1), method='ward')

        # Determine the number of clusters based on the distance threshold
        max_dist = min_distance
        row_labels = fcluster(Z, t=max_dist, criterion='distance') - 1

        return row_labels

    def order_panels_in_rows(self, row_labels):
        \"\"\"
        Order panels within each row based on their x-coordinates.

        Parameters:
        -----------
        row_labels : numpy.ndarray
            Row label for each panel

        Returns:
        --------
        column_indices : numpy.ndarray
            Column index for each panel within its row
        \"\"\"
        # Extract centroids
        centroids = np.array([np.array(panel['centroid']) for panel in self.panel_data])

        # Initialize column indices
        column_indices = np.zeros(len(self.panel_data), dtype=int)

        # For each row, sort panels by x-coordinate
        for row in np.unique(row_labels):
            row_mask = (row_labels == row)
            row_centroids = centroids[row_mask]

            # Sort by x-coordinate
            x_order = np.argsort(row_centroids[:, 0])

            # Assign column indices
            column_indices[row_mask] = np.arange(len(x_order))[x_order]

        return column_indices

    def calculate_panel_spacing(self, row_labels, column_indices):
        \"\"\"
        Calculate spacing between adjacent panels.

        Parameters:
        -----------
        row_labels : numpy.ndarray
            Row label for each panel
        column_indices : numpy.ndarray
            Column index for each panel within its row

        Returns:
        --------
        row_spacing : numpy.ndarray
            Spacing between rows
        column_spacing : numpy.ndarray
            Spacing between columns within each row
        \"\"\"
        # Extract centroids
        centroids = np.array([np.array(panel['centroid']) for panel in self.panel_data])

        # Initialize spacing arrays
        n_panels = len(self.panel_data)
        row_spacing = np.full(n_panels, np.nan)
        column_spacing = np.full(n_panels, np.nan)

        # Calculate row spacing
        unique_rows = np.unique(row_labels)
        row_centroids = np.array([np.mean(centroids[row_labels == row], axis=0) for row in unique_rows])

        # Sort rows by y-coordinate
        row_order = np.argsort(row_centroids[:, 1])
        sorted_row_centroids = row_centroids[row_order]

        # Calculate spacing between adjacent rows
        for i in range(len(unique_rows) - 1):
            current_row = unique_rows[row_order[i]]
            next_row = unique_rows[row_order[i + 1]]

            # Calculate distance between row centroids
            dist = np.linalg.norm(sorted_row_centroids[i + 1] - sorted_row_centroids[i])

            # Assign to all panels in the current row
            row_spacing[row_labels == current_row] = dist

        # Calculate column spacing within each row
        for row in unique_rows:
            row_mask = (row_labels == row)
            row_panels = np.where(row_mask)[0]

            # Sort by column index
            col_order = np.argsort(column_indices[row_mask])
            sorted_row_panels = row_panels[col_order]

            # Calculate spacing between adjacent panels in the row
            for i in range(len(sorted_row_panels) - 1):
                current_panel = sorted_row_panels[i]
                next_panel = sorted_row_panels[i + 1]

                # Calculate distance between panel centroids
                dist = np.linalg.norm(centroids[next_panel] - centroids[current_panel])

                # Assign to the current panel
                column_spacing[current_panel] = dist

        return row_spacing, column_spacing

    def analyze_panels(self):
        \"\"\"
        Analyze all panels and create a DataFrame with the results.

        Returns:
        --------
        panel_df : pandas.DataFrame
            DataFrame containing panel properties
        \"\"\"
        # Initialize lists to store results
        panel_ids = []
        plane_ids = []
        cluster_ids = []
        centroids = []
        normals = []
        lengths = []
        widths = []
        areas = []
        tilts = []
        azimuths = []

        # Process each panel
        for i, (points, panel) in enumerate(zip(self.panel_points, self.panel_data)):
            panel_ids.append(i)
            plane_ids.append(panel.get('plane_id', -1))
            cluster_ids.append(panel.get('cluster_id', -1))

            # Get centroid and normal
            centroid = np.array(panel['centroid'])
            normal = np.array(panel['normal'])

            centroids.append(centroid)
            normals.append(normal)

            # Calculate dimensions
            length, width, area = self.calculate_panel_dimensions(points, normal)
            lengths.append(length)
            widths.append(width)
            areas.append(area)

            # Calculate tilt and azimuth
            tilt, azimuth = self.calculate_tilt_azimuth(normal)
            tilts.append(tilt)
            azimuths.append(azimuth)

        # Create DataFrame
        self.panel_df = pd.DataFrame({
            'Panel ID': panel_ids,
            'Plane ID': plane_ids,
            'Cluster ID': cluster_ids,
            'Centroid X': [c[0] for c in centroids],
            'Centroid Y': [c[1] for c in centroids],
            'Centroid Z': [c[2] for c in centroids],
            'Normal X': [n[0] for n in normals],
            'Normal Y': [n[1] for n in normals],
            'Normal Z': [n[2] for n in normals],
            'Length (m)': lengths,
            'Width (m)': widths,
            'Area (m²)': areas,
            'Tilt (°)': tilts,
            'Azimuth (°)': azimuths
        })

        # Cluster panels into rows and columns
        row_labels = self.cluster_panels_into_rows()
        column_indices = self.order_panels_in_rows(row_labels)

        # Add row and column information to DataFrame
        self.panel_df['Row'] = row_labels
        self.panel_df['Column'] = column_indices

        # Calculate spacing
        row_spacing, column_spacing = self.calculate_panel_spacing(row_labels, column_indices)

        # Add spacing information to DataFrame
        self.panel_df['Row Spacing (m)'] = row_spacing
        self.panel_df['Column Spacing (m)'] = column_spacing

        return self.panel_df"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Apply Geometric Analysis\n",
    "\n",
    "Now let's apply the geometric analysis to the segmented panels."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create an instance of the PanelGeometryAnalyzer\n",
    "analyzer = PanelGeometryAnalyzer(panel_points, panels_data['clusters'], panels_data.get('metadata'))\n",
    "\n",
    "# Analyze panels\n",
    "panel_df = analyzer.analyze_panels()\n",
    "\n",
    "# Display the results\n",
    "print(\"\\n=== Panel Geometry Analysis Results ===\\n\")\n",
    "print(f\"Total panels analyzed: {len(panel_df)}\")\n",
    "print(f\"Number of rows: {panel_df['Row'].nunique()}\")\n",
    "print(f\"Number of columns: {panel_df['Column'].nunique()}\")\n",
    "\n",
    "# Display the DataFrame\n",
    "pd.set_option('display.max_columns', None)\n",
    "pd.set_option('display.width', 1000)\n",
    "display(panel_df)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Visualize Geometric Properties\n",
    "\n",
    "Now let's visualize the geometric properties of the panels."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def visualize_panel_properties(panel_df, property_name, cmap='viridis', title=None):\n",
    "    \"\"\"\n",
    "    Visualize a panel property in 3D space.\n",
    "    \n",
    "    Parameters:\n",
    "    -----------\n",
    "    panel_df : pandas.DataFrame\n",
    "        DataFrame containing panel properties\n",
    "    property_name : str\n",
    "        Name of the property to visualize\n",
    "    cmap : str\n",
    "        Colormap to use\n",
    "    title : str\n",
    "        Plot title\n",
    "    \"\"\"\n",
    "    # Create a new figure\n",
    "    fig = plt.figure(figsize=(12, 10))\n",
    "    ax = fig.add_subplot(111, projection='3d')\n",
    "    \n",
    "    # Extract centroids and property values\n",
    "    x = panel_df['Centroid X'].values\n",
    "    y = panel_df['Centroid Y'].values\n",
    "    z = panel_df['Centroid Z'].values\n",
    "    values = panel_df[property_name].values\n",
    "    \n",
    "    # Create a scatter plot with color based on the property value\n",
    "    scatter = ax.scatter(x, y, z, c=values, cmap=cmap, s=100, alpha=0.8)\n",
    "    \n",
    "    # Add a colorbar\n",
    "    cbar = plt.colorbar(scatter, ax=ax, pad=0.1)\n",
    "    cbar.set_label(property_name)\n",
    "    \n",
    "    # Set labels and title\n",
    "    ax.set_xlabel('X')\n",
    "    ax.set_ylabel('Y')\n",
    "    ax.set_zlabel('Z')\n",
    "    \n",
    "    if title is None:\n",
    "        title = f'Panel {property_name} Visualization'\n",
    "    ax.set_title(title)\n",
    "    \n",
    "    # Add panel IDs as text labels\n",
    "    for i, (xi, yi, zi) in enumerate(zip(x, y, z)):\n",
    "        ax.text(xi, yi, zi, str(i), fontsize=8)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "\n",
    "def visualize_panel_normals(panel_df, scale=1.0):\n",
    "    \"\"\"\n",
    "    Visualize panel normal vectors in 3D space.\n",
    "    \n",
    "    Parameters:\n",
    "    -----------\n",
    "    panel_df : pandas.DataFrame\n",
    "        DataFrame containing panel properties\n",
    "    scale : float\n",
    "        Scale factor for normal vectors\n",
    "    \"\"\"\n",
    "    # Create a new figure\n",
    "    fig = plt.figure(figsize=(12, 10))\n",
    "    ax = fig.add_subplot(111, projection='3d')\n",
    "    \n",
    "    # Extract centroids and normals\n",
    "    x = panel_df['Centroid X'].values\n",
    "    y = panel_df['Centroid Y'].values\n",
    "    z = panel_df['Centroid Z'].values\n",
    "    nx = panel_df['Normal X'].values\n",
    "    ny = panel_df['Normal Y'].values\n",
    "    nz = panel_df['Normal Z'].values\n",
    "    \n",
    "    # Create a scatter plot for panel centroids\n",
    "    ax.scatter(x, y, z, c='blue', s=50, alpha=0.5)\n",
    "    \n",
    "    # Plot normal vectors as arrows\n",
    "    for i in range(len(x)):\n",
    "        ax.quiver(x[i], y[i], z[i], nx[i]*scale, ny[i]*scale, nz[i]*scale, color='red', alpha=0.8)\n",
    "    \n",
    "    # Set labels and title\n",
    "    ax.set_xlabel('X')\n",
    "    ax.set_ylabel('Y')\n",
    "    ax.set_zlabel('Z')\n",
    "    ax.set_title('Panel Normal Vectors')\n",
    "    \n",
    "    # Add panel IDs as text labels\n",
    "    for i, (xi, yi, zi) in enumerate(zip(x, y, z)):\n",
    "        ax.text(xi, yi, zi, str(i), fontsize=8)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "\n",
    "def visualize_panel_rows_columns(panel_df):\n",
    "    \"\"\"\n",
    "    Visualize panel rows and columns in 3D space.\n",
    "    \n",
    "    Parameters:\n",
    "    -----------\n",
    "    panel_df : pandas.DataFrame\n",
    "        DataFrame containing panel properties\n",
    "    \"\"\"\n",
    "    # Create a new figure\n",
    "    fig = plt.figure(figsize=(12, 10))\n",
    "    ax = fig.add_subplot(111, projection='3d')\n",
    "    \n",
    "    # Extract centroids\n",
    "    x = panel_df['Centroid X'].values\n",
    "    y = panel_df['Centroid Y'].values\n",
    "    z = panel_df['Centroid Z'].values\n",
    "    rows = panel_df['Row'].values\n",
    "    cols = panel_df['Column'].values\n",
    "    \n",
    "    # Create a scatter plot with color based on row\n",
    "    scatter = ax.scatter(x, y, z, c=rows, cmap='tab10', s=100, alpha=0.8)\n",
    "    \n",
    "    # Add a colorbar\n",
    "    cbar = plt.colorbar(scatter, ax=ax, pad=0.1)\n",
    "    cbar.set_label('Row')\n",
    "    \n",
    "    # Set labels and title\n",
    "    ax.set_xlabel('X')\n",
    "    ax.set_ylabel('Y')\n",
    "    ax.set_zlabel('Z')\n",
    "    ax.set_title('Panel Rows and Columns')\n",
    "    \n",
    "    # Add panel IDs and column indices as text labels\n",
    "    for i, (xi, yi, zi, col) in enumerate(zip(x, y, z, cols)):\n",
    "        ax.text(xi, yi, zi, f\"{i} (C{col})\", fontsize=8)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Visualize tilt angles\n",
    "visualize_panel_properties(panel_df, 'Tilt (°)', cmap='coolwarm')\n",
    "\n",
    "# Visualize azimuth angles\n",
    "visualize_panel_properties(panel_df, 'Azimuth (°)', cmap='hsv')\n",
    "\n",
    "# Visualize panel areas\n",
    "visualize_panel_properties(panel_df, 'Area (m²)', cmap='viridis')\n",
    "\n",
    "# Visualize normal vectors\n",
    "visualize_panel_normals(panel_df, scale=1.0)\n",
    "\n",
    "# Visualize rows and columns\n",
    "visualize_panel_rows_columns(panel_df)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Statistical Analysis\n",
    "\n",
    "Now let's perform some statistical analysis on the panel properties."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Calculate summary statistics\n",
    "print(\"\\n=== Panel Property Statistics ===\\n\")\n",
    "stats_df = panel_df[['Length (m)', 'Width (m)', 'Area (m²)', 'Tilt (°)', 'Azimuth (°)', 'Row Spacing (m)', 'Column Spacing (m)']].describe()\n",
    "display(stats_df)\n",
    "\n",
    "# Calculate statistics by row\n",
    "print(\"\\n=== Statistics by Row ===\\n\")\n",
    "row_stats = panel_df.groupby('Row')[['Tilt (°)', 'Azimuth (°)', 'Area (m²)']].mean()\n",
    "display(row_stats)\n",
    "\n",
    "# Calculate statistics by column\n",
    "print(\"\\n=== Statistics by Column ===\\n\")\n",
    "col_stats = panel_df.groupby('Column')[['Tilt (°)', 'Azimuth (°)', 'Area (m²)']].mean()\n",
    "display(col_stats)\n",
    "\n",
    "# Plot histograms of key properties\n",
    "fig, axes = plt.subplots(2, 2, figsize=(14, 10))\n",
    "panel_df['Tilt (°)'].hist(ax=axes[0, 0], bins=20)\n",
    "axes[0, 0].set_title('Tilt Angle Distribution')\n",
    "axes[0, 0].set_xlabel('Tilt (°)')\n",
    "axes[0, 0].set_ylabel('Count')\n",
    "\n",
    "panel_df['Azimuth (°)'].hist(ax=axes[0, 1], bins=20)\n",
    "axes[0, 1].set_title('Azimuth Angle Distribution')\n",
    "axes[0, 1].set_xlabel('Azimuth (°)')\n",
    "axes[0, 1].set_ylabel('Count')\n",
    "\n",
    "panel_df['Area (m²)'].hist(ax=axes[1, 0], bins=20)\n",
    "axes[1, 0].set_title('Panel Area Distribution')\n",
    "axes[1, 0].set_xlabel('Area (m²)')\n",
    "axes[1, 0].set_ylabel('Count')\n",
    "\n",
    "# Plot tilt vs. azimuth\n",
    "axes[1, 1].scatter(panel_df['Azimuth (°)'], panel_df['Tilt (°)'], c=panel_df['Row'], cmap='tab10')\n",
    "axes[1, 1].set_title('Tilt vs. Azimuth')\n",
    "axes[1, 1].set_xlabel('Azimuth (°)')\n",
    "axes[1, 1].set_ylabel('Tilt (°)')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. Save Results\n",
    "\n",
    "Finally, let's save the results for further analysis."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Save the panel geometry data\n",
    "print(\"\\n=== Saving Panel Geometry Data ===\\n\")\n",
    "\n",
    "# Save as CSV\n",
    "csv_file = os.path.join(output_dir, 'panel_geometry.csv')\n",
    "panel_df.to_csv(csv_file, index=False)\n",
    "print(f\"Saved panel geometry data to {csv_file}\")\n",
    "\n",
    "# Save as NumPy file\n",
    "np_file = os.path.join(output_dir, 'panel_geometry.npy')\n",
    "np.save(np_file, panel_df.to_dict('records'))\n",
    "print(f\"Saved panel geometry data to {np_file}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. Conclusion\n",
    "\n",
    "In this notebook, we've implemented geometric analysis of solar panels detected in point cloud data. We've demonstrated how to:\n",
    "\n",
    "1. Load segmented panels from DBSCAN clustering\n",
    "2. Calculate tilt and azimuth angles for each panel\n",
    "3. Measure panel dimensions and area\n",
    "4. Group panels into rows and columns\n",
    "5. Calculate distances between adjacent panels\n",
    "6. Visualize the geometric properties\n",
    "7. Perform statistical analysis\n",
    "8. Export the results for further analysis\n",
    "\n",
    "This implementation can be used as a foundation for more advanced solar panel analysis tasks, such as:\n",
    "\n",
    "- Detecting anomalies in panel orientation\n",
    "- Optimizing panel layout for maximum solar exposure\n",
    "- Monitoring panel degradation over time\n",
    "- Generating reports for solar installation inspection\n",
    "\n",
    "Next steps could include:\n",
    "\n",
    "1. Implementing anomaly detection algorithms\n",
    "2. Calculating solar irradiance based on panel orientation\n",
    "3. Comparing measured geometry with design specifications\n",
    "4. Integrating with a visualization dashboard"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.10"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
