{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Point Cloud Alignment Benchmark Research\n", "\n", "This notebook compares different methods for 3D point cloud alignment, including:\n", "\n", "1. **Traditional ICP**: Iterative Closest Point algorithm\n", "2. **Deep Learning (GeoPoseNet)**: Neural network-based pose regression\n", "3. **Hybrid Approach**: GeoPoseNet + ICP refinement\n", "\n", "We'll evaluate each method on accuracy, speed, and robustness to different initial conditions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import sys\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "\n", "# Add the project root to the path\n", "sys.path.append(os.path.abspath('..'))\n", "\n", "from src.geoposenet.models.pose_regression_net import PoseRegressionNet\n", "from src.geoposenet.datasets.data_loader import read_obj_file, read_las_file, normalize_point_cloud\n", "from src.geoposenet.utils.icp_utils import refine_with_icp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Point Clouds\n", "\n", "First, let's load some example point clouds."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Replace with your own point cloud paths\n", "source_path = '../data/source_cloud.obj'\n", "target_path = '../data/target_cloud.las'\n", "\n", "# Load point clouds\n", "source_pc = read_obj_file(source_path)\n", "target_pc = read_las_file(target_path)\n", "\n", "# Normalize point clouds\n", "source_pc = normalize_point_cloud(source_pc)\n", "target_pc = normalize_point_cloud(target_pc)\n", "\n", "print(f\"Source point cloud shape: {source_pc.shape}\")\n", "print(f\"Target point cloud shape: {target_pc.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Initialize and Load Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Initialize model\n", "model = PoseRegressionNet(num_points=4096)\n", "\n", "# Load pre-trained weights if available\n", "model_path = '../saved_models/geoposenet_model.keras'\n", "if os.path.exists(model_path):\n", "    model = tf.keras.models.load_model(model_path)\n", "    print(\"Loaded pre-trained model\")\n", "else:\n", "    print(\"No pre-trained model found. Using untrained model.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON> and Align Point Clouds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Prepare input for the model\n", "source_flat = np.reshape(source_pc[:4096], (1, -1))\n", "target_flat = np.reshape(target_pc[:4096], (1, -1))\n", "inputs = tf.concat([source_flat, target_flat], axis=1)\n", "\n", "# Predict pose\n", "pred_pose = model(inputs, training=False).numpy()[0]\n", "print(f\"Predicted pose: {pred_pose}\")\n", "\n", "# Apply pose to source point cloud\n", "from src.geoposenet.utils.common import apply_pose\n", "aligned_source = apply_pose(source_pc[:4096], pred_pose)\n", "\n", "# Refine with ICP\n", "aligned_source_icp, transformation = refine_with_icp(aligned_source, target_pc[:4096])\n", "print(f\"ICP transformation matrix:\\n{transformation}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Visualize Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["from src.geoposenet.utils.common import plot_alignment\n", "\n", "# Visualize alignment\n", "plot_alignment(aligned_source_icp, target_pc[:4096], title=\"Point Cloud Alignment\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Benchmark Different Methods\n", "\n", "Now let's compare the performance of different alignment methods:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import time\n", "import pandas as pd\n", "from src.geoposenet.utils.icp_utils import kabsch_algorithm\n", "import open3d as o3d\n", "\n", "# Function to run pure ICP\n", "def run_pure_icp(source, target, max_iterations=100):\n", "    start_time = time.time()\n", "    \n", "    # Convert to Open3D format\n", "    source_o3d = o3d.geometry.PointCloud()\n", "    target_o3d = o3d.geometry.PointCloud()\n", "    source_o3d.points = o3d.utility.Vector3dVector(source)\n", "    target_o3d.points = o3d.utility.Vector3dVector(target)\n", "    \n", "    # Run ICP\n", "    reg = o3d.pipelines.registration.registration_icp(\n", "        source_o3d, target_o3d, \n", "        max_correspondence_distance=0.05,\n", "        init=np.eye(4),\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=max_iterations)\n", "    )\n", "    \n", "    # Apply transformation\n", "    aligned = np.asarray(source_o3d.transform(reg.transformation).points)\n", "    \n", "    # Calculate RMSE\n", "    from src.geoposenet.utils.common import rmse_alignment\n", "    rmse = rmse_alignment(aligned, target)\n", "    \n", "    end_time = time.time()\n", "    return {\n", "        'method': 'Pure ICP',\n", "        'rmse': rmse,\n", "        'time': end_time - start_time,\n", "        'aligned_points': aligned\n", "    }\n", "\n", "# Function to run GeoPoseNet\n", "def run_geoposenet(source, target, model):\n", "    start_time = time.time()\n", "    \n", "    # Prepare input\n", "    source_flat = np.reshape(source, (1, -1))\n", "    target_flat = np.reshape(target, (1, -1))\n", "    inputs = tf.concat([source_flat, target_flat], axis=1)\n", "    \n", "    # Predict pose\n", "    pred_pose = model(inputs, training=False).numpy()[0]\n", "    \n", "    # Apply pose\n", "    aligned = apply_pose(source, pred_pose)\n", "    \n", "    # Calculate RMSE\n", "    rmse = rmse_alignment(aligned, target)\n", "    \n", "    end_time = time.time()\n", "    return {\n", "        'method': 'GeoPoseNet',\n", "        'rmse': rmse,\n", "        'time': end_time - start_time,\n", "        'aligned_points': aligned\n", "    }\n", "\n", "# Function to run Hybrid approach (GeoPoseNet + ICP)\n", "def run_hybrid(source, target, model):\n", "    start_time = time.time()\n", "    \n", "    # First run GeoPoseNet\n", "    source_flat = np.reshape(source, (1, -1))\n", "    target_flat = np.reshape(target, (1, -1))\n", "    inputs = tf.concat([source_flat, target_flat], axis=1)\n", "    pred_pose = model(inputs, training=False).numpy()[0]\n", "    aligned_nn = apply_pose(source, pred_pose)\n", "    \n", "    # Then refine with ICP\n", "    aligned, _ = refine_with_icp(aligned_nn, target)\n", "    \n", "    # Calculate RMSE\n", "    rmse = rmse_alignment(aligned, target)\n", "    \n", "    end_time = time.time()\n", "    return {\n", "        'method': 'Hybrid (GeoPoseNet + ICP)',\n", "        'rmse': rmse,\n", "        'time': end_time - start_time,\n", "        'aligned_points': aligned\n", "    }\n", "\n", "# Run benchmarks\n", "results = []\n", "\n", "# Prepare data\n", "sample_size = 4096\n", "source_sample = source_pc[:sample_size]\n", "target_sample = target_pc[:sample_size]\n", "\n", "# Run each method\n", "results.append(run_pure_icp(source_sample, target_sample))\n", "results.append(run_geoposenet(source_sample, target_sample, model))\n", "results.append(run_hybrid(source_sample, target_sample, model))\n", "\n", "# Display results\n", "results_df = pd.DataFrame(results)[['method', 'rmse', 'time']]\n", "results_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Visualize Comparison\n", "\n", "Let's visualize the results of each method:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import matplotlib.pyplot as plt\n", "\n", "# Plot RMSE comparison\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(results_df['method'], results_df['rmse'])\n", "plt.title('Alignment Accuracy (RMSE)')\n", "plt.ylabel('RMSE (lower is better)')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Plot time comparison\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(results_df['method'], results_df['time'])\n", "plt.title('Computation Time')\n", "plt.ylabel('Time (seconds, lower is better)')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Robustness Analysis\n", "\n", "Let's test how each method performs with different initial misalignments:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import transforms3d.euler as t3d\n", "\n", "# Function to create misaligned point clouds with varying degrees of misalignment\n", "def create_misaligned(points, rotation_magnitude, translation_magnitude):\n", "    angles = np.random.uniform(-rotation_magnitude, rotation_magnitude, size=3)\n", "    R = t3d.euler2mat(angles[0], angles[1], angles[2])\n", "    t = np.random.uniform(-translation_magnitude, translation_magnitude, size=3)\n", "    return np.dot(points, R.T) + t\n", "\n", "# Test different misalignment levels\n", "misalignment_levels = [\n", "    {'rotation': 0.1, 'translation': 0.05, 'name': 'Small'},\n", "    {'rotation': 0.5, 'translation': 0.2, 'name': 'Medium'},\n", "    {'rotation': 1.0, 'translation': 0.5, 'name': 'Large'}\n", "]\n", "\n", "robustness_results = []\n", "\n", "for level in misalignment_levels:\n", "    # Create misaligned source\n", "    misaligned_source = create_misaligned(\n", "        target_sample, \n", "        level['rotation'], \n", "        level['translation']\n", "    )\n", "    \n", "    # Run each method\n", "    icp_result = run_pure_icp(misaligned_source, target_sample)\n", "    nn_result = run_geoposenet(misaligned_source, target_sample, model)\n", "    hybrid_result = run_hybrid(misaligned_source, target_sample, model)\n", "    \n", "    # Add to results\n", "    robustness_results.append({\n", "        'misalignment': level['name'],\n", "        'method': 'Pure ICP',\n", "        'rmse': icp_result['rmse']\n", "    })\n", "    robustness_results.append({\n", "        'misalignment': level['name'],\n", "        'method': 'GeoPoseNet',\n", "        'rmse': nn_result['rmse']\n", "    })\n", "    robustness_results.append({\n", "        'misalignment': level['name'],\n", "        'method': 'Hybrid',\n", "        'rmse': hybrid_result['rmse']\n", "    })\n", "\n", "# Display results\n", "robustness_df = pd.DataFrame(robustness_results)\n", "robustness_pivot = robustness_df.pivot(index='misalignment', columns='method', values='rmse')\n", "robustness_pivot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Plot robustness results\n", "robustness_pivot.plot(kind='bar', figsize=(12, 6))\n", "plt.title('Method Robustness to Different Misalignment Levels')\n", "plt.ylabel('RMSE (lower is better)')\n", "plt.xlabel('Misalignment Level')\n", "plt.xticks(rotation=0)\n", "plt.legend(title='Method')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Conclusion\n", "\n", "Based on our benchmark results:\n", "\n", "1. **Accuracy**: [To be filled after running]\n", "2. **Speed**: [To be filled after running]\n", "3. **Robustness**: [To be filled after running]\n", "\n", "The hybrid approach (GeoPoseNet + ICP) generally provides the best balance of accuracy and speed, especially for larger misalignments where pure ICP might get stuck in local minima."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}