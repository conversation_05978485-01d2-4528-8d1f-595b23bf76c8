import os
import yaml
import argparse
import tensorflow as tf
import numpy as np
from tqdm import tqdm

# Import from the src directory
from src.geoposenet.models.pose_regression_net import PoseRegressionNet
from src.geoposenet.datasets.data_loader import read_obj_file, read_las_file, normalize_point_cloud, create_real_dataset
from src.geoposenet.utils.common import apply_pose, rmse_alignment
from src.geoposenet.utils.logger import Logger
from src.geoposenet.utils.tensorboard_logger import TensorboardLogger

def main():
    # Load config
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, default='configs/config.yaml', help='Path to config.yaml')
    args = parser.parse_args()

    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)

    # Hyperparameters
    EPOCHS = config['train']['epochs']
    BATCH_SIZE = config['train']['batch_size']
    LEARNING_RATE = config['train']['learning_rate']
    NUM_POINTS = config['train']['num_points']

    # Data paths
    source_path = config['train']['dataset']['source_path']
    target_path = config['train']['dataset']['target_path']

    # Setup logging
    logger = Logger()
    tb_logger = TensorboardLogger(log_dir="logs/")

    # Create directories
    os.makedirs('saved_models', exist_ok=True)
    os.makedirs('logs', exist_ok=True)

    logger.info(f"Starting training with {EPOCHS} epochs, batch size {BATCH_SIZE}")
    logger.info(f"Loading data from {source_path} and {target_path}")

    # Load point clouds
    try:
        source_pc = read_obj_file(source_path)
        target_pc = read_las_file(target_path)

        source_pc = normalize_point_cloud(source_pc)
        target_pc = normalize_point_cloud(target_pc)

        logger.info(f"Loaded source point cloud with {source_pc.shape[0]} points")
        logger.info(f"Loaded target point cloud with {target_pc.shape[0]} points")
    except Exception as e:
        logger.error(f"Error loading point clouds: {str(e)}")
        return

    # Initialize model and optimizer
    model = PoseRegressionNet(num_points=NUM_POINTS)
    optimizer = tf.keras.optimizers.Adam(learning_rate=LEARNING_RATE)

    logger.info("Model initialized")

    # Training
    losses = []
    progress_bar = tqdm(range(EPOCHS), desc="Training")

    for epoch in progress_bar:
        source_batch, target_batch, gt_pose_batch = create_real_dataset(
            source_pc, target_pc, batch_size=BATCH_SIZE, num_points=NUM_POINTS
        )

        source_flat = np.reshape(source_batch, (BATCH_SIZE, -1))
        target_flat = np.reshape(target_batch, (BATCH_SIZE, -1))
        inputs = tf.concat([source_flat, target_flat], axis=1)

        with tf.GradientTape() as tape:
            pred_pose = model(inputs)
            trans_loss = tf.reduce_mean(tf.square(pred_pose[:, :3] - gt_pose_batch[:, :3]))
            rot_loss = tf.reduce_mean(tf.square(pred_pose[:, 3:] - gt_pose_batch[:, 3:]))
            loss = trans_loss + rot_loss

        gradients = tape.gradient(loss, model.trainable_variables)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))

        losses.append(loss.numpy())

        # Update progress bar
        progress_bar.set_postfix({"loss": f"{loss.numpy():.6f}"})

        # Log to TensorBoard
        tb_logger.log_scalar("loss", loss.numpy(), epoch)
        tb_logger.log_scalar("translation_loss", trans_loss.numpy(), epoch)
        tb_logger.log_scalar("rotation_loss", rot_loss.numpy(), epoch)

    # Save model
    model.save('saved_models/geoposenet_model.keras')
    logger.success("Model saved to saved_models/geoposenet_model.keras")

if __name__ == "__main__":
    main()
