import argparse
import numpy as np
import tensorflow as tf
import yaml
import os

# Import from the src directory
from src.geoposenet.models.pose_regression_net import PoseRegressionNet
from src.geoposenet.datasets.data_loader import read_obj_file, read_las_file, normalize_point_cloud, create_real_dataset
from src.geoposenet.utils.common import apply_pose, rmse_alignment, plot_alignment, save_ply
from src.geoposenet.utils.icp_utils import refine_with_icp
from src.geoposenet.utils.logger import Logger

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, default='configs/config.yaml', help='Path to config.yaml')
    parser.add_argument('--model_path', type=str, default='saved_models/geoposenet_model.keras', help='Path to saved model')
    parser.add_argument('--source_path', type=str, help='Source point cloud file (.obj)')
    parser.add_argument('--target_path', type=str, help='Target point cloud file (.las)')
    args = parser.parse_args()

    # Setup logger
    logger = Logger()

    # Load config
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)

    # Get evaluation parameters
    N_SAMPLES = config['evaluation']['n_samples']
    USE_ICP = config['evaluation']['icp_refinement']
    SAVE_CLOUDS = config['visualization']['save_aligned_clouds']
    PLOT_FIRST = config['visualization']['plot_first_sample']

    # Use command line args if provided, otherwise use config
    source_path = args.source_path or config['train']['dataset']['source_path']
    target_path = args.target_path or config['train']['dataset']['target_path']

    logger.info(f"Evaluating model: {args.model_path}")
    logger.info(f"Source point cloud: {source_path}")
    logger.info(f"Target point cloud: {target_path}")

    # Load model
    try:
        model = tf.keras.models.load_model(args.model_path)
        logger.success("Model loaded successfully")
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        return

    # Load point clouds
    try:
        source_pc = read_obj_file(source_path)
        target_pc = read_las_file(target_path)

        source_pc = normalize_point_cloud(source_pc)
        target_pc = normalize_point_cloud(target_pc)

        logger.info(f"Loaded source point cloud with {source_pc.shape[0]} points")
        logger.info(f"Loaded target point cloud with {target_pc.shape[0]} points")
    except Exception as e:
        logger.error(f"Error loading point clouds: {str(e)}")
        return

    # Create output directory
    os.makedirs('results', exist_ok=True)

    # Create dataset and evaluate
    BATCH_SIZE = 1
    NUM_POINTS = 4096
    alignment_rmses = []

    for i in range(N_SAMPLES):
        logger.info(f"Processing sample {i+1}/{N_SAMPLES}")

        src_batch, tgt_batch, _ = create_real_dataset(
            source_pc, target_pc, batch_size=BATCH_SIZE, num_points=NUM_POINTS
        )

        src_flat = np.reshape(src_batch, (BATCH_SIZE, -1))
        tgt_flat = np.reshape(tgt_batch, (BATCH_SIZE, -1))
        inputs = tf.concat([src_flat, tgt_flat], axis=1)

        # Predict pose
        pred_pose = model(inputs, training=False).numpy()[0]
        aligned_source = apply_pose(src_batch[0], pred_pose)

        # Calculate initial RMSE
        initial_rmse = rmse_alignment(aligned_source, tgt_batch[0])
        logger.info(f"Initial alignment RMSE: {initial_rmse:.6f}")

        # ICP refinement if enabled
        if USE_ICP:
            aligned_source_icp, transformation = refine_with_icp(aligned_source, tgt_batch[0])
            final_rmse = rmse_alignment(aligned_source_icp, tgt_batch[0])
            logger.info(f"After ICP refinement RMSE: {final_rmse:.6f}")
            logger.info(f"RMSE improvement: {initial_rmse - final_rmse:.6f}")
        else:
            aligned_source_icp = aligned_source
            final_rmse = initial_rmse

        alignment_rmses.append(final_rmse)

        # Save aligned clouds if enabled
        if SAVE_CLOUDS:
            save_ply(aligned_source_icp, f"results/aligned_source_{i+1}.ply", color=[1, 0, 0])
            save_ply(tgt_batch[0], f"results/target_{i+1}.ply", color=[0, 1, 0])
            logger.info(f"Saved point clouds to results/aligned_source_{i+1}.ply and results/target_{i+1}.ply")

        # Plot first sample if enabled
        if i == 0 and PLOT_FIRST:
            plot_alignment(aligned_source_icp, tgt_batch[0], title=f"Sample {i+1}")

    # Print summary
    logger.success(f"Evaluation complete. Average RMSE across {N_SAMPLES} samples: {np.mean(alignment_rmses):.6f}")

if __name__ == "__main__":
    main()
