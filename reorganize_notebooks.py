#!/usr/bin/env python3
"""
Notebook Reorganization Implementation Script
Safely reorganizes notebooks according to the new workflow-based structure.

Usage:
    python reorganize_notebooks.py [--dry-run] [--backup-dir BACKUP_DIR]

Options:
    --dry-run       Show what would be done without actually moving files
    --backup-dir    Specify custom backup directory name
"""

import os
import shutil
import argparse
from pathlib import Path
import json
from datetime import datetime

def create_backup(backup_dir=None):
    """Create backup of current notebook structure."""
    if backup_dir is None:
        backup_dir = Path(f"notebooks_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    else:
        backup_dir = Path(backup_dir)
    
    if Path("notebooks").exists():
        shutil.copytree("notebooks", backup_dir)
        print(f"✅ Backup created: {backup_dir}")
        return backup_dir
    else:
        print("❌ notebooks directory not found!")
        return None

def safe_move(src, dst, dry_run=False):
    """Safely move a file or directory with dry-run support."""
    src_path = Path(src)
    dst_path = Path(dst)
    
    if not src_path.exists():
        print(f"⚠️ Source does not exist: {src}")
        return False
    
    # Create destination directory if needed
    dst_path.parent.mkdir(parents=True, exist_ok=True)
    
    if dry_run:
        print(f"🔍 DRY RUN: Would move {src} → {dst}")
        return True
    else:
        try:
            shutil.move(str(src_path), str(dst_path))
            print(f"✅ Moved {src} → {dst}")
            return True
        except Exception as e:
            print(f"❌ Failed to move {src} → {dst}: {e}")
            return False

def safe_mkdir(path, dry_run=False):
    """Safely create directory with dry-run support."""
    if dry_run:
        print(f"🔍 DRY RUN: Would create directory {path}")
    else:
        Path(path).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {path}")

def safe_rmdir(path, dry_run=False):
    """Safely remove empty directory with dry-run support."""
    path_obj = Path(path)
    
    if not path_obj.exists():
        return
    
    if dry_run:
        print(f"🔍 DRY RUN: Would remove empty directory {path}")
    else:
        try:
            # Only remove if empty
            if not any(path_obj.iterdir()):
                os.rmdir(path)
                print(f"✅ Removed empty directory: {path}")
            else:
                print(f"⚠️ Directory not empty, keeping: {path}")
        except OSError as e:
            print(f"⚠️ Could not remove directory {path}: {e}")

def execute_reorganization(dry_run=False):
    """Execute the notebook reorganization."""
    
    print("🚀 Starting notebook reorganization...")
    
    # Phase 1: Move ground segmentation under preprocessing
    safe_move(
        "notebooks/ground_segmentation", 
        "notebooks/preprocessing/ground_segmentation",
        dry_run
    )
    
    # Phase 2: Move plane detection under preprocessing  
    safe_move(
        "notebooks/plane_detection", 
        "notebooks/preprocessing/plane_detection",
        dry_run
    )
    
    # Phase 3: Move labeling under preprocessing
    safe_move(
        "notebooks/labeling", 
        "notebooks/preprocessing/labeling",
        dry_run
    )
    
    # Phase 4: Distribute geometric analysis
    # Move panel geometry to data analysis
    safe_move(
        "notebooks/geometric_analysis/panel_geometry_analysis.ipynb",
        "notebooks/data_analysis/panel_geometry_analysis.ipynb",
        dry_run
    )
    
    # Move panel segmentation to preprocessing
    safe_move(
        "notebooks/geometric_analysis/panel_segmentation_unet.ipynb", 
        "notebooks/preprocessing/plane_detection/panel_segmentation_unet.ipynb",
        dry_run
    )
    
    # Remove empty geometric_analysis folder
    safe_rmdir("notebooks/geometric_analysis", dry_run)
    
    # Phase 5: Create additional subfolders
    safe_mkdir("notebooks/preprocessing/data_loading", dry_run)
    safe_mkdir("notebooks/preprocessing/filtering", dry_run)
    
    print("✅ Reorganization plan completed!")

def validate_structure():
    """Validate the new notebook structure."""
    print("\n🔍 Validating new structure...")
    
    expected_structure = {
        "notebooks/data_acquisition": "Raw data loading, conversion, and extraction",
        "notebooks/preprocessing": "Filtering, ground segmentation, normalization",
        "notebooks/preprocessing/ground_segmentation": "Ground/non-ground classification",
        "notebooks/preprocessing/plane_detection": "Plane detection algorithms",
        "notebooks/preprocessing/labeling": "Auto-labeling from CAD/IFC",
        "notebooks/preprocessing/cad": "CAD processing pipeline",
        "notebooks/preprocessing/ifc": "IFC processing pipeline",
        "notebooks/preprocessing/data_loading": "Data loading utilities",
        "notebooks/preprocessing/filtering": "Point cloud filtering",
        "notebooks/data_analysis": "Stats, comparison, visual checks",
        "notebooks/alignment": "CAD-to-point cloud, ICP, etc.",
        "notebooks/pile_detection": "DGCNN or geometric feature-based",
        "notebooks/trench_detection": "Similar to pile but for trench geometry",
        "notebooks/compliance_analysis": "Deviation, classification, compliance scoring",
        "notebooks/visualization": "Dashboard-style output or overlay visualizers"
    }
    
    for path, description in expected_structure.items():
        if Path(path).exists():
            print(f"✅ {path} - {description}")
        else:
            print(f"⚠️ {path} - {description} (missing)")
    
    print("\n📊 Structure validation complete!")

def main():
    parser = argparse.ArgumentParser(description="Reorganize notebooks according to workflow structure")
    parser.add_argument("--dry-run", action="store_true", 
                       help="Show what would be done without actually moving files")
    parser.add_argument("--backup-dir", type=str,
                       help="Specify custom backup directory name")
    parser.add_argument("--no-backup", action="store_true",
                       help="Skip creating backup (not recommended)")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only validate current structure without making changes")
    
    args = parser.parse_args()
    
    if args.validate_only:
        validate_structure()
        return
    
    print("📋 Notebook Reorganization Script")
    print("=" * 50)
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - No files will be moved")
    
    # Create backup unless explicitly disabled
    backup_dir = None
    if not args.no_backup and not args.dry_run:
        backup_dir = create_backup(args.backup_dir)
        if backup_dir is None:
            print("❌ Failed to create backup, aborting!")
            return
    
    try:
        # Execute reorganization
        execute_reorganization(args.dry_run)
        
        if not args.dry_run:
            print(f"\n✅ Reorganization completed successfully!")
            if backup_dir:
                print(f"📁 Backup available at: {backup_dir}")
            
            # Validate the new structure
            validate_structure()
        else:
            print("\n🔍 Dry run completed. Use without --dry-run to execute changes.")
        
    except Exception as e:
        print(f"\n❌ Error during reorganization: {e}")
        if backup_dir:
            print(f"🔄 Restore from backup if needed: {backup_dir}")

if __name__ == "__main__":
    main()
