{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📘 Notebook 1: Panel Segmentation (U-Net on Orthoimage/DSM)\n", "\n", "**Input:** Orthophoto and DSM/Heightmap (2.5D raster from point cloud)  \n", "**Output:** Segmentation map with panel boundaries  \n", "**Model:** U-Net / DeepLabV3+  \n", "**Extras:** Use combined RGB + heightmap as input channels  \n", "\n", "This notebook implements deep learning-based panel segmentation from drone-based photogrammetry point clouds converted to 2.5D raster format.\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** December 2024"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install tensorflow opencv-python rasterio scikit-image open3d matplotlib numpy scipy pandas\n", "!pip install segmentation-models albumentations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import cv2\n", "from pathlib import Path\n", "import rasterio\n", "from rasterio.transform import from_bounds\n", "from skimage import filters, morphology, measure\n", "from scipy import ndimage\n", "import open3d as o3d\n", "\n", "import tensorflow as tf\n", "from tensorflow.keras import layers, models, optimizers, callbacks\n", "from tensorflow.keras.utils import to_categorical\n", "import segmentation_models as sm\n", "import albumentations as A\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"GPU available: {tf.config.list_physical_devices('GPU')}\")\n", "\n", "# Set segmentation models backend\n", "sm.set_framework('tf.keras')\n", "sm.framework()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Point Cloud to 2.5D Raster Conversion\n", "\n", "Convert 3D point clouds to orthophoto and Digital Surface Model (DSM) for CNN processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def point_cloud_to_raster(points, colors=None, resolution=0.05, bounds=None, method='max'):\n", "    \"\"\"\n", "    Convert point cloud to raster format (orthophoto + DSM).\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray, optional\n", "        RGB colors (N, 3) in range [0, 255]\n", "    resolution : float\n", "        Pixel resolution in meters\n", "    bounds : tuple, optional\n", "        (min_x, min_y, max_x, max_y) bounds\n", "    method : str\n", "        Aggregation method ('max', 'mean', 'min')\n", "        \n", "    Returns:\n", "    --------\n", "    orthophoto : numpy.n<PERSON><PERSON>\n", "        RGB orthophoto (H, W, 3)\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model (H, W)\n", "    transform : rasterio.Affine\n", "        Geospatial transform\n", "    \"\"\"\n", "    if bounds is None:\n", "        min_x, min_y = points[:, :2].min(axis=0)\n", "        max_x, max_y = points[:, :2].max(axis=0)\n", "    else:\n", "        min_x, min_y, max_x, max_y = bounds\n", "    \n", "    # Calculate raster dimensions\n", "    width = int(np.ceil((max_x - min_x) / resolution))\n", "    height = int(np.ceil((max_y - min_y) / resolution))\n", "    \n", "    # Initialize output arrays\n", "    dsm = np.full((height, width), np.nan)\n", "    orthophoto = np.zeros((height, width, 3), dtype=np.uint8)\n", "    point_counts = np.zeros((height, width), dtype=int)\n", "    \n", "    # Convert points to pixel coordinates\n", "    pixel_x = ((points[:, 0] - min_x) / resolution).astype(int)\n", "    pixel_y = ((max_y - points[:, 1]) / resolution).astype(int)  # Flip Y axis\n", "    \n", "    # Clip to valid range\n", "    valid_mask = (pixel_x >= 0) & (pixel_x < width) & (pixel_y >= 0) & (pixel_y < height)\n", "    pixel_x = pixel_x[valid_mask]\n", "    pixel_y = pixel_y[valid_mask]\n", "    valid_points = points[valid_mask]\n", "    \n", "    if colors is not None:\n", "        valid_colors = colors[valid_mask]\n", "    \n", "    # Aggregate points per pixel\n", "    for i, (px, py) in enumerate(zip(pixel_x, pixel_y)):\n", "        z_val = valid_points[i, 2]\n", "        \n", "        if method == 'max':\n", "            if np.isnan(dsm[py, px]) or z_val > dsm[py, px]:\n", "                dsm[py, px] = z_val\n", "                if colors is not None:\n", "                    orthophoto[py, px] = valid_colors[i]\n", "        elif method == 'mean':\n", "            if np.isnan(dsm[py, px]):\n", "                dsm[py, px] = z_val\n", "                if colors is not None:\n", "                    orthophoto[py, px] = valid_colors[i]\n", "                point_counts[py, px] = 1\n", "            else:\n", "                # Running average\n", "                count = point_counts[py, px]\n", "                dsm[py, px] = (dsm[py, px] * count + z_val) / (count + 1)\n", "                if colors is not None:\n", "                    orthophoto[py, px] = (orthophoto[py, px] * count + valid_colors[i]) / (count + 1)\n", "                point_counts[py, px] += 1\n", "        elif method == 'min':\n", "            if np.isnan(dsm[py, px]) or z_val < dsm[py, px]:\n", "                dsm[py, px] = z_val\n", "                if colors is not None:\n", "                    orthophoto[py, px] = valid_colors[i]\n", "    \n", "    # Fill NaN values in DSM using interpolation\n", "    mask = ~np.isnan(dsm)\n", "    if mask.any():\n", "        from scipy.interpolate import griddata\n", "        y_grid, x_grid = np.mgrid[0:height, 0:width]\n", "        valid_coords = np.column_stack((x_grid[mask], y_grid[mask]))\n", "        valid_values = dsm[mask]\n", "        \n", "        all_coords = np.column_stack((x_grid.ravel(), y_grid.ravel()))\n", "        interpolated = griddata(valid_coords, valid_values, all_coords, method='linear')\n", "        dsm_filled = interpolated.reshape(height, width)\n", "        \n", "        # Fill remaining NaNs with nearest neighbor\n", "        nan_mask = np.isnan(dsm_filled)\n", "        if nan_mask.any():\n", "            nearest = griddata(valid_coords, valid_values, all_coords, method='nearest')\n", "            dsm_filled[nan_mask] = nearest.reshape(height, width)[nan_mask]\n", "        \n", "        dsm = dsm_filled\n", "    \n", "    # Create geospatial transform\n", "    transform = from_bounds(min_x, min_y, max_x, max_y, width, height)\n", "    \n", "    return orthophoto, dsm, transform"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Multi-Channel Input Preparation\n", "\n", "Combine RGB orthophoto with elevation data for enhanced segmentation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_multichannel_input(orthophoto, dsm, normalize=True):\n", "    \"\"\"\n", "    Prepare multi-channel input combining RGB and elevation data.\n", "    \n", "    Parameters:\n", "    -----------\n", "    orthophoto : numpy.n<PERSON><PERSON>\n", "        RGB orthophoto (H, W, 3)\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model (H, W)\n", "    normalize : bool\n", "        Whether to normalize the data\n", "        \n", "    Returns:\n", "    --------\n", "    multichannel_input : numpy.ndarray\n", "        Combined input (H, W, 4) - RGB + elevation\n", "    \"\"\"\n", "    height, width = dsm.shape\n", "    \n", "    # Ensure orthophoto has the same dimensions\n", "    if orthophoto.shape[:2] != (height, width):\n", "        orthophoto = cv2.resize(orthophoto, (width, height))\n", "    \n", "    # Normalize orthophoto to [0, 1]\n", "    if normalize:\n", "        orthophoto_norm = orthophoto.astype(np.float32) / 255.0\n", "    else:\n", "        orthophoto_norm = orthophoto.astype(np.float32)\n", "    \n", "    # Normalize DSM\n", "    if normalize:\n", "        dsm_norm = (dsm - dsm.min()) / (dsm.max() - dsm.min())\n", "    else:\n", "        dsm_norm = dsm\n", "    \n", "    # Combine channels\n", "    multichannel_input = np.concatenate([\n", "        orthophoto_norm,\n", "        dsm_norm[..., np.newaxis]\n", "    ], axis=-1)\n", "    \n", "    return multichannel_input.astype(np.float32)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Panel Detection Preprocessing\n", "\n", "Preprocess data specifically for panel detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_panel_features(dsm, orthophoto):\n", "    \"\"\"\n", "    Extract features specifically useful for panel detection.\n", "    \n", "    Parameters:\n", "    -----------\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model (H, W)\n", "    orthophoto : numpy.n<PERSON><PERSON>\n", "        RGB orthophoto (H, W, 3)\n", "        \n", "    Returns:\n", "    --------\n", "    features : dict\n", "        Dictionary containing various features\n", "    \"\"\"\n", "    # Calculate gradients and slopes\n", "    grad_y, grad_x = np.gradient(dsm)\n", "    slope = np.sqrt(grad_x**2 + grad_y**2)\n", "    \n", "    # Edge detection on orthophoto\n", "    gray = cv2.cvtColor(orthophoto, cv2.COLOR_RGB2GRAY)\n", "    edges = cv2.<PERSON><PERSON>(gray, 50, 150)\n", "    \n", "    # Texture analysis\n", "    # Local Binary Pattern (simplified)\n", "    def local_binary_pattern(image, radius=1):\n", "        rows, cols = image.shape\n", "        lbp = np.zeros_like(image)\n", "        \n", "        for i in range(radius, rows - radius):\n", "            for j in range(radius, cols - radius):\n", "                center = image[i, j]\n", "                binary_string = ''\n", "                \n", "                # 8-neighborhood\n", "                neighbors = [\n", "                    image[i-1, j-1], image[i-1, j], image[i-1, j+1],\n", "                    image[i, j+1], image[i+1, j+1], image[i+1, j],\n", "                    image[i+1, j-1], image[i, j-1]\n", "                ]\n", "                \n", "                for neighbor in neighbors:\n", "                    binary_string += '1' if neighbor >= center else '0'\n", "                \n", "                lbp[i, j] = int(binary_string, 2)\n", "        \n", "        return lbp\n", "    \n", "    texture = local_binary_pattern(gray)\n", "    \n", "    # Color analysis\n", "    # Convert to HSV for better color analysis\n", "    hsv = cv2.cvtColor(orthophoto, cv2.COLOR_RGB2HSV)\n", "    \n", "    # Calculate color variance\n", "    color_variance = np.var(orthophoto, axis=-1)\n", "    \n", "    # Flatness indicator (low slope + low texture variation)\n", "    flatness = 1.0 / (1.0 + slope + np.std(texture, axis=None))\n", "    \n", "    features = {\n", "        'slope': slope,\n", "        'edges': edges,\n", "        'texture': texture,\n", "        'hsv': hsv,\n", "        'color_variance': color_variance,\n", "        'flatness': flatness\n", "    }\n", "    \n", "    return features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. U-Net Model for Panel Segmentation\n", "\n", "Implement U-Net architecture for panel segmentation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_unet_model(input_shape=(256, 256, 4), num_classes=2, backbone='resnet34'):\n", "    \"\"\"\n", "    Create U-Net model for panel segmentation.\n", "    \n", "    Parameters:\n", "    -----------\n", "    input_shape : tuple\n", "        Input shape (H, W, C)\n", "    num_classes : int\n", "        Number of classes (background + panel)\n", "    backbone : str\n", "        Backbone architecture\n", "        \n", "    Returns:\n", "    --------\n", "    model : tf.keras.Model\n", "        Compiled U-Net model\n", "    \"\"\"\n", "    # Create model using segmentation_models\n", "    model = sm.Unet(\n", "        backbone_name=backbone,\n", "        input_shape=input_shape,\n", "        classes=num_classes,\n", "        activation='softmax',\n", "        encoder_weights=None,  # No pretrained weights for 4-channel input\n", "        encoder_freeze=False\n", "    )\n", "    \n", "    # Compile model\n", "    model.compile(\n", "        optimizer=optimizers.<PERSON>(learning_rate=1e-4),\n", "        loss=sm.losses.DiceLoss() + sm.losses.CategoricalFocalLoss(),\n", "        metrics=[\n", "            sm.metrics.IOUScore(threshold=0.5),\n", "            sm.metrics.FScore(threshold=0.5),\n", "            'accuracy'\n", "        ]\n", "    )\n", "    \n", "    return model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Augmentation\n", "\n", "Define data augmentation pipeline for training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_training_augmentation():\n", "    \"\"\"\n", "    Get training augmentation pipeline.\n", "    \"\"\"\n", "    train_transform = [\n", "        <PERSON><PERSON>(p=0.5),\n", "        <PERSON><PERSON>(p=0.5),\n", "        <PERSON><PERSON>(p=0.5),\n", "        <PERSON><PERSON>(p=0.5),\n", "        <PERSON><PERSON>Rota<PERSON>(\n", "            shift_limit=0.0625,\n", "            scale_limit=0.1,\n", "            rotate_limit=15,\n", "            p=0.5\n", "        ),\n", "        <PERSON><PERSON>([\n", "            <PERSON><PERSON>(p=1, alpha=120, sigma=120 * 0.05, alpha_affine=120 * 0.03),\n", "            <PERSON><PERSON>(p=1),\n", "            <PERSON><PERSON>ticalDistortion(distort_limit=1, shift_limit=0.5, p=1),\n", "        ], p=0.3),\n", "        <PERSON><PERSON>([\n", "            <PERSON><PERSON>(p=1),\n", "            <PERSON><PERSON>(p=1),\n", "            <PERSON><PERSON>(p=1),\n", "        ], p=0.2),\n", "        <PERSON><PERSON>([\n", "            <PERSON><PERSON>ontrast(p=1),\n", "            <PERSON><PERSON>V<PERSON>ue(p=1),\n", "        ], p=0.3),\n", "    ]\n", "    return <PERSON><PERSON>(train_transform)\n", "\n", "def get_validation_augmentation():\n", "    \"\"\"\n", "    Get validation augmentation pipeline (minimal).\n", "    \"\"\"\n", "    test_transform = [\n", "        # Add any necessary preprocessing here\n", "    ]\n", "    return <PERSON><PERSON>(test_transform)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}