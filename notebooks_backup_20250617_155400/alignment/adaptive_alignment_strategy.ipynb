{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adaptive Alignment Strategy\n", "\n", "This notebook implements a flexible alignment strategy that adapts to available reference data sources.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Point clouds + Reference data (IFC, CAD, or GPS)  \n", "**Output**: Aligned point clouds with coordinate transformation  \n", "**Strategy**: Adaptive based on available reference sources  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Alignment Strategies:\n", "1. **IFC-Based**: Use IFC pile positions as ground truth (highest accuracy)\n", "2. **CAD-Based**: Use CAD INSERT positions as reference (good accuracy)\n", "3. **GPS-Based**: Use GPS survey points as reference (moderate accuracy)\n", "4. **Point Cloud-to-Point Cloud**: Relative alignment between scans (lowest accuracy)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"RPCS\"  # Site name for processing\n", "project_type = \"USA\"  # Options: \"ENEL\", \"USA\"\n", "point_cloud_path = \"\"  # Path to point cloud file\n", "coordinate_system = \"EPSG:32614\"  # Source coordinate reference system\n", "target_crs = \"EPSG:4326\"  # Target CRS (WGS84 for GPS coordinates)\n", "\n", "# Reference data availability (auto-detected or manually set)\n", "has_ifc_reference = False  # IFC files available\n", "has_cad_reference = True   # CAD files available\n", "has_gps_reference = False  # GPS survey points available\n", "\n", "# Alignment parameters\n", "alignment_strategy = \"auto\"  # Options: \"auto\", \"ifc\", \"cad\", \"gps\", \"relative\"\n", "icp_max_iterations = 100    # Maximum ICP iterations\n", "icp_tolerance = 1e-6        # ICP convergence tolerance\n", "correspondence_threshold = 2.0  # Maximum distance for point correspondence (meters)\n", "\n", "# Quality thresholds\n", "min_reference_points = 3    # Minimum reference points for alignment\n", "max_alignment_error = 1.0   # Maximum acceptable alignment error (meters)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "import os\n", "from pathlib import Path\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import logging\n", "\n", "# Point cloud processing\n", "try:\n", "    import open3d as o3d\n", "    O3D_SUPPORT = True\n", "    print(f\"Open3D available: {o3d.__version__}\")\n", "except ImportError:\n", "    print(\"Open3D not available. Some features will be disabled.\")\n", "    O3D_SUPPORT = False\n", "\n", "# Coordinate transformation\n", "try:\n", "    from pyproj import Transformer\n", "    COORDINATE_TRANSFORM_SUPPORT = True\n", "    print(\"Coordinate transformation support available\")\n", "except ImportError:\n", "    print(\"Coordinate transformation not available. Install pyproj for CRS transformation.\")\n", "    COORDINATE_TRANSFORM_SUPPORT = False\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set up paths\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Create output directory structure for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_alignment_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"Adaptive Alignment Strategy - Ready!\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Current run output: {current_run_path}\")\n", "print(f\"Reference data: IFC={has_ifc_reference}, CAD={has_cad_reference}, GPS={has_gps_reference}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reference Data Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_available_references(project_type, site_name):\n", "    \"\"\"\n", "    Automatically detect available reference data sources.\n", "    \"\"\"\n", "    site_path = data_path / project_type / site_name\n", "    \n", "    references = {\n", "        'ifc': <PERSON><PERSON><PERSON>,\n", "        'cad': <PERSON><PERSON><PERSON>,\n", "        'gps': <PERSON><PERSON><PERSON>,\n", "        'metadata': False\n", "    }\n", "    \n", "    # Check for IFC files\n", "    ifc_files = list((site_path / 'raw').glob('*.ifc'))\n", "    if ifc_files:\n", "        references['ifc'] = True\n", "        print(f\"Found {len(ifc_files)} IFC file(s)\")\n", "    \n", "    # Check for CAD files\n", "    cad_files = list((site_path / 'raw').glob('*.dxf')) + list((site_path / 'raw').glob('*.dwg'))\n", "    if cad_files:\n", "        references['cad'] = True\n", "        print(f\"Found {len(cad_files)} CAD file(s)\")\n", "    \n", "    # Check for extracted metadata\n", "    metadata_files = [\n", "        *list((site_path / 'preprocessing').glob('*_metadata.csv')),\n", "        *list(Path('../preprocessing/output_runs').glob(f'{site_name}_*_metadata.csv'))\n", "    ]\n", "    if metadata_files:\n", "        references['metadata'] = True\n", "        print(f\"Found {len(metadata_files)} metadata file(s)\")\n", "    \n", "    # Check for GPS survey data (common file patterns)\n", "    gps_files = [\n", "        *list((site_path / 'raw').glob('*gps*.csv')),\n", "        *list((site_path / 'raw').glob('*survey*.csv')),\n", "        *list((site_path / 'raw').glob('*coordinates*.csv'))\n", "    ]\n", "    if gps_files:\n", "        references['gps'] = True\n", "        print(f\"Found {len(gps_files)} GPS/survey file(s)\")\n", "    \n", "    return references\n", "\n", "# Detect available references\n", "available_refs = detect_available_references(project_type, site_name)\n", "\n", "print(f\"\\nAvailable reference sources:\")\n", "for ref_type, available in available_refs.items():\n", "    status = \"✓\" if available else \"✗\"\n", "    print(f\"  {status} {ref_type.upper()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Alignment Strategy Selection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def select_alignment_strategy(available_refs, strategy_preference=\"auto\"):\n", "    \"\"\"\n", "    Select the best alignment strategy based on available data.\n", "    \"\"\"\n", "    \n", "    if strategy_preference != \"auto\":\n", "        return strategy_preference\n", "    \n", "    # Priority order: IFC > CAD > GPS > Relative\n", "    if available_refs['ifc']:\n", "        return \"ifc\"\n", "    elif available_refs['cad'] or available_refs['metadata']:\n", "        return \"cad\"\n", "    elif available_refs['gps']:\n", "        return \"gps\"\n", "    else:\n", "        return \"relative\"\n", "\n", "# Select strategy\n", "selected_strategy = select_alignment_strategy(available_refs, alignment_strategy)\n", "\n", "print(f\"\\nSelected alignment strategy: {selected_strategy.upper()}\")\n", "\n", "strategy_descriptions = {\n", "    \"ifc\": \"IFC-based alignment using design pile positions (highest accuracy)\",\n", "    \"cad\": \"CAD-based alignment using INSERT block positions (good accuracy)\", \n", "    \"gps\": \"GPS-based alignment using survey control points (moderate accuracy)\",\n", "    \"relative\": \"Point cloud to point cloud alignment (lowest accuracy)\"\n", "}\n", "\n", "print(f\"Strategy: {strategy_descriptions[selected_strategy]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_point_cloud_files(project_type, site_name):\n", "    \"\"\"\n", "    Find point cloud files for the site.\n", "    \"\"\"\n", "    site_path = data_path / project_type / site_name\n", "    \n", "    # Look for point cloud files in common locations\n", "    search_paths = [\n", "        site_path / 'raw',\n", "        site_path / 'processed',\n", "        site_path / 'point_clouds'\n", "    ]\n", "    \n", "    point_cloud_files = []\n", "    for search_path in search_paths:\n", "        if search_path.exists():\n", "            # Common point cloud formats\n", "            for pattern in ['*.las', '*.laz', '*.ply', '*.pcd', '*.xyz']:\n", "                point_cloud_files.extend(list(search_path.glob(pattern)))\n", "    \n", "    return point_cloud_files\n", "\n", "# Find point cloud files\n", "pc_files = find_point_cloud_files(project_type, site_name)\n", "\n", "if pc_files:\n", "    print(f\"Found {len(pc_files)} point cloud file(s):\")\n", "    for pc_file in pc_files:\n", "        print(f\"  - {pc_file.name}\")\n", "    \n", "    # Use the first point cloud file for processing\n", "    if not point_cloud_path:\n", "        point_cloud_path = str(pc_files[0])\n", "        print(f\"\\nUsing point cloud: {pc_files[0].name}\")\n", "else:\n", "    print(f\"No point cloud files found for {site_name}\")\n", "    print(\"Please ensure point cloud files (.las, .laz, .ply, .pcd, .xyz) are available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Reference Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_reference_data(selected_strategy, project_type, site_name):\n", "    \"\"\"\n", "    Load reference data based on selected strategy.\n", "    \"\"\"\n", "    reference_points = []\n", "    \n", "    if selected_strategy == \"cad\":\n", "        # Load CAD metadata from preprocessing output\n", "        metadata_files = list(Path('../preprocessing/output_runs').glob(f'{site_name}_cad_*_metadata.csv'))\n", "        \n", "        if metadata_files:\n", "            # Use the most recent metadata file\n", "            latest_metadata = max(metadata_files, key=lambda x: x.stat().st_mtime)\n", "            print(f\"Loading CAD reference data from: {latest_metadata.name}\")\n", "            \n", "            try:\n", "                df = pd.read_csv(latest_metadata)\n", "                if len(df) > 0:\n", "                    # Extract pile positions\n", "                    for _, row in df.iterrows():\n", "                        if pd.notna(row['x_local']) and pd.notna(row['y_local']):\n", "                            reference_points.append({\n", "                                'id': row['element_id'],\n", "                                'name': row['element_name'],\n", "                                'x': float(row['x_local']),\n", "                                'y': float(row['y_local']),\n", "                                'z': float(row['z_local']) if pd.notna(row['z_local']) else 0.0,\n", "                                'source': 'CAD'\n", "                            })\n", "                    \n", "                    print(f\"Loaded {len(reference_points)} reference points from CAD\")\n", "                else:\n", "                    print(\"CAD metadata file is empty\")\n", "            except Exception as e:\n", "                print(f\"Error loading CAD metadata: {e}\")\n", "        else:\n", "            print(\"No CAD metadata files found. Please run CAD metadata extraction first.\")\n", "    \n", "    elif selected_strategy == \"ifc\":\n", "        # Load IFC metadata (similar pattern)\n", "        metadata_files = list(Path('../preprocessing/output_runs').glob(f'{site_name}_ifc_*_metadata.csv'))\n", "        # Implementation similar to CAD...\n", "        print(\"IFC reference loading not yet implemented\")\n", "    \n", "    elif selected_strategy == \"gps\":\n", "        # Load GPS survey data\n", "        print(\"GPS reference loading not yet implemented\")\n", "    \n", "    else:\n", "        print(f\"Reference loading for strategy '{selected_strategy}' not implemented\")\n", "    \n", "    return reference_points\n", "\n", "# Load reference data\n", "reference_points = load_reference_data(selected_strategy, project_type, site_name)\n", "\n", "if reference_points:\n", "    print(f\"\\nReference points summary:\")\n", "    print(f\"  Total points: {len(reference_points)}\")\n", "    print(f\"  Source: {reference_points[0]['source']}\")\n", "    \n", "    # Display coordinate bounds\n", "    x_coords = [p['x'] for p in reference_points]\n", "    y_coords = [p['y'] for p in reference_points]\n", "    z_coords = [p['z'] for p in reference_points]\n", "    \n", "    print(f\"  X range: {min(x_coords):.2f} to {max(x_coords):.2f}\")\n", "    print(f\"  Y range: {min(y_coords):.2f} to {max(y_coords):.2f}\")\n", "    print(f\"  Z range: {min(z_coords):.2f} to {max(z_coords):.2f}\")\n", "else:\n", "    print(\"No reference points loaded. Cannot proceed with alignment.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Perform Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def perform_alignment(point_cloud_path, reference_points, selected_strategy):\n", "    \"\"\"\n", "    Perform point cloud alignment based on reference data.\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        print(\"Open3D not available. Cannot perform alignment.\")\n", "        return None, None\n", "    \n", "    if not point_cloud_path or not Path(point_cloud_path).exists():\n", "        print(f\"Point cloud file not found: {point_cloud_path}\")\n", "        return None, None\n", "    \n", "    if len(reference_points) < min_reference_points:\n", "        print(f\"Insufficient reference points: {len(reference_points)} < {min_reference_points}\")\n", "        return None, None\n", "    \n", "    try:\n", "        # Load point cloud\n", "        print(f\"Loading point cloud: {Path(point_cloud_path).name}\")\n", "        \n", "        if point_cloud_path.endswith('.las') or point_cloud_path.endswith('.laz'):\n", "            # For LAS/LAZ files, we'd need laspy or similar\n", "            print(\"LAS/LAZ loading requires additional implementation\")\n", "            return None, None\n", "        else:\n", "            # Load with Open3D\n", "            pcd = o3d.io.read_point_cloud(point_cloud_path)\n", "        \n", "        if len(pcd.points) == 0:\n", "            print(\"Point cloud is empty\")\n", "            return None, None\n", "        \n", "        print(f\"Loaded point cloud with {len(pcd.points):,} points\")\n", "        \n", "        # Create reference point cloud from reference data\n", "        ref_points = np.array([[p['x'], p['y'], p['z']] for p in reference_points])\n", "        ref_pcd = o3d.geometry.PointCloud()\n", "        ref_pcd.points = o3d.utility.Vector3dVector(ref_points)\n", "        \n", "        print(f\"Created reference point cloud with {len(ref_points)} points\")\n", "        \n", "        # Perform ICP alignment\n", "        print(\"Performing ICP alignment...\")\n", "        \n", "        # Initial alignment (identity matrix)\n", "        initial_transformation = np.eye(4)\n", "        \n", "        # ICP registration\n", "        reg_result = o3d.pipelines.registration.registration_icp(\n", "            pcd, ref_pcd,\n", "            correspondence_threshold,\n", "            initial_transformation,\n", "            o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "            o3d.pipelines.registration.ICPConvergenceCriteria(\n", "                max_iteration=icp_max_iterations,\n", "                relative_fitness=icp_tolerance,\n", "                relative_rmse=icp_tolerance\n", "            )\n", "        )\n", "        \n", "        # Apply transformation to point cloud\n", "        aligned_pcd = pcd.transform(reg_result.transformation)\n", "        \n", "        # Calculate alignment quality metrics\n", "        alignment_error = reg_result.inlier_rmse\n", "        fitness = reg_result.fitness\n", "        \n", "        print(f\"\\nAlignment completed:\")\n", "        print(f\"  RMSE: {alignment_error:.4f} meters\")\n", "        print(f\"  Fitness: {fitness:.4f}\")\n", "        print(f\"  Converged: {reg_result.converged}\")\n", "        \n", "        # Check alignment quality\n", "        if alignment_error > max_alignment_error:\n", "            print(f\"Warning: Alignment error ({alignment_error:.4f}m) exceeds threshold ({max_alignment_error}m)\")\n", "        \n", "        return aligned_pcd, {\n", "            'transformation_matrix': reg_result.transformation,\n", "            'rmse': alignment_error,\n", "            'fitness': fitness,\n", "            'converged': reg_result.converged,\n", "            'strategy': selected_strategy,\n", "            'reference_points_count': len(reference_points)\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during alignment: {e}\")\n", "        return None, None\n", "\n", "# Perform alignment if we have the required data\n", "aligned_pcd = None\n", "alignment_info = None\n", "\n", "if point_cloud_path and reference_points:\n", "    aligned_pcd, alignment_info = perform_alignment(point_cloud_path, reference_points, selected_strategy)\n", "else:\n", "    print(\"Skipping alignment - missing point cloud or reference data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Aligned Point Cloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_aligned_point_cloud(aligned_pcd, alignment_info, output_path, site_name, selected_strategy):\n", "    \"\"\"\n", "    Save the aligned point cloud and alignment metadata.\n", "    \"\"\"\n", "    if aligned_pcd is None:\n", "        print(\"No aligned point cloud to save\")\n", "        return None\n", "    \n", "    try:\n", "        # Create output filenames\n", "        base_name = f\"{site_name}_{selected_strategy}_aligned_{timestamp}\"\n", "        \n", "        # Save point cloud in multiple formats\n", "        saved_files = {}\n", "        \n", "        # PLY format (recommended for Open3D)\n", "        ply_path = output_path / f\"{base_name}.ply\"\n", "        o3d.io.write_point_cloud(str(ply_path), aligned_pcd)\n", "        saved_files['ply'] = ply_path\n", "        print(f\"Saved PLY: {ply_path}\")\n", "        \n", "        # PCD format (Point Cloud Data)\n", "        pcd_path = output_path / f\"{base_name}.pcd\"\n", "        o3d.io.write_point_cloud(str(pcd_path), aligned_pcd)\n", "        saved_files['pcd'] = pcd_path\n", "        print(f\"Saved PCD: {pcd_path}\")\n", "        \n", "        # XYZ format (simple text format)\n", "        xyz_path = output_path / f\"{base_name}.xyz\"\n", "        points = np.asarray(aligned_pcd.points)\n", "        np.savetxt(xyz_path, points, fmt='%.6f', delimiter=' ')\n", "        saved_files['xyz'] = xyz_path\n", "        print(f\"Saved XYZ: {xyz_path}\")\n", "        \n", "        # Save alignment metadata\n", "        if alignment_info:\n", "            metadata_path = output_path / f\"{base_name}_alignment_info.json\"\n", "            \n", "            # Prepare metadata for JSON serialization\n", "            metadata = {\n", "                'alignment_info': {\n", "                    'site_name': site_name,\n", "                    'strategy': selected_strategy,\n", "                    'timestamp': timestamp,\n", "                    'rmse': float(alignment_info['rmse']),\n", "                    'fitness': float(alignment_info['fitness']),\n", "                    'converged': bool(alignment_info['converged']),\n", "                    'reference_points_count': int(alignment_info['reference_points_count']),\n", "                    'transformation_matrix': alignment_info['transformation_matrix'].tolist()\n", "                },\n", "                'point_cloud_info': {\n", "                    'total_points': len(aligned_pcd.points),\n", "                    'has_colors': len(aligned_pcd.colors) > 0,\n", "                    'has_normals': len(aligned_pcd.normals) > 0\n", "                },\n", "                'output_files': {\n", "                    'ply': str(ply_path),\n", "                    'pcd': str(pcd_path),\n", "                    'xyz': str(xyz_path)\n", "                }\n", "            }\n", "            \n", "            with open(metadata_path, 'w') as f:\n", "                json.dump(metadata, f, indent=2)\n", "            \n", "            saved_files['metadata'] = metadata_path\n", "            print(f\"Saved metadata: {metadata_path}\")\n", "        \n", "        return saved_files\n", "        \n", "    except Exception as e:\n", "        print(f\"Error saving aligned point cloud: {e}\")\n", "        return None\n", "\n", "# Save aligned point cloud if available\n", "saved_files = None\n", "if aligned_pcd is not None and alignment_info is not None:\n", "    saved_files = save_aligned_point_cloud(aligned_pcd, alignment_info, current_run_path, site_name, selected_strategy)\n", "    \n", "    if saved_files:\n", "        print(f\"\\nAlignment completed successfully!\")\n", "        print(f\"Output directory: {current_run_path}\")\n", "        print(f\"\\nSaved files:\")\n", "        for file_type, file_path in saved_files.items():\n", "            print(f\"  {file_type.upper()}: {file_path.name}\")\n", "else:\n", "    print(\"\\nNo aligned point cloud to save\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This adaptive alignment workflow provides a complete solution for aligning point clouds with available reference data:\n", "\n", "### **Process Summary:**\n", "1. **Detects available reference sources** (IFC, CAD, GPS)\n", "2. **Selects optimal alignment strategy** based on data quality\n", "3. **Loads point cloud and reference data**\n", "4. **Performs ICP alignment** with quality metrics\n", "5. **Saves aligned point cloud** in multiple formats\n", "\n", "### **Output Files:**\n", "- **PLY format**: Best for visualization and further processing\n", "- **PCD format**: Open3D native format\n", "- **XYZ format**: Simple text format for compatibility\n", "- **JSON metadata**: Alignment parameters and quality metrics\n", "\n", "### **Output Location:**\n", "```\n", "notebooks/alignment/output_runs/{site_name}_alignment_{timestamp}/\n", "├── {site_name}_{strategy}_aligned_{timestamp}.ply\n", "├── {site_name}_{strategy}_aligned_{timestamp}.pcd  \n", "├── {site_name}_{strategy}_aligned_{timestamp}.xyz\n", "└── {site_name}_{strategy}_aligned_{timestamp}_alignment_info.json\n", "```\n", "\n", "The aligned point cloud is now ready for pile detection and validation workflows."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}