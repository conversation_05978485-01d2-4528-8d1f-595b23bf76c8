{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📊 Pipeline Visualization\n", "\n", "This notebook implements comprehensive visualization as the final stage of the pipeline. It creates interactive 3D overlays, heatmaps, and validation plots from point cloud data and detection/QA results.\n", "\n", "**Stage**: Visualization  \n", "**Input Data**: Point cloud + detection/QA results  \n", "**Output**: 3D overlays, heatmaps, validation plots  \n", "**Format**: .html (for interactive viewers), .png (for summary images in reports/presentations), .npy (optional, for heatmap matrices)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Pipeline Results**: Import point clouds and all detection/analysis results\n", "2. **3D Overlays**: Create interactive 3D visualizations with detection overlays\n", "3. **Heatmaps**: Generate quality and deviation heatmaps\n", "4. **Validation Plots**: Create summary plots for reports and presentations\n", "5. **Interactive Viewers**: Export HTML files for web-based exploration\n", "6. **Export Results**: Save visualizations in specified formats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1️⃣ Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 3D visualization\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import plotly.offline as pyo\n", "\n", "# Point cloud processing\n", "import open3d as o3d\n", "\n", "# Scientific computing\n", "from scipy.spatial.distance import cdist\n", "from scipy.interpolate import griddata\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "# Image processing\n", "from PIL import Image\n", "import seaborn as sns\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Project organization - adjust based on your project\n", "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "\n", "# Input and output paths following the specified organization\n", "project_base = base_path / 'output' / PROJECT_TYPE / PROJECT_NAME\n", "ground_seg_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'ground_segmentation'\n", "alignment_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'alignment'\n", "detection_path = project_base / 'pile_detection'\n", "compliance_path = project_base / 'compliance_analysis'\n", "trench_seg_path = project_base / 'trench_segmentation'\n", "visualization_path = project_base / 'visualization'\n", "visualization_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"📊 Pipeline Visualization - Ready!\")\n", "print(f\"📁 Data path: {data_path}\")\n", "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n", "print(f\"📥 Input sources: Ground segmentation, Alignment, Detection, Compliance, Trench segmentation\")\n", "print(f\"💾 Visualization output path: {visualization_path}\")\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "pyo.init_notebook_mode(connected=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2️⃣ Load Pipeline Results\n", "\n", "Load point cloud data and all detection/analysis results from previous stages."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_pipeline_results():\n", "    \"\"\"\n", "    Load all pipeline results for visualization.\n", "    Expected input: Point cloud + detection/QA results from all stages.\n", "    \"\"\"\n", "    print(f\"🔍 Loading pipeline results for visualization...\")\n", "    \n", "    pipeline_data = {\n", "        'point_clouds': {},\n", "        'detection_results': {},\n", "        'compliance_results': {},\n", "        'trench_results': {},\n", "        'metadata': {}\n", "    }\n", "    \n", "    # 1. Load point cloud data\n", "    point_cloud_sources = [\n", "        # Aligned point cloud (preferred)\n", "        {\n", "            'name': 'aligned',\n", "            'path': alignment_path / f'{PROJECT_NAME}_aligned_point_cloud.pcd',\n", "            'description': 'Aligned point cloud from IFC/CAD alignment'\n", "        },\n", "        # Ground-filtered point cloud\n", "        {\n", "            'name': 'non_ground',\n", "            'path': ground_seg_path / f'{PROJECT_NAME}_non_ground_points.ply',\n", "            'description': 'Non-ground points from ground segmentation'\n", "        },\n", "        # Ground points\n", "        {\n", "            'name': 'ground',\n", "            'path': ground_seg_path / f'{PROJECT_NAME}_ground_points.ply',\n", "            'description': 'Ground points from ground segmentation'\n", "        },\n", "        # Raw point cloud (fallback)\n", "        {\n", "            'name': 'raw',\n", "            'path': data_path / PROJECT_TYPE / PROJECT_NAME / 'raw' / f'{PROJECT_NAME}_point_cloud.las',\n", "            'description': 'Raw point cloud data'\n", "        }\n", "    ]\n", "    \n", "    for pc_source in point_cloud_sources:\n", "        if pc_source['path'].exists():\n", "            try:\n", "                if pc_source['path'].suffix.lower() == '.las':\n", "                    import laspy\n", "                    las_file = laspy.read(str(pc_source['path']))\n", "                    points = np.vstack((las_file.x, las_file.y, las_file.z)).T\n", "                    colors = None\n", "                    if hasattr(las_file, 'red'):\n", "                        colors = np.vstack((las_file.red, las_file.green, las_file.blue)).T / 65535.0\n", "                else:\n", "                    pcd = o3d.io.read_point_cloud(str(pc_source['path']))\n", "                    points = np.asarray(pcd.points)\n", "                    colors = np.asarray(pcd.colors) if pcd.has_colors() else None\n", "                \n", "                pipeline_data['point_clouds'][pc_source['name']] = {\n", "                    'points': points,\n", "                    'colors': colors,\n", "                    'description': pc_source['description'],\n", "                    'source_path': str(pc_source['path'])\n", "                }\n", "                \n", "                print(f\"✅ Loaded {pc_source['name']} point cloud: {points.shape[0]:,} points\")\n", "                \n", "            except Exception as e:\n", "                print(f\"⚠️ Could not load {pc_source['name']} point cloud: {e}\")\n", "    \n", "    # 2. Load detection results\n", "    detection_files = [\n", "        {\n", "            'name': 'pile_detection',\n", "            'csv_path': detection_path / f'{PROJECT_NAME}_detected_pile_centers.csv',\n", "            'json_path': detection_path / f'{PROJECT_NAME}_pile_detection_metadata.json'\n", "        }\n", "    ]\n", "    \n", "    for det_file in detection_files:\n", "        if det_file['csv_path'].exists():\n", "            try:\n", "                detection_df = pd.read_csv(det_file['csv_path'])\n", "                pipeline_data['detection_results'][det_file['name']] = {\n", "                    'data': detection_df,\n", "                    'source_path': str(det_file['csv_path'])\n", "                }\n", "                \n", "                if det_file['json_path'].exists():\n", "                    with open(det_file['json_path'], 'r') as f:\n", "                        metadata = json.load(f)\n", "                    pipeline_data['detection_results'][det_file['name']]['metadata'] = metadata\n", "                \n", "                print(f\"✅ Loaded {det_file['name']}: {len(detection_df)} detections\")\n", "                \n", "            except Exception as e:\n", "                print(f\"⚠️ Could not load {det_file['name']}: {e}\")\n", "    \n", "    return pipeline_data\n", "\n", "# Load all pipeline results\n", "pipeline_data = load_pipeline_results()\n", "\n", "print(f\"\\n📊 Pipeline data summary:\")\n", "print(f\"  Point clouds loaded: {len(pipeline_data['point_clouds'])}\")\n", "print(f\"  Detection results: {len(pipeline_data['detection_results'])}\")\n", "print(f\"  Available for visualization: {list(pipeline_data['point_clouds'].keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3️⃣ 3D Interactive Visualization Functions\n", "\n", "Create interactive 3D overlays with detection results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_3d_point_cloud_overlay(pipeline_data, output_path, project_name):\n", "    \"\"\"\n", "    Create interactive 3D point cloud visualization with detection overlays.\n", "    \"\"\"\n", "    print(f\"🎨 Creating 3D point cloud overlay...\")\n", "    \n", "    # Get primary point cloud (prefer aligned, fallback to others)\n", "    pc_priority = ['aligned', 'non_ground', 'ground', 'raw']\n", "    primary_pc = None\n", "    pc_name = None\n", "    \n", "    for pc_type in pc_priority:\n", "        if pc_type in pipeline_data['point_clouds']:\n", "            primary_pc = pipeline_data['point_clouds'][pc_type]\n", "            pc_name = pc_type\n", "            break\n", "    \n", "    if primary_pc is None:\n", "        print(\"❌ No point cloud data available for visualization\")\n", "        return None\n", "    \n", "    points = primary_pc['points']\n", "    colors = primary_pc['colors']\n", "    \n", "    # Downsample for performance if too many points\n", "    if len(points) > 100000:\n", "        indices = np.random.choice(len(points), 100000, replace=False)\n", "        points = points[indices]\n", "        if colors is not None:\n", "            colors = colors[indices]\n", "        print(f\"📉 Downsampled to {len(points):,} points for visualization\")\n", "    \n", "    # Create base point cloud trace\n", "    if colors is not None:\n", "        # Use RGB colors if available\n", "        rgb_colors = [f'rgb({int(r*255)},{int(g*255)},{int(b*255)})' for r, g, b in colors]\n", "        point_trace = go.Scatter3d(\n", "            x=points[:, 0],\n", "            y=points[:, 1],\n", "            z=points[:, 2],\n", "            mode='markers',\n", "            marker=dict(\n", "                size=1,\n", "                color=rgb_colors,\n", "                opacity=0.6\n", "            ),\n", "            name=f'Point Cloud ({pc_name})',\n", "            hovertemplate='X: %{x:.2f}<br>Y: %{y:.2f}<br>Z: %{z:.2f}<extra></extra>'\n", "        )\n", "    else:\n", "        # Use height-based coloring\n", "        point_trace = go.Scatter3d(\n", "            x=points[:, 0],\n", "            y=points[:, 1],\n", "            z=points[:, 2],\n", "            mode='markers',\n", "            marker=dict(\n", "                size=1,\n", "                color=points[:, 2],\n", "                colorscale='Viridis',\n", "                opacity=0.6,\n", "                colorbar=dict(title=\"Height (m)\")\n", "            ),\n", "            name=f'Point Cloud ({pc_name})',\n", "            hovertemplate='X: %{x:.2f}<br>Y: %{y:.2f}<br>Z: %{z:.2f}<extra></extra>'\n", "        )\n", "    \n", "    traces = [point_trace]\n", "    \n", "    # Add detection overlays\n", "    if 'pile_detection' in pipeline_data['detection_results']:\n", "        detection_data = pipeline_data['detection_results']['pile_detection']['data']\n", "        \n", "        # Create pile detection overlay\n", "        pile_colors = {\n", "            'I-section': 'red',\n", "            'cylindrical': 'blue',\n", "            'i_section': 'red',\n", "            'unknown': 'gray'\n", "        }\n", "        \n", "        for pile_type in detection_data['pile_type'].unique():\n", "            pile_subset = detection_data[detection_data['pile_type'] == pile_type]\n", "            \n", "            pile_trace = go.Scatter3d(\n", "                x=pile_subset['x'],\n", "                y=pile_subset['y'],\n", "                z=pile_subset['z'],\n", "                mode='markers',\n", "                marker=dict(\n", "                    size=8,\n", "                    color=pile_colors.get(pile_type, 'orange'),\n", "                    symbol='diamond',\n", "                    line=dict(width=2, color='black'),\n", "                    opacity=0.9\n", "                ),\n", "                name=f'Detected Piles ({pile_type})',\n", "                text=pile_subset['pile_id'],\n", "                hovertemplate='Pile ID: %{text}<br>X: %{x:.2f}<br>Y: %{y:.2f}<br>Z: %{z:.2f}<br>Type: ' + pile_type + '<extra></extra>'\n", "            )\n", "            traces.append(pile_trace)\n", "    \n", "    # Create layout\n", "    layout = go.Layout(\n", "        title=f'{project_name} - 3D Point Cloud with Detection Overlay',\n", "        scene=dict(\n", "            xaxis_title='X (m)',\n", "            yaxis_title='Y (m)',\n", "            zaxis_title='Z (m)',\n", "            aspectmode='data',\n", "            camera=dict(\n", "                eye=dict(x=1.5, y=1.5, z=1.5)\n", "            )\n", "        ),\n", "        width=1200,\n", "        height=800,\n", "        showlegend=True\n", "    )\n", "    \n", "    # Create figure\n", "    fig = go.Figure(data=traces, layout=layout)\n", "    \n", "    # Export as HTML\n", "    html_filename = f\"{project_name}_3d_point_cloud_overlay.html\"\n", "    html_path = output_path / html_filename\n", "    \n", "    fig.write_html(str(html_path))\n", "    \n", "    print(f\"✅ Exported 3D overlay: {html_path}\")\n", "    print(f\"   Format: .html (interactive 3D viewer)\")\n", "    print(f\"   Points: {len(points):,} point cloud + {len(detection_data) if 'pile_detection' in pipeline_data['detection_results'] else 0} detections\")\n", "    \n", "    return html_path, fig"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4️⃣ Heatmap Generation Functions\n", "\n", "Create quality and deviation heatmaps."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_quality_heatmaps(pipeline_data, output_path, project_name):\n", "    \"\"\"\n", "    Create quality and deviation heatmaps from pipeline results.\n", "    \"\"\"\n", "    print(f\"🔥 Creating quality heatmaps...\")\n", "    \n", "    exported_files = []\n", "    \n", "    # Get point cloud bounds for heatmap grid\n", "    primary_pc = None\n", "    for pc_type in ['aligned', 'non_ground', 'raw']:\n", "        if pc_type in pipeline_data['point_clouds']:\n", "            primary_pc = pipeline_data['point_clouds'][pc_type]\n", "            break\n", "    \n", "    if primary_pc is None:\n", "        print(\"❌ No point cloud data available for heatmaps\")\n", "        return []\n", "    \n", "    points = primary_pc['points']\n", "    x_min, y_min = points[:, :2].min(axis=0)\n", "    x_max, y_max = points[:, :2].max(axis=0)\n", "    \n", "    # Create grid for heatmaps\n", "    grid_resolution = 1.0  # 1 meter resolution\n", "    x_grid = np.arange(x_min, x_max + grid_resolution, grid_resolution)\n", "    y_grid = np.arange(y_min, y_max + grid_resolution, grid_resolution)\n", "    X, Y = np.meshgrid(x_grid, y_grid)\n", "    \n", "    # 1. Point Density Heatmap\n", "    print(\"📊 Creating point density heatmap...\")\n", "    \n", "    # Calculate point density in each grid cell\n", "    density_grid = np.zeros_like(X)\n", "    \n", "    for i in range(len(x_grid) - 1):\n", "        for j in range(len(y_grid) - 1):\n", "            # Count points in this grid cell\n", "            mask = ((points[:, 0] >= x_grid[i]) & (points[:, 0] < x_grid[i + 1]) &\n", "                   (points[:, 1] >= y_grid[j]) & (points[:, 1] < y_grid[j + 1]))\n", "            density_grid[j, i] = mask.sum()\n", "    \n", "    # Create density heatmap\n", "    fig_density = go.Figure(data=go.Heatmap(\n", "        z=density_grid,\n", "        x=x_grid[:-1],\n", "        y=y_grid[:-1],\n", "        colorscale='Viridis',\n", "        colorbar=dict(title=\"Point Density\")\n", "    ))\n", "    \n", "    fig_density.update_layout(\n", "        title=f'{project_name} - Point Cloud Density Heatmap',\n", "        xaxis_title='X (m)',\n", "        yaxis_title='Y (m)',\n", "        width=800,\n", "        height=600\n", "    )\n", "    \n", "    # Export density heatmap\n", "    density_html = output_path / f\"{project_name}_point_density_heatmap.html\"\n", "    density_png = output_path / f\"{project_name}_point_density_heatmap.png\"\n", "    density_npy = output_path / f\"{project_name}_point_density_matrix.npy\"\n", "    \n", "    fig_density.write_html(str(density_html))\n", "    fig_density.write_image(str(density_png), width=800, height=600, scale=2)\n", "    np.save(density_npy, density_grid)\n", "    \n", "    exported_files.extend([density_html, density_png, density_npy])\n", "    \n", "    print(f\"✅ Exported density heatmap: {density_html.name}\")\n", "    \n", "    # 2. Detection Confidence Heatmap (if available)\n", "    if 'pile_detection' in pipeline_data['detection_results']:\n", "        print(\"📊 Creating detection confidence heatmap...\")\n", "        \n", "        detection_data = pipeline_data['detection_results']['pile_detection']['data']\n", "        \n", "        if 'confidence' in detection_data.columns:\n", "            # Interpolate confidence values to grid\n", "            det_points = detection_data[['x', 'y']].values\n", "            det_confidence = detection_data['confidence'].values\n", "            \n", "            # Create grid points for interpolation\n", "            grid_points = np.column_stack([X.ravel(), Y.ravel()])\n", "            \n", "            # Interpolate confidence values\n", "            confidence_interp = griddata(\n", "                det_points, det_confidence, grid_points, \n", "                method='linear', fill_value=0\n", "            )\n", "            confidence_grid = confidence_interp.reshape(X.shape)\n", "            \n", "            # Create confidence heatmap\n", "            fig_confidence = go.Figure(data=go.Heatmap(\n", "                z=confidence_grid,\n", "                x=x_grid,\n", "                y=y_grid,\n", "                colorscale='RdYlGn',\n", "                colorbar=dict(title=\"Detection Confidence\")\n", "            ))\n", "            \n", "            # Add detection points overlay\n", "            fig_confidence.add_trace(go.<PERSON>(\n", "                x=detection_data['x'],\n", "                y=detection_data['y'],\n", "                mode='markers',\n", "                marker=dict(\n", "                    size=8,\n", "                    color='black',\n", "                    symbol='diamond',\n", "                    line=dict(width=2, color='white')\n", "                ),\n", "                name='Detected Piles',\n", "                text=detection_data['pile_id'],\n", "                hovertemplate='Pile: %{text}<br>Confidence: %{marker.color:.3f}<extra></extra>'\n", "            ))\n", "            \n", "            fig_confidence.update_layout(\n", "                title=f'{project_name} - Detection Confidence Heatmap',\n", "                xaxis_title='X (m)',\n", "                yaxis_title='Y (m)',\n", "                width=800,\n", "                height=600\n", "            )\n", "            \n", "            # Export confidence heatmap\n", "            conf_html = output_path / f\"{project_name}_confidence_heatmap.html\"\n", "            conf_png = output_path / f\"{project_name}_confidence_heatmap.png\"\n", "            conf_npy = output_path / f\"{project_name}_confidence_matrix.npy\"\n", "            \n", "            fig_confidence.write_html(str(conf_html))\n", "            fig_confidence.write_image(str(conf_png), width=800, height=600, scale=2)\n", "            np.save(conf_npy, confidence_grid)\n", "            \n", "            exported_files.extend([conf_html, conf_png, conf_npy])\n", "            \n", "            print(f\"✅ Exported confidence heatmap: {conf_html.name}\")\n", "    \n", "    return exported_files"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5️⃣ Validation Plots and Summary Visualizations\n", "\n", "Create summary plots for reports and presentations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_validation_plots(pipeline_data, output_path, project_name):\n", "    \"\"\"\n", "    Create validation plots and summary visualizations for reports.\n", "    \"\"\"\n", "    print(f\"📈 Creating validation plots...\")\n", "    \n", "    exported_files = []\n", "    \n", "    # Load compliance analysis results if available\n", "    compliance_csv = compliance_path / f\"{project_name}_compliance_analysis.csv\"\n", "    compliance_data = None\n", "    \n", "    if compliance_csv.exists():\n", "        try:\n", "            compliance_data = pd.read_csv(compliance_csv)\n", "            print(f\"✅ Loaded compliance data: {len(compliance_data)} records\")\n", "        except Exception as e:\n", "            print(f\"⚠️ Could not load compliance data: {e}\")\n", "    \n", "    # 1. Pipeline Summary Dashboard\n", "    print(\"📊 Creating pipeline summary dashboard...\")\n", "    \n", "    # Create subplot layout\n", "    fig_summary = make_subplots(\n", "        rows=2, cols=3,\n", "        subplot_titles=[\n", "            'Detection Summary', 'Pile Type Distribution', 'Confidence Distribution',\n", "            'Spatial Deviations', 'Compliance Overview', 'Quality Metrics'\n", "        ],\n", "        specs=[\n", "            [{'type': 'bar'}, {'type': 'pie'}, {'type': 'histogram'}],\n", "            [{'type': 'scatter'}, {'type': 'bar'}, {'type': 'indicator'}]\n", "        ]\n", "    )\n", "    \n", "    # Detection summary\n", "    if 'pile_detection' in pipeline_data['detection_results']:\n", "        detection_data = pipeline_data['detection_results']['pile_detection']['data']\n", "        \n", "        # Plot 1: Detection counts by type\n", "        type_counts = detection_data['pile_type'].value_counts()\n", "        fig_summary.add_trace(\n", "            go.Bar(x=type_counts.index, y=type_counts.values, name='Detections'),\n", "            row=1, col=1\n", "        )\n", "        \n", "        # Plot 2: Pile type distribution\n", "        fig_summary.add_trace(\n", "            go.Pie(labels=type_counts.index, values=type_counts.values, name='Types'),\n", "            row=1, col=2\n", "        )\n", "        \n", "        # Plot 3: Confidence distribution\n", "        if 'confidence' in detection_data.columns:\n", "            fig_summary.add_trace(\n", "                go.Histogram(x=detection_data['confidence'], name='Confidence', nbinsx=20),\n", "                row=1, col=3\n", "            )\n", "    \n", "    # Compliance analysis plots\n", "    if compliance_data is not None:\n", "        # Plot 4: Spatial deviations scatter\n", "        fig_summary.add_trace(\n", "            <PERSON><PERSON>(\n", "                x=compliance_data['x_deviation'],\n", "                y=compliance_data['y_deviation'],\n", "                mode='markers',\n", "                marker=dict(\n", "                    color=compliance_data['spatial_deviation'],\n", "                    colorscale='Viridis',\n", "                    size=8,\n", "                    colorbar=dict(title=\"Spatial Dev (m)\")\n", "                ),\n", "                text=compliance_data['pile_id'],\n", "                name='Deviations'\n", "            ),\n", "            row=2, col=1\n", "        )\n", "        \n", "        # Plot 5: Compliance overview\n", "        compliance_summary = {\n", "            'Position': compliance_data['position_compliant'].sum(),\n", "            'Spacing': compliance_data['spacing_compliant'].sum(),\n", "            'Verticality': compliance_data['verticality_compliant'].sum(),\n", "            'Rotation': compliance_data['rotation_compliant'].sum(),\n", "            'Overall': compliance_data['overall_compliant'].sum()\n", "        }\n", "        \n", "        fig_summary.add_trace(\n", "            go.Bar(\n", "                x=list(compliance_summary.keys()),\n", "                y=list(compliance_summary.values()),\n", "                name='Compliant Count'\n", "            ),\n", "            row=2, col=2\n", "        )\n", "        \n", "        # Plot 6: Overall quality indicator\n", "        overall_compliance_rate = (compliance_data['overall_compliant'].sum() / len(compliance_data)) * 100\n", "        \n", "        fig_summary.add_trace(\n", "            go.Indicator(\n", "                mode=\"gauge+number+delta\",\n", "                value=overall_compliance_rate,\n", "                domain={'x': [0, 1], 'y': [0, 1]},\n", "                title={'text': \"Overall Compliance %\"},\n", "                gauge={\n", "                    'axis': {'range': [None, 100]},\n", "                    'bar': {'color': \"darkblue\"},\n", "                    'steps': [\n", "                        {'range': [0, 50], 'color': \"lightgray\"},\n", "                        {'range': [50, 80], 'color': \"yellow\"},\n", "                        {'range': [80, 100], 'color': \"green\"}\n", "                    ],\n", "                    'threshold': {\n", "                        'line': {'color': \"red\", 'width': 4},\n", "                        'thickness': 0.75,\n", "                        'value': 90\n", "                    }\n", "                }\n", "            ),\n", "            row=2, col=3\n", "        )\n", "    \n", "    # Update layout\n", "    fig_summary.update_layout(\n", "        title=f'{project_name} - Pipeline Analysis Summary',\n", "        height=800,\n", "        width=1200,\n", "        showlegend=False\n", "    )\n", "    \n", "    # Export summary dashboard\n", "    summary_html = output_path / f\"{project_name}_pipeline_summary.html\"\n", "    summary_png = output_path / f\"{project_name}_pipeline_summary.png\"\n", "    \n", "    fig_summary.write_html(str(summary_html))\n", "    fig_summary.write_image(str(summary_png), width=1200, height=800, scale=2)\n", "    \n", "    exported_files.extend([summary_html, summary_png])\n", "    \n", "    print(f\"✅ Exported pipeline summary: {summary_html.name}\")\n", "    \n", "    return exported_files, fig_summary"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6️⃣ Execute Visualization Pipeline\n", "\n", "Run the complete visualization workflow and export all results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_synthetic_data_for_demo():\n", "    \"\"\"\n", "    Create synthetic data for demonstration when real pipeline data is not available.\n", "    \"\"\"\n", "    print(\"🔧 Creating synthetic data for visualization demo...\")\n", "    \n", "    # Create synthetic point cloud\n", "    np.random.seed(42)\n", "    n_points = 50000\n", "    \n", "    # Generate terrain-like point cloud\n", "    x = np.random.uniform(0, 100, n_points)\n", "    y = np.random.uniform(0, 100, n_points)\n", "    z = 2 * np.sin(x/10) * np.cos(y/10) + 0.5 * np.random.normal(0, 1, n_points)\n", "    \n", "    # Add some structures (piles)\n", "    pile_locations = [(20, 20), (40, 30), (60, 50), (80, 70)]\n", "    for px, py in pile_locations:\n", "        # Add pile points\n", "        pile_mask = ((x - px)**2 + (y - py)**2) < 4  # 2m radius\n", "        z[pile_mask] += np.random.uniform(0.5, 2.0, pile_mask.sum())  # Pile height\n", "    \n", "    points = np.column_stack([x, y, z])\n", "    \n", "    # Create synthetic colors (height-based)\n", "    colors = plt.cm.viridis((z - z.min()) / (z.max() - z.min()))[:, :3]\n", "    \n", "    # Create synthetic detection data\n", "    detection_data = pd.DataFrame({\n", "        'pile_id': [f'P{i+1:03d}' for i in range(len(pile_locations))],\n", "        'x': [loc[0] + np.random.normal(0, 0.2) for loc in pile_locations],\n", "        'y': [loc[1] + np.random.normal(0, 0.2) for loc in pile_locations],\n", "        'z': [2.0 + np.random.normal(0, 0.3) for _ in pile_locations],\n", "        'pile_type': ['I-section', 'cylindrical', 'I-section', 'cylindrical'],\n", "        'confidence': [0.85, 0.92, 0.78, 0.88]\n", "    })\n", "    \n", "    # Create synthetic compliance data\n", "    compliance_data = pd.DataFrame({\n", "        'pile_id': detection_data['pile_id'],\n", "        'x_deviation': np.random.normal(0, 0.3, len(pile_locations)),\n", "        'y_deviation': np.random.normal(0, 0.3, len(pile_locations)),\n", "        'spatial_deviation': np.random.uniform(0.1, 1.5, len(pile_locations)),\n", "        'position_compliant': [True, True, False, True],\n", "        'spacing_compliant': [True, False, True, True],\n", "        'verticality_compliant': [True, True, True, False],\n", "        'rotation_compliant': [True, True, True, True],\n", "        'overall_compliant': [True, <PERSON>alse, <PERSON>alse, False]\n", "    })\n", "    \n", "    # Save synthetic compliance data\n", "    compliance_path.mkdir(parents=True, exist_ok=True)\n", "    compliance_csv = compliance_path / f\"{PROJECT_NAME}_compliance_analysis.csv\"\n", "    compliance_data.to_csv(compliance_csv, index=False)\n", "    \n", "    return {\n", "        'point_clouds': {\n", "            'synthetic': {\n", "                'points': points,\n", "                'colors': colors,\n", "                'description': 'Synthetic point cloud for demo',\n", "                'source_path': 'synthetic'\n", "            }\n", "        },\n", "        'detection_results': {\n", "            'pile_detection': {\n", "                'data': detection_data,\n", "                'source_path': 'synthetic'\n", "            }\n", "        },\n", "        'compliance_results': {},\n", "        'trench_results': {},\n", "        'metadata': {'source': 'synthetic'}\n", "    }\n", "\n", "# Check if we have real data, otherwise create synthetic data\n", "if (len(pipeline_data['point_clouds']) == 0 or \n", "    len(pipeline_data['detection_results']) == 0):\n", "    \n", "    print(\"\\n⚠️ Limited pipeline data available. Creating synthetic data for demonstration...\")\n", "    pipeline_data = create_synthetic_data_for_demo()\n", "    print(f\"🔧 Synthetic data created for visualization demo\")\n", "\n", "print(f\"\\n📊 Starting visualization pipeline...\")\n", "\n", "# Execute all visualization functions\n", "all_exported_files = []\n", "\n", "try:\n", "    # 1. Create 3D point cloud overlay\n", "    print(\"\\n🎨 Creating 3D visualizations...\")\n", "    html_3d, fig_3d = create_3d_point_cloud_overlay(\n", "        pipeline_data, visualization_path, PROJECT_NAME\n", "    )\n", "    if html_3d:\n", "        all_exported_files.append(html_3d)\n", "    \n", "    # 2. Create quality heatmaps\n", "    print(\"\\n🔥 Creating heatmaps...\")\n", "    heatmap_files = create_quality_heatmaps(\n", "        pipeline_data, visualization_path, PROJECT_NAME\n", "    )\n", "    all_exported_files.extend(heatmap_files)\n", "    \n", "    # 3. Create validation plots\n", "    print(\"\\n📈 Creating validation plots...\")\n", "    validation_files, fig_summary = create_validation_plots(\n", "        pipeline_data, visualization_path, PROJECT_NAME\n", "    )\n", "    all_exported_files.extend(validation_files)\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error during visualization: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7️⃣ Export Summary and Results\n", "\n", "Summarize all exported visualization files and create final reports."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_visualization_summary(all_exported_files, output_path, project_name):\n", "    \"\"\"\n", "    Create a summary of all visualization outputs.\n", "    \"\"\"\n", "    print(f\"📋 Creating visualization summary...\")\n", "    \n", "    # Categorize exported files by type\n", "    file_categories = {\n", "        'html_files': [],\n", "        'png_files': [],\n", "        'npy_files': []\n", "    }\n", "    \n", "    for file_path in all_exported_files:\n", "        if file_path.suffix.lower() == '.html':\n", "            file_categories['html_files'].append(file_path)\n", "        elif file_path.suffix.lower() == '.png':\n", "            file_categories['png_files'].append(file_path)\n", "        elif file_path.suffix.lower() == '.npy':\n", "            file_categories['npy_files'].append(file_path)\n", "    \n", "    # Create visualization summary metadata\n", "    visualization_summary = {\n", "        'project_info': {\n", "            'project_name': project_name,\n", "            'visualization_timestamp': datetime.now().isoformat(),\n", "            'stage': 'visualization'\n", "        },\n", "        'visualization_types': {\n", "            '3d_overlays': 'Interactive 3D point cloud with detection overlays',\n", "            'heatmaps': 'Quality and deviation heatmaps',\n", "            'validation_plots': 'Summary plots for reports and presentations'\n", "        },\n", "        'output_formats': {\n", "            'html_files': {\n", "                'purpose': 'Interactive viewers for web-based exploration',\n", "                'count': len(file_categories['html_files']),\n", "                'files': [f.name for f in file_categories['html_files']]\n", "            },\n", "            'png_files': {\n", "                'purpose': 'Summary images for reports and presentations',\n", "                'count': len(file_categories['png_files']),\n", "                'files': [f.name for f in file_categories['png_files']]\n", "            },\n", "            'npy_files': {\n", "                'purpose': 'Heatmap matrices for further analysis',\n", "                'count': len(file_categories['npy_files']),\n", "                'files': [f.name for f in file_categories['npy_files']]\n", "            }\n", "        },\n", "        'usage_instructions': {\n", "            'html_files': 'Open in web browser for interactive exploration',\n", "            'png_files': 'Include in reports, presentations, or documentation',\n", "            'npy_files': 'Load with numpy.load() for programmatic analysis'\n", "        },\n", "        'total_files_exported': len(all_exported_files)\n", "    }\n", "    \n", "    # Export visualization summary\n", "    summary_json = output_path / f\"{project_name}_visualization_summary.json\"\n", "    with open(summary_json, 'w') as f:\n", "        json.dump(visualization_summary, f, indent=2)\n", "    \n", "    print(f\"💾 Visualization summary saved: {summary_json}\")\n", "    \n", "    return visualization_summary, summary_json\n", "\n", "# Create visualization summary\n", "if all_exported_files:\n", "    viz_summary, summary_file = create_visualization_summary(\n", "        all_exported_files, visualization_path, PROJECT_NAME\n", "    )\n", "    \n", "    print(f\"\\n✅ Visualization pipeline complete! Output summary:\")\n", "    print(f\"\\n📁 Output directory: {visualization_path}\")\n", "    print(f\"📊 Total files exported: {len(all_exported_files)}\")\n", "    \n", "    print(f\"\\n📋 Files by format:\")\n", "    for format_type, format_info in viz_summary['output_formats'].items():\n", "        if format_info['count'] > 0:\n", "            print(f\"  📄 {format_type.upper()}: {format_info['count']} files\")\n", "            print(f\"     Purpose: {format_info['purpose']}\")\n", "            for filename in format_info['files']:\n", "                print(f\"     - {filename}\")\n", "    \n", "    print(f\"\\n🎯 Usage Instructions:\")\n", "    for format_type, instruction in viz_summary['usage_instructions'].items():\n", "        if viz_summary['output_formats'][format_type]['count'] > 0:\n", "            print(f\"  📄 {format_type.upper()}: {instruction}\")\n", "    \n", "    print(f\"\\n🔄 Next Steps:\")\n", "    print(f\"  - Open HTML files in web browser for interactive exploration\")\n", "    print(f\"  - Include PNG files in reports and presentations\")\n", "    print(f\"  - Use NPY files for further programmatic analysis\")\n", "    print(f\"  - Share visualization outputs with stakeholders\")\n", "    \n", "else:\n", "    print(\"❌ No visualization files were exported.\")\n", "    print(\"   Check that pipeline data is available and visualization functions executed successfully.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📝 Summary\n", "\n", "This visualization notebook successfully processed pipeline results:\n", "\n", "### ✅ **What We Accomplished:**\n", "1. **Loaded Pipeline Data**: Processed point clouds and detection/QA results from all stages\n", "2. **3D Interactive Overlays**: Created web-based 3D visualizations with detection overlays\n", "3. **Quality Heatmaps**: Generated density and confidence heatmaps for quality assessment\n", "4. **Validation Plots**: Created summary dashboards for reports and presentations\n", "5. **Multi-Format Export**: Saved visualizations in HTML, PNG, and NPY formats\n", "\n", "### 📊 **Output Formats:**\n", "- **HTML**: Interactive viewers for web-based exploration and stakeholder review\n", "- **PNG**: High-quality summary images for reports, presentations, and documentation\n", "- **NPY**: Heatmap matrices for further programmatic analysis and processing\n", "\n", "### 🎯 **Visualization Types:**\n", "- **3D Point Cloud Overlays**: Interactive exploration of point clouds with detection results\n", "- **Quality Heatmaps**: Spatial distribution of point density and detection confidence\n", "- **Validation Dashboards**: Comprehensive summary of pipeline performance and compliance\n", "- **Deviation Analysis**: Visual analysis of spatial and compliance deviations\n", "\n", "### 🔄 **Integration with Pipeline:**\n", "The visualization stage successfully integrates results from:\n", "- **Ground Segmentation**: Point cloud processing and filtering\n", "- **Alignment**: IFC/CAD alignment and transformation\n", "- **<PERSON>le Detection**: Detected pile locations and classifications\n", "- **Compliance Analysis**: Deviation statistics and compliance assessment\n", "- **Trench Segmentation**: Trench detection and segmentation results\n", "\n", "**📧 Contact**: For questions about visualization outputs, reach out to the development team."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}