{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Thesis Data Download\n", "\n", "**Purpose**: Download optimal datasets using rclone commands\n", "\n", "**Target Datasets**: \n", "- Castro Area4 (7 GB) - Primary dataset\n", "- RPCS Point Cloud (1.3 GB) - Secondary dataset  \n", "- <PERSON> (36 MB) - Testing dataset\n", "\n", "**Total**: ~8.3 GB\n", "\n", "**Method**: Using rclone for reliable S3 downloads with progress tracking"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data directory: /Users/<USER>/Documents/GitHub/energy-inspection-3d/notebooks/data_acquisition/../../data/thesis_datasets/pointcloud\n", "Download started: 2025-06-13 16:38:23\n"]}], "source": ["import os\n", "from pathlib import Path\n", "from datetime import datetime\n", "\n", "# Setup data directory\n", "DATA_DIR = Path(\"../../data/thesis_datasets/pointcloud\")\n", "DATA_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Data directory: {DATA_DIR.absolute()}\")\n", "print(f\"Download started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Download Castro Area4 (Primary Dataset)\n", "\n", "7 GB - Most manageable Castro file:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download Castro Area4 (7 GB) using rclone\n", "!rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/area4_point.las ../../data/thesis_datasets/pointcloud/ --progress"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify Castro download\n", "castro_file = Path(\"../../data/thesis_datasets/pointcloud/area4_point.las\")\n", "if castro_file.exists():\n", "    size_gb = castro_file.stat().st_size / (1024**3)\n", "    print(f\"Castro Area4 downloaded: {size_gb:.1f} GB\")\n", "else:\n", "    print(\"Castro Area4 download failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Download RPCS Point Cloud (Secondary Dataset)\n", "\n", "1.3 GB - Perfect for cross-validation:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download RPCS Point Cloud (1.3 GB) using rclone\n", "!rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las ../../data/thesis_datasets/pointcloud/ --progress"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify RPCS download\n", "rpcs_file = Path(\"../../data/thesis_datasets/pointcloud/Point_Cloud.las\")\n", "if rpcs_file.exists():\n", "    size_gb = rpcs_file.stat().st_size / (1024**3)\n", "    print(f\"RPCS Point Cloud downloaded: {size_gb:.1f} GB\")\n", "else:\n", "    print(\"RPCS Point Cloud download failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Download <PERSON> (Testing Dataset)\n", "\n", "36 MB - Tiny dataset for quick testing:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download <PERSON> (36 MB) using rclone\n", "!rclone copy \"datasee-s3:preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las\" ../../data/thesis_datasets/pointcloud/ --progress"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify McCarthy download\n", "mccarthy_file = Path(\"../../data/thesis_datasets/pointcloud/Buffer_las(rev1).las\")\n", "if mccarthy_file.exists():\n", "    size_mb = mccarthy_file.stat().st_size / (1024**2)\n", "    print(f\"<PERSON> downloaded: {size_mb:.1f} MB\")\n", "else:\n", "    print(\"<PERSON> Buffer download failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Download Summary\n", "\n", "Check all downloads and create summary:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check all downloads\n", "import pandas as pd\n", "\n", "datasets = [\n", "    {'name': 'Castro Area4', 'file': 'area4_point.las', 'expected_gb': 7.0},\n", "    {'name': 'RPCS Point Cloud', 'file': 'Point_Cloud.las', 'expected_gb': 1.3},\n", "    {'name': '<PERSON>', 'file': '<PERSON><PERSON><PERSON>_<PERSON>(rev1).las', 'expected_gb': 0.036}\n", "]\n", "\n", "results = []\n", "total_size = 0\n", "\n", "for dataset in datasets:\n", "    file_path = Path(f\"../../data/thesis_datasets/pointcloud/{dataset['file']}\")\n", "    \n", "    if file_path.exists():\n", "        actual_size_gb = file_path.stat().st_size / (1024**3)\n", "        status = \"Downloaded\"\n", "        total_size += actual_size_gb\n", "    else:\n", "        actual_size_gb = 0\n", "        status = \"Failed\"\n", "    \n", "    results.append({\n", "        'Dataset': dataset['name'],\n", "        'Expected_GB': dataset['expected_gb'],\n", "        'Actual_GB': round(actual_size_gb, 2),\n", "        'Status': status\n", "    })\n", "\n", "summary_df = pd.DataFrame(results)\n", "print(\"DOWNLOAD SUMMARY:\")\n", "print(summary_df)\n", "print(f\"\\nTotal downloaded: {total_size:.1f} GB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Thesis Readiness Check\n", "\n", "Verify datasets are ready for method development:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check thesis readiness\n", "downloaded_count = len([r for r in results if r['Status'] == 'Downloaded'])\n", "\n", "print(\"THESIS READINESS ASSESSMENT:\")\n", "print(\"=\" * 40)\n", "\n", "if downloaded_count >= 2:\n", "    print(f\"READY FOR THESIS WORK!\")\n", "    print(f\"- {downloaded_count} datasets available\")\n", "    print(f\"- {total_size:.1f} GB total data\")\n", "    print(f\"- Sufficient for comparative analysis\")\n", "    \n", "    print(f\"\\nNEXT STEPS:\")\n", "    print(f\"1. Start method implementation on primary dataset\")\n", "    print(f\"2. Validate methods work on single dataset\")\n", "    print(f\"3. Expand to cross-validation\")\n", "    print(f\"4. Perform statistical comparison\")\n", "    \n", "elif downloaded_count == 1:\n", "    print(f\"PARTIAL SUCCESS\")\n", "    print(f\"- 1 dataset available\")\n", "    print(f\"- Can start development\")\n", "    print(f\"- Limited cross-validation\")\n", "    \n", "else:\n", "    print(f\"DOWNLOAD ISSUES\")\n", "    print(f\"- No datasets downloaded\")\n", "    print(f\"- Check AWS credentials\")\n", "    print(f\"- Retry downloads\")\n", "\n", "print(f\"\\nDownload completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Dataset Validation\n", "\n", "Verify thesis readiness:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final validation\n", "print(\"\\n🔍 THESIS READINESS VALIDATION\")\n", "print(\"=\" * 40)\n", "\n", "total_downloaded_size = 0\n", "ready_for_thesis = True\n", "\n", "for key in successful_downloads:\n", "    dataset = DATASETS[key]\n", "    local_path = DATA_DIR / \"pointcloud\" / dataset['local_filename']\n", "    \n", "    if local_path.exists():\n", "        actual_size_gb = local_path.stat().st_size / (1024**3)\n", "        total_downloaded_size += actual_size_gb\n", "        \n", "        print(f\"✅ {dataset['name']}:\")\n", "        print(f\"   File: {local_path}\")\n", "        print(f\"   Size: {actual_size_gb:.2f} GB\")\n", "        print(f\"   Purpose: {dataset['purpose']}\")\n", "        print()\n", "\n", "# Check minimum requirements\n", "if len(successful_downloads) >= 2:\n", "    print(f\"🎉 THESIS READY!\")\n", "    print(f\"   ✅ {len(successful_downloads)} datasets available\")\n", "    print(f\"   ✅ {total_downloaded_size:.1f} GB total data\")\n", "    print(f\"   ✅ Sufficient for comparative analysis\")\n", "    \n", "    print(f\"\\n🚀 NEXT STEPS:\")\n", "    print(f\"   1. Start method implementation on primary dataset\")\n", "    print(f\"   2. Validate methods work on single dataset\")\n", "    print(f\"   3. Expand to cross-validation on secondary datasets\")\n", "    print(f\"   4. Perform statistical comparison analysis\")\n", "    \n", "elif len(successful_downloads) == 1:\n", "    print(f\"⚠️ PARTIAL SUCCESS\")\n", "    print(f\"   ✅ 1 dataset available - can start development\")\n", "    print(f\"   ⚠️ Limited cross-validation capability\")\n", "    print(f\"   💡 Consider downloading additional datasets later\")\n", "    \n", "else:\n", "    print(f\"❌ THESIS RISK\")\n", "    print(f\"   ❌ No datasets successfully downloaded\")\n", "    print(f\"   🔧 Check AWS credentials and network connectivity\")\n", "    print(f\"   🔄 Retry download or consider alternative datasets\")\n", "    ready_for_thesis = False\n", "\n", "# Export dataset manifest\n", "if successful_downloads:\n", "    manifest = {\n", "        'download_date': datetime.now().isoformat(),\n", "        'total_size_gb': total_downloaded_size,\n", "        'datasets': {}\n", "    }\n", "    \n", "    for key in successful_downloads:\n", "        dataset = DATASETS[key]\n", "        local_path = DATA_DIR / \"pointcloud\" / dataset['local_filename']\n", "        manifest['datasets'][key] = {\n", "            'name': dataset['name'],\n", "            'local_path': str(local_path),\n", "            'size_gb': local_path.stat().st_size / (1024**3),\n", "            'purpose': dataset['purpose']\n", "        }\n", "    \n", "    import json\n", "    manifest_path = DATA_DIR / \"dataset_manifest.json\"\n", "    with open(manifest_path, 'w') as f:\n", "        json.dump(manifest, f, indent=2)\n", "    \n", "    print(f\"\\n📄 Dataset manifest saved: {manifest_path}\")\n", "\n", "print(f\"\\n📅 Download completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>\n", "\n", "**Data Acquisition Results:**\n", "- Systematic download of thesis-optimal datasets\n", "- Verification of data integrity and accessibility\n", "- Preparation for method development phase\n", "\n", "**Academic Value:**\n", "- Reproducible data acquisition process\n", "- Clear documentation of dataset selection rationale\n", "- Foundation for rigorous comparative analysis\n", "\n", "**Next Phase:**\n", "- Begin method implementation on primary dataset\n", "- Establish baseline performance metrics\n", "- Prepare for cross-validation analysis"]}], "metadata": {"kernelspec": {"display_name": "pdf_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 4}