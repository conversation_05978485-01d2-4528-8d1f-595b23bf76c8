{"cells": [{"cell_type": "markdown", "id": "db849c94", "metadata": {"tags": ["papermill-error-cell-tag"]}, "source": ["<span style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">An Exception was encountered at '<a href=\"#papermill-error-cell\">In [8]</a>'.</span>"]}, {"cell_type": "markdown", "id": "a273ce3c", "metadata": {"papermill": {"duration": 0.003607, "end_time": "2025-06-15T12:06:26.251737", "exception": false, "start_time": "2025-06-15T12:06:26.248130", "status": "completed"}, "tags": []}, "source": ["# CAD Metadata Extraction\n", "\n", "This notebook extracts metadata and geometric information from CAD files (DXF/DWG) for preprocessing workflows.\n", "\n", "**Stage**: Preprocessing  \n", "**Input Data**: CAD files (.dwg, .dxf)  \n", "**Output**: Structured metadata in CSV, JSON, and Parquet formats  \n", "**Schema**: Standardized pile/element specification format  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load CAD Files**: Import .dwg/.dxf files using ezdxf\n", "2. **Extract Construction Elements**: Identify pile/column INSERT blocks\n", "3. **Geometric Analysis**: Process 3D geometry and measurements\n", "4. **Coordinate Transformation**: Convert to geographic coordinates if needed\n", "5. **Export Structured Data**: Save in multiple formats for downstream usage"]}, {"cell_type": "markdown", "id": "74f5be83", "metadata": {"papermill": {"duration": 0.001199, "end_time": "2025-06-15T12:06:26.256167", "exception": false, "start_time": "2025-06-15T12:06:26.254968", "status": "completed"}, "tags": []}, "source": ["## Parameters"]}, {"cell_type": "code", "execution_count": 1, "id": "0ab29cde", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T12:06:26.259248Z", "iopub.status.busy": "2025-06-15T12:06:26.259111Z", "iopub.status.idle": "2025-06-15T12:06:26.264458Z", "shell.execute_reply": "2025-06-15T12:06:26.264219Z"}, "papermill": {"duration": 0.007743, "end_time": "2025-06-15T12:06:26.265176", "exception": false, "start_time": "2025-06-15T12:06:26.257433", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Trino\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "cad_file_path = \"\"  # Path to specific CAD file (optional)\n", "coordinate_system = \"EPSG:32643\"  # Source coordinate reference system\n", "target_crs = \"EPSG:4326\"  # Target CRS (WGS84 for GPS coordinates)\n", "\n", "# Extraction parameters\n", "pile_name_pattern = r\"PILE_(\\d+)\"  # Regex pattern for pile identification\n", "include_attributes = True  # Extract block attributes\n", "include_geometry = True  # Perform geometric analysis\n", "coordinate_transform = True  # Transform coordinates to target CRS\n", "\n", "# Output format options\n", "export_csv = True  # Export tabular data as CSV\n", "export_json = True  # Export hierarchical metadata as JSON\n", "export_parquet = True  # Export as Parquet for large datasets"]}, {"cell_type": "code", "execution_count": 2, "id": "e06a1d59", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T12:06:26.268189Z", "iopub.status.busy": "2025-06-15T12:06:26.267993Z", "iopub.status.idle": "2025-06-15T12:06:26.270622Z", "shell.execute_reply": "2025-06-15T12:06:26.270358Z"}, "papermill": {"duration": 0.004929, "end_time": "2025-06-15T12:06:26.271333", "exception": false, "start_time": "2025-06-15T12:06:26.266404", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "site_name = \"RPCS\"\n", "project_type = \"USA\"\n", "coordinate_system = \"EPSG:32614\"\n", "target_crs = \"EPSG:4326\"\n", "pile_name_pattern = \"PILE_(\\\\d+)\"\n", "include_attributes = True\n", "include_geometry = True\n", "coordinate_transform = True\n", "export_csv = True\n", "export_json = True\n", "export_parquet = True\n"]}, {"cell_type": "markdown", "id": "1c5315a6", "metadata": {"papermill": {"duration": 0.001065, "end_time": "2025-06-15T12:06:26.273663", "exception": false, "start_time": "2025-06-15T12:06:26.272598", "status": "completed"}, "tags": []}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": 3, "id": "b8d0fc45", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T12:06:26.276253Z", "iopub.status.busy": "2025-06-15T12:06:26.276148Z", "iopub.status.idle": "2025-06-15T12:06:26.924079Z", "shell.execute_reply": "2025-06-15T12:06:26.923708Z"}, "papermill": {"duration": 0.650096, "end_time": "2025-06-15T12:06:26.924805", "exception": false, "start_time": "2025-06-15T12:06:26.274709", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parquet support available: 20.0.0\n", "Coordinate transformation support available\n", "CAD Metadata Extraction - Ready!\n", "Data path: ../../data\n", "Project: USA/RPCS\n", "Input path: ../../data/USA/RPCS/raw\n", "Output path: ../../data/USA/RPCS/preprocessing\n", "Current run output: output_runs/RPCS_cad_20250615_173626\n", "Coordinate system: EPSG:32614 -> EPSG:4326\n"]}], "source": ["# Import libraries\n", "import ezdxf\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import os\n", "import re\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import Counter\n", "import logging\n", "\n", "# Optional imports\n", "try:\n", "    import pyarrow as pa\n", "    import pyarrow.parquet as pq\n", "    PARQUET_SUPPORT = True\n", "    print(f\"Parquet support available: {pa.__version__}\")\n", "except ImportError:\n", "    print(\"Parquet support not available. Install pyarrow for Parquet export.\")\n", "    PARQUET_SUPPORT = False\n", "\n", "try:\n", "    from pyproj import Transformer\n", "    COORDINATE_TRANSFORM_SUPPORT = True\n", "    print(f\"Coordinate transformation support available\")\n", "except ImportError:\n", "    print(\"Coordinate transformation not available. Install pyproj for CRS transformation.\")\n", "    COORDINATE_TRANSFORM_SUPPORT = False\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Create output directory structure for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_cad_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Input and output paths\n", "if cad_file_path:\n", "    input_path = Path(cad_file_path)\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "\n", "preprocessing_path = data_path / project_type / site_name / 'preprocessing'\n", "preprocessing_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"CAD Metadata Extraction - Ready!\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Project: {project_type}/{site_name}\")\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {preprocessing_path}\")\n", "print(f\"Current run output: {current_run_path}\")\n", "print(f\"Coordinate system: {coordinate_system} -> {target_crs}\")"]}, {"cell_type": "markdown", "id": "5a44551a", "metadata": {"papermill": {"duration": 0.001376, "end_time": "2025-06-15T12:06:26.927800", "exception": false, "start_time": "2025-06-15T12:06:26.926424", "status": "completed"}, "tags": []}, "source": ["## Data Loading Functions"]}, {"cell_type": "code", "execution_count": 4, "id": "d3bc1865", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T12:06:26.930865Z", "iopub.status.busy": "2025-06-15T12:06:26.930701Z", "iopub.status.idle": "2025-06-15T12:06:26.938678Z", "shell.execute_reply": "2025-06-15T12:06:26.938368Z"}, "papermill": {"duration": 0.010281, "end_time": "2025-06-15T12:06:26.939359", "exception": false, "start_time": "2025-06-15T12:06:26.929078", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Import the standardized schema\n", "import sys\n", "sys.path.append('.')\n", "from metadata_schema import ElementMetadata, MetadataSchema, MetadataExporter, create_element_from_cad_insert\n", "\n", "def find_cad_files():\n", "    \"\"\"\n", "    Find CAD files in the input directory.\n", "    \"\"\"\n", "    if cad_file_path and Path(cad_file_path).exists():\n", "        return [Path(cad_file_path)]\n", "    \n", "    search_path = input_path if input_path.is_dir() else input_path.parent\n", "    cad_files = [\n", "        *list(search_path.glob('*.dxf')),\n", "        *list(search_path.glob('*.dwg'))\n", "    ]\n", "    \n", "    return cad_files\n", "\n", "def load_cad_file(file_path):\n", "    \"\"\"\n", "    Load CAD file using ezdxf.\n", "    \"\"\"\n", "    try:\n", "        if file_path.suffix.lower() == '.dwg':\n", "            print(f\"Found DWG file: {file_path.name}\")\n", "            print(f\"DWG files need to be converted to DXF format first.\")\n", "            \n", "            # Check if a converted DXF file exists\n", "            dxf_path = file_path.with_suffix('.dxf')\n", "            if dxf_path.exists():\n", "                print(f\"Found converted DXF file: {dxf_path.name}\")\n", "                file_path = dxf_path\n", "            else:\n", "                print(f\"No converted DXF file found at: {dxf_path}\")\n", "                print(f\"Please convert {file_path.name} to DXF format using:\")\n", "                print(f\"  - AutoCAD: SAVEAS -> DXF format\")\n", "                print(f\"  - FreeCAD: File -> Export -> DXF\")\n", "                print(f\"  - Online converter: CloudConvert, Zamzar, etc.\")\n", "                print(f\"  - Command line: ODA File Converter (free)\")\n", "                return None, None\n", "        \n", "        if file_path.suffix.lower() != '.dxf':\n", "            print(f\"Unsupported file format: {file_path.suffix}\")\n", "            print(f\"Supported formats: .dxf\")\n", "            return None, None\n", "        \n", "        doc = ezdxf.readfile(str(file_path))\n", "        modelspace = doc.modelspace()\n", "        \n", "        print(f\"Successfully loaded CAD file: {file_path.name}\")\n", "        print(f\"DXF version: {doc.dxfversion}\")\n", "        \n", "        return doc, modelspace\n", "        \n", "    except ezdxf.DXFError as e:\n", "        print(f\"Error loading DXF file: {e}\")\n", "        return None, None\n", "    except Exception as e:\n", "        print(f\"Unexpected error loading CAD file: {e}\")\n", "        return None, None"]}, {"cell_type": "markdown", "id": "151cf9ad", "metadata": {"papermill": {"duration": 0.001145, "end_time": "2025-06-15T12:06:26.941777", "exception": false, "start_time": "2025-06-15T12:06:26.940632", "status": "completed"}, "tags": []}, "source": ["## Load and Process CAD Data"]}, {"cell_type": "code", "execution_count": 5, "id": "49f0ea21", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T12:06:26.944580Z", "iopub.status.busy": "2025-06-15T12:06:26.944446Z", "iopub.status.idle": "2025-06-15T12:06:26.951764Z", "shell.execute_reply": "2025-06-15T12:06:26.951492Z"}, "papermill": {"duration": 0.009729, "end_time": "2025-06-15T12:06:26.952587", "exception": false, "start_time": "2025-06-15T12:06:26.942858", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 2 CAD file(s):\n", "  - ForumEnergy_AltheaI&II_Eng_Ampacity-PPP_031925.dwg\n", "  - ForumEnergyPartners_AltheaI&II_Eng_RPCS-PPP_082824.dwg\n", "\n", "Trying to load: ForumEnergy_AltheaI&II_Eng_Ampacity-PPP_031925.dwg\n", "Found DWG file: ForumEnergy_AltheaI&II_Eng_Ampacity-PPP_031925.dwg\n", "DWG files need to be converted to DXF format first.\n", "No converted DXF file found at: ../../data/USA/RPCS/raw/ForumEnergy_AltheaI&II_Eng_Ampacity-PPP_031925.dxf\n", "Please convert ForumEnergy_AltheaI&II_Eng_Ampacity-PPP_031925.dwg to DXF format using:\n", "  - AutoCAD: SAVEAS -> DXF format\n", "  - FreeCAD: File -> Export -> DXF\n", "  - Online converter: CloudConvert, Zamzar, etc.\n", "  - Command line: ODA File Converter (free)\n", "Failed to load ForumEnergy_AltheaI&II_Eng_Ampacity-PPP_031925.dwg, trying next file...\n", "\n", "Trying to load: ForumEnergyPartners_AltheaI&II_Eng_RPCS-PPP_082824.dwg\n", "Found DWG file: ForumEnergyPartners_AltheaI&II_Eng_RPCS-PPP_082824.dwg\n", "DWG files need to be converted to DXF format first.\n", "No converted DXF file found at: ../../data/USA/RPCS/raw/ForumEnergyPartners_AltheaI&II_Eng_RPCS-PPP_082824.dxf\n", "Please convert ForumEnergyPartners_AltheaI&II_Eng_RPCS-PPP_082824.dwg to DXF format using:\n", "  - AutoCAD: SAVEAS -> DXF format\n", "  - FreeCAD: File -> Export -> DXF\n", "  - Online converter: CloudConvert, Zamzar, etc.\n", "  - Command line: ODA File Converter (free)\n", "Failed to load ForumEnergyPartners_AltheaI&II_Eng_RPCS-PPP_082824.dwg, trying next file...\n", "\n", "No valid CAD files could be loaded.\n", "Available files: ['ForumEnergy_AltheaI&II_Eng_Ampacity-PPP_031925.dwg', 'ForumEnergyPartners_AltheaI&II_Eng_RPCS-PPP_082824.dwg']\n", "\n", "Possible solutions:\n", "1. Convert DWG files to DXF format\n", "2. Check file permissions and integrity\n", "3. Ensure files are valid CAD files\n", "\n", "Creating empty results for site: RPCS\n", "Skipping to export section with empty results...\n"]}], "source": ["# Find and load CAD files\n", "cad_files = find_cad_files()\n", "\n", "if not cad_files:\n", "    print(f\"No CAD files found in {input_path}\")\n", "    print(\"Please place your CAD files (.dxf or .dwg) in the input directory.\")\n", "    raise FileNotFoundError(f\"No CAD files found in {input_path}\")\n", "\n", "print(f\"Found {len(cad_files)} CAD file(s):\")\n", "for file_path in cad_files:\n", "    print(f\"  - {file_path.name}\")\n", "\n", "# Try to process CAD files until we find one that works\n", "doc, modelspace, cad_file_path = None, None, None\n", "\n", "for file_path in cad_files:\n", "    print(f\"\\nTrying to load: {file_path.name}\")\n", "    doc, modelspace = load_cad_file(file_path)\n", "    if doc is not None:\n", "        cad_file_path = file_path\n", "        break\n", "    else:\n", "        print(f\"Failed to load {file_path.name}, trying next file...\")\n", "\n", "if doc is None:\n", "    print(f\"\\nNo valid CAD files could be loaded.\")\n", "    print(f\"Available files: {[f.name for f in cad_files]}\")\n", "    print(f\"\\nPossible solutions:\")\n", "    print(f\"1. Convert DWG files to DXF format\")\n", "    print(f\"2. Check file permissions and integrity\")\n", "    print(f\"3. Ensure files are valid CAD files\")\n", "    \n", "    # Create empty results for export\n", "    print(f\"\\nCreating empty results for site: {site_name}\")\n", "    df = MetadataSchema.create_empty_dataframe()\n", "    elements = []\n", "    insert_entities = []\n", "    \n", "    # Set dummy values for export section\n", "    cad_file_path = cad_files[0]  # Use first file for metadata even if it failed to load\n", "    \n", "    print(f\"Skipping to export section with empty results...\")\n", "else:\n", "    print(f\"Successfully loaded CAD file: {cad_file_path.name}\")\n", "\n", "# Only proceed with analysis if we have a valid document\n", "if doc is not None:\n", "    # Display basic file information\n", "    print(f\"\\nCAD File Analysis:\")\n", "    print(f\"File: {cad_file_path.name}\")\n", "    print(f\"DXF version: {doc.dxfversion}\")\n", "\n", "    # Display available layers\n", "    layers = list(doc.layers)\n", "    print(f\"Layers ({len(layers)}): {', '.join([layer.dxf.name for layer in layers[:10]])}{'...' if len(layers) > 10 else ''}\")\n", "    \n", "    # Display available blocks\n", "    blocks = list(doc.blocks)\n", "    non_system_blocks = [block.name for block in blocks if not block.name.startswith('*')]\n", "    print(f\"Blocks ({len(non_system_blocks)}): {', '.join(non_system_blocks[:10])}{'...' if len(non_system_blocks) > 10 else ''}\")\n", "    \n", "    # Count entity types in modelspace\n", "    entity_types = {}\n", "    for entity in modelspace:\n", "        entity_type = entity.dxftype()\n", "        entity_types[entity_type] = entity_types.get(entity_type, 0) + 1\n", "    \n", "    print(f\"\\nEntity types in modelspace:\")\n", "    for entity_type, count in sorted(entity_types.items()):\n", "        print(f\"  {entity_type}: {count}\")"]}, {"cell_type": "markdown", "id": "43d95e1d", "metadata": {"papermill": {"duration": 0.001166, "end_time": "2025-06-15T12:06:26.955041", "exception": false, "start_time": "2025-06-15T12:06:26.953875", "status": "completed"}, "tags": []}, "source": ["## Extract <PERSON><PERSON><PERSON> from INSERT Entities"]}, {"cell_type": "code", "execution_count": 6, "id": "95c74939", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T12:06:26.957896Z", "iopub.status.busy": "2025-06-15T12:06:26.957748Z", "iopub.status.idle": "2025-06-15T12:06:26.962744Z", "shell.execute_reply": "2025-06-15T12:06:26.962524Z"}, "papermill": {"duration": 0.007218, "end_time": "2025-06-15T12:06:26.963428", "exception": false, "start_time": "2025-06-15T12:06:26.956210", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing 0 INSERT entities...\n", "Extracted metadata for 0 elements\n", "No elements extracted\n"]}], "source": ["# Extract metadata from INSERT entities\n", "elements = []\n", "if doc is not None and modelspace is not None:\n", "    insert_entities = list(modelspace.query(\"INSERT\"))\n", "else:\n", "    insert_entities = []\n", "\n", "print(f\"Processing {len(insert_entities)} INSERT entities...\")\n", "\n", "for entity in insert_entities:\n", "    try:\n", "        # Extract basic properties\n", "        block_name = entity.dxf.name\n", "        x, y, z = entity.dxf.insert\n", "        layer = entity.dxf.layer\n", "        \n", "        # Extract rotation and scale if available\n", "        rotation = getattr(entity.dxf, \"rotation\", 0)\n", "        scale_x = getattr(entity.dxf, \"xscale\", 1)\n", "        scale_y = getattr(entity.dxf, \"yscale\", 1)\n", "        scale_z = getattr(entity.dxf, \"zscale\", 1)\n", "        \n", "        # Use naming convention to extract element information\n", "        pile_match = re.search(pile_name_pattern, block_name)\n", "        element_name = f\"PILE:{pile_match.group(1)}\" if pile_match else block_name\n", "        element_type = \"pile\" if pile_match else \"unknown\"\n", "        \n", "        # Get entity handle (unique identifier)\n", "        handle = entity.dxf.handle\n", "        \n", "        # Extract attributes if available and requested\n", "        attributes = {}\n", "        if include_attributes and hasattr(entity, \"attribs\") and entity.attribs:\n", "            for attrib in entity.attribs:\n", "                attributes[attrib.dxf.tag] = attrib.dxf.text\n", "        \n", "        # Create standardized element metadata\n", "        element = ElementMetadata(\n", "            element_id=handle,\n", "            element_name=element_name,\n", "            element_type=element_type,\n", "            source_file=cad_file_path.name,\n", "            source_type=\"CAD\",\n", "            x_local=x,\n", "            y_local=y,\n", "            z_local=z,\n", "            rotation=rotation,\n", "            scale_x=scale_x,\n", "            scale_y=scale_y,\n", "            scale_z=scale_z,\n", "            layer_name=layer,\n", "            attributes=attributes if attributes else None,\n", "            extraction_timestamp=datetime.now().isoformat(),\n", "            coordinate_system=coordinate_system\n", "        )\n", "        \n", "        elements.append(element)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing entity {getattr(entity.dxf, 'handle', 'unknown')}: {e}\")\n", "\n", "print(f\"Extracted metadata for {len(elements)} elements\")\n", "\n", "# Convert to DataFrame\n", "if elements:\n", "    df = pd.DataFrame([element.to_dict() for element in elements])\n", "    df = MetadataSchema.standardize_dataframe(df)\n", "    \n", "    print(f\"\\nDataFrame shape: {df.shape}\")\n", "    print(f\"Element types: {df['element_type'].value_counts().to_dict()}\")\n", "    \n", "    # Display sample data\n", "    print(f\"\\nSample data:\")\n", "    display(df.head())\n", "else:\n", "    df = MetadataSchema.create_empty_dataframe()\n", "    print(\"No elements extracted\")"]}, {"cell_type": "markdown", "id": "19a0d269", "metadata": {"papermill": {"duration": 0.001213, "end_time": "2025-06-15T12:06:26.965999", "exception": false, "start_time": "2025-06-15T12:06:26.964786", "status": "completed"}, "tags": []}, "source": ["## Coordinate Transformation"]}, {"cell_type": "code", "execution_count": 7, "id": "218b8efb", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T12:06:26.968996Z", "iopub.status.busy": "2025-06-15T12:06:26.968872Z", "iopub.status.idle": "2025-06-15T12:06:26.972068Z", "shell.execute_reply": "2025-06-15T12:06:26.971866Z"}, "papermill": {"duration": 0.005454, "end_time": "2025-06-15T12:06:26.972619", "exception": false, "start_time": "2025-06-15T12:06:26.967165", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No data available for coordinate transformation\n"]}], "source": ["# Transform coordinates if requested and data is available\n", "if coordinate_transform and len(df) > 0 and COORDINATE_TRANSFORM_SUPPORT:\n", "    try:\n", "        # Create coordinate transformer\n", "        transformer = Transformer.from_crs(coordinate_system, target_crs, always_xy=True)\n", "        print(f\"Transforming coordinates: {coordinate_system} -> {target_crs}\")\n", "        \n", "        # Transform coordinates for each element\n", "        transformed_coords = []\n", "        for _, row in df.iterrows():\n", "            try:\n", "                lon, lat = transformer.transform(row['x_local'], row['y_local'])\n", "                transformed_coords.append({'longitude': lon, 'latitude': lat, 'elevation': row['z_local']})\n", "            except Exception as e:\n", "                print(f\"Error transforming coordinates for element {row['element_id']}: {e}\")\n", "                transformed_coords.append({'longitude': None, 'latitude': None, 'elevation': None})\n", "        \n", "        # Add transformed coordinates to DataFrame\n", "        transform_df = pd.DataFrame(transformed_coords)\n", "        df['longitude'] = transform_df['longitude']\n", "        df['latitude'] = transform_df['latitude']\n", "        df['elevation'] = transform_df['elevation']\n", "        \n", "        print(f\"Coordinate transformation completed for {len(df)} elements\")\n", "        \n", "        # Display coordinate bounds\n", "        if df['longitude'].notna().any():\n", "            print(f\"Coordinate bounds:\")\n", "            print(f\"  Longitude: {df['longitude'].min():.6f} to {df['longitude'].max():.6f}\")\n", "            print(f\"  Latitude: {df['latitude'].min():.6f} to {df['latitude'].max():.6f}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Coordinate transformation failed: {e}\")\n", "        print(\"Continuing without coordinate transformation\")\n", "        \n", "elif coordinate_transform and not COORDINATE_TRANSFORM_SUPPORT:\n", "    print(\"Coordinate transformation requested but pyproj not available\")\n", "    print(\"Install pyproj for coordinate transformation support\")\n", "    \n", "elif len(df) == 0:\n", "    print(\"No data available for coordinate transformation\")"]}, {"cell_type": "markdown", "id": "8937ab85", "metadata": {"papermill": {"duration": 0.001188, "end_time": "2025-06-15T12:06:26.975095", "exception": false, "start_time": "2025-06-15T12:06:26.973907", "status": "completed"}, "tags": []}, "source": ["## Export Results"]}, {"cell_type": "markdown", "id": "a81dca11", "metadata": {"tags": ["papermill-error-cell-tag"]}, "source": ["<span id=\"papermill-error-cell\" style=\"color:red; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; font-size:2em;\">Execution using papermill encountered an exception here and stopped:</span>"]}, {"cell_type": "code", "execution_count": 8, "id": "9131dbb4", "metadata": {"execution": {"iopub.execute_input": "2025-06-15T12:06:26.978273Z", "iopub.status.busy": "2025-06-15T12:06:26.978075Z", "iopub.status.idle": "2025-06-15T12:06:27.162489Z", "shell.execute_reply": "2025-06-15T12:06:27.162030Z"}, "papermill": {"duration": 0.186874, "end_time": "2025-06-15T12:06:27.163199", "exception": true, "start_time": "2025-06-15T12:06:26.976325", "status": "failed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No data to export - creating empty result files\n", "CSV exported: output_runs/RPCS_cad_20250615_173626/RPCS_cad_metadata.csv\n"]}, {"ename": "NameError", "evalue": "name 'j<PERSON>' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 76\u001b[39m\n\u001b[32m     50\u001b[39m empty_metadata = {\n\u001b[32m     51\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mextraction_parameters\u001b[39m\u001b[33m'\u001b[39m: {\n\u001b[32m     52\u001b[39m         \u001b[33m'\u001b[39m\u001b[33msite_name\u001b[39m\u001b[33m'\u001b[39m: site_name,\n\u001b[32m   (...)\u001b[39m\u001b[32m     72\u001b[39m     }\n\u001b[32m     73\u001b[39m }\n\u001b[32m     75\u001b[39m \u001b[38;5;66;03m# Export empty results\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m exported_files = \u001b[43mexporter\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexport_all_formats\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mempty_metadata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     78\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mEmpty result files created:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     79\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m format_type, file_path \u001b[38;5;129;01min\u001b[39;00m exported_files.items():\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/GitHub/asbuilt-foundation-analysis/notebooks/preprocessing/metadata_schema.py:231\u001b[39m, in \u001b[36mMetadataExporter.export_all_formats\u001b[39m\u001b[34m(self, df, metadata)\u001b[39m\n\u001b[32m    208\u001b[39m \u001b[38;5;66;03m# JSON export\u001b[39;00m\n\u001b[32m    209\u001b[39m json_metadata = {\n\u001b[32m    210\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mextraction_info\u001b[39m\u001b[33m'\u001b[39m: {\n\u001b[32m    211\u001b[39m         \u001b[33m'\u001b[39m\u001b[33msite_name\u001b[39m\u001b[33m'\u001b[39m: \u001b[38;5;28mself\u001b[39m.site_name,\n\u001b[32m   (...)\u001b[39m\u001b[32m    228\u001b[39m     }\n\u001b[32m    229\u001b[39m }\n\u001b[32m--> \u001b[39m\u001b[32m231\u001b[39m json_path = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mexport_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43mjson_metadata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    232\u001b[39m exported_files[\u001b[33m'\u001b[39m\u001b[33mjson\u001b[39m\u001b[33m'\u001b[39m] = json_path\n\u001b[32m    234\u001b[39m \u001b[38;5;66;03m# Parquet export (if available)\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/GitHub/asbuilt-foundation-analysis/notebooks/preprocessing/metadata_schema.py:175\u001b[39m, in \u001b[36mMetadataExporter.export_json\u001b[39m\u001b[34m(self, data, filename)\u001b[39m\n\u001b[32m    173\u001b[39m json_path = \u001b[38;5;28mself\u001b[39m.output_path / filename\n\u001b[32m    174\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(json_path, \u001b[33m'\u001b[39m\u001b[33mw\u001b[39m\u001b[33m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[32m--> \u001b[39m\u001b[32m175\u001b[39m     \u001b[43mjson\u001b[49m.dump(data, f, indent=\u001b[32m2\u001b[39m, default=\u001b[38;5;28mstr\u001b[39m)\n\u001b[32m    176\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mJSON exported: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mjson_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m    177\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mstr\u001b[39m(json_path)\n", "\u001b[31mNameError\u001b[39m: name 'json' is not defined"]}], "source": ["# Export results in multiple formats\n", "if len(df) > 0:\n", "    # Create exporter\n", "    exporter = MetadataExporter(current_run_path, site_name, \"CAD\")\n", "    \n", "    # Prepare metadata for JSON export\n", "    extraction_metadata = {\n", "        'extraction_parameters': {\n", "            'site_name': site_name,\n", "            'project_type': project_type,\n", "            'coordinate_system': coordinate_system,\n", "            'target_crs': target_crs,\n", "            'pile_name_pattern': pile_name_pattern,\n", "            'include_attributes': include_attributes,\n", "            'include_geometry': include_geometry,\n", "            'coordinate_transform': coordinate_transform\n", "        },\n", "        'source_file_info': {\n", "            'filename': cad_file_path.name if cad_file_path else 'unknown',\n", "            'file_size': cad_file_path.stat().st_size if cad_file_path and cad_file_path.exists() else 0,\n", "            'dxf_version': doc.dxfversion if doc else 'unknown',\n", "            'load_success': doc is not None\n", "        },\n", "        'processing_info': {\n", "            'total_insert_entities': len(insert_entities),\n", "            'extracted_elements': len(df),\n", "            'extraction_timestamp': timestamp\n", "        }\n", "    }\n", "    \n", "    # Export in all requested formats\n", "    exported_files = exporter.export_all_formats(df, extraction_metadata)\n", "    \n", "    print(f\"\\nExport completed:\")\n", "    for format_type, file_path in exported_files.items():\n", "        print(f\"  {format_type.upper()}: {file_path}\")\n", "    \n", "    # Also save to main preprocessing directory\n", "    main_csv_path = preprocessing_path / f\"{site_name}_cad_metadata.csv\"\n", "    df.to_csv(main_csv_path, index=False)\n", "    print(f\"  Main CSV: {main_csv_path}\")\n", "    \n", "else:\n", "    print(\"No data to export - creating empty result files\")\n", "    \n", "    # Create exporter for empty results\n", "    exporter = MetadataExporter(current_run_path, site_name, \"CAD\")\n", "    \n", "    # Create empty metadata\n", "    empty_metadata = {\n", "        'extraction_parameters': {\n", "            'site_name': site_name,\n", "            'project_type': project_type,\n", "            'coordinate_system': coordinate_system,\n", "            'target_crs': target_crs,\n", "            'pile_name_pattern': pile_name_pattern,\n", "            'include_attributes': include_attributes,\n", "            'include_geometry': include_geometry,\n", "            'coordinate_transform': coordinate_transform\n", "        },\n", "        'source_file_info': {\n", "            'filename': cad_file_path.name if cad_file_path else 'unknown',\n", "            'file_size': 0,\n", "            'dxf_version': 'unknown',\n", "            'load_success': <PERSON><PERSON><PERSON>,\n", "            'error_reason': 'DWG file requires conversion to DXF format'\n", "        },\n", "        'processing_info': {\n", "            'total_insert_entities': 0,\n", "            'extracted_elements': 0,\n", "            'extraction_timestamp': timestamp\n", "        }\n", "    }\n", "    \n", "    # Export empty results\n", "    exported_files = exporter.export_all_formats(df, empty_metadata)\n", "    \n", "    print(f\"Empty result files created:\")\n", "    for format_type, file_path in exported_files.items():\n", "        print(f\"  {format_type.upper()}: {file_path}\")\n", "\n", "print(f\"\\nCAD metadata extraction completed for {site_name}\")\n", "print(f\"Results saved to: {current_run_path}\")\n", "if doc is None:\n", "    print(f\"Note: No valid CAD files could be processed. Please convert DWG files to DXF format.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "papermill": {"default_parameters": {}, "duration": 2.117778, "end_time": "2025-06-15T12:06:27.482080", "environment_variables": {}, "exception": true, "input_path": "metadata_extraction_cad.ipynb", "output_path": "output_runs/RPCS_cad_20250615_173625_executed.ipynb", "parameters": {"coordinate_system": "EPSG:32614", "coordinate_transform": true, "export_csv": true, "export_json": true, "export_parquet": true, "include_attributes": true, "include_geometry": true, "pile_name_pattern": "PILE_(\\d+)", "project_type": "USA", "site_name": "RPCS", "target_crs": "EPSG:4326"}, "start_time": "2025-06-15T12:06:25.364302", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}