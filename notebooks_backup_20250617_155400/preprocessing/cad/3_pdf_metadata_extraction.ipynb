{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD PDF Metadata Extraction\n", "\n", "This notebook extracts metadata from CAD PDF files including document properties, creation dates, authors, and technical specifications.\n", "\n", "**Stage**: Preprocessing - CAD Pipeline  \n", "**Input Data**: PDF files discovered from previous stages  \n", "**Output**: Structured metadata including document properties and technical information  \n", "**Format**: pandas DataFrame and JSON structures  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Extract comprehensive metadata from CAD PDF files to support:\n", "- Document provenance and version tracking\n", "- Technical specification identification\n", "- Quality assessment and validation\n", "- Automated categorization and indexing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Extract standard PDF metadata (title, author, creation date, etc.)\n", "2. Analyze document structure and page properties\n", "3. Extract CAD-specific information from text content\n", "4. Identify drawing numbers, revision information, and scales\n", "5. Generate structured metadata records\n", "6. Validate and clean extracted metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pdfplumber library loaded successfully\n", "PyMuPDF library loaded successfully\n", "CAD PDF Metadata Extraction - Starting...\n", "Timestamp: 2025-06-17 13:22:07\n"]}], "source": ["import logging\n", "import re\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import defaultdict\n", "from typing import Dict, List, Optional, Any\n", "\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF processing libraries\n", "try:\n", "    import pdfplumber\n", "    PDFPLUMBER_AVAILABLE = True\n", "    print(\"pdfplumber library loaded successfully\")\n", "except ImportError:\n", "    PDFPLUMBER_AVAILABLE = False\n", "    print(\"Warning: pdfplumber not available\")\n", "\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "    print(\"PyMuPDF library loaded successfully\")\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"Warning: PyMuPDF not available\")\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"CAD PDF Metadata Extraction - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:07,499 - INFO - Loaded 33 PDF files from directory scan\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 33 PDF files for metadata extraction\n", "Text extraction results available for 0 files\n"]}], "source": ["# Check for required libraries\n", "if not PDFPLUMBER_AVAILABLE and not PYMUPDF_AVAILABLE:\n", "    raise ImportError(\"Neither pdfplumber nor PyMuPDF is available. Please install at least one: pip install pdfplumber pymupdf\")\n", "\n", "# Load discovered PDF files from previous notebooks\n", "try:\n", "    # Try to use results from previous notebooks\n", "    pdf_files = globals().get('discovered_pdf_files', [])\n", "    extraction_results = globals().get('pdf_extraction_results', {})\n", "    pdf_classifications = globals().get('pdf_classifications', {})\n", "    \n", "    if not pdf_files:\n", "        # Fallback: load from data directory\n", "        project_root = Path('../../../')\n", "        cad_data_path = project_root / 'data' / 'cad'\n", "        pdf_files = [str(p) for p in cad_data_path.rglob('*.pdf')]\n", "        logger.info(f\"Loaded {len(pdf_files)} PDF files from directory scan\")\n", "    else:\n", "        logger.info(f\"Using {len(pdf_files)} PDF files from previous notebooks\")\n", "        \n", "except Exception as e:\n", "    logger.error(f\"Error loading PDF file list: {e}\")\n", "    pdf_files = []\n", "    extraction_results = {}\n", "\n", "print(f\"Processing {len(pdf_files)} PDF files for metadata extraction\")\n", "print(f\"Text extraction results available for {len(extraction_results)} files\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata extraction utilities defined\n"]}], "source": ["# Metadata extraction utilities\n", "def extract_pdf_metadata_pdfplumber(pdf_path: str) -> Dict[str, Any]:\n", "    \"\"\"Extract metadata using pdfplumber.\"\"\"\n", "    metadata = {}\n", "    \n", "    try:\n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            # Basic document metadata\n", "            metadata.update({\n", "                'page_count': len(pdf.pages),\n", "                'pdf_metadata': pdf.metadata or {},\n", "                'extraction_method': 'pdfplumber'\n", "            })\n", "            \n", "            # Page-level information\n", "            if pdf.pages:\n", "                first_page = pdf.pages[0]\n", "                metadata.update({\n", "                    'page_width': first_page.width,\n", "                    'page_height': first_page.height,\n", "                    'page_rotation': getattr(first_page, 'rotation', 0)\n", "                })\n", "                \n", "    except Exception as e:\n", "        logger.error(f\"Error extracting metadata with pdfplumber from {pdf_path}: {e}\")\n", "        raise\n", "    \n", "    return metadata\n", "\n", "def extract_pdf_metadata_pymupdf(pdf_path: str) -> Dict[str, Any]:\n", "    \"\"\"Extract metadata using PyMuPDF.\"\"\"\n", "    metadata = {}\n", "    \n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        \n", "        # Basic document metadata\n", "        metadata.update({\n", "            'page_count': len(doc),\n", "            'pdf_metadata': doc.metadata,\n", "            'extraction_method': 'pymupdf'\n", "        })\n", "        \n", "        # Page-level information\n", "        if len(doc) > 0:\n", "            first_page = doc.load_page(0)\n", "            rect = first_page.rect\n", "            metadata.update({\n", "                'page_width': rect.width,\n", "                'page_height': rect.height,\n", "                'page_rotation': first_page.rotation\n", "            })\n", "        \n", "        doc.close()\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error extracting metadata with PyMuPDF from {pdf_path}: {e}\")\n", "        raise\n", "    \n", "    return metadata\n", "\n", "print(\"Metadata extraction utilities defined\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CAD-specific metadata extraction patterns defined\n"]}], "source": ["# CAD-specific metadata extraction patterns\n", "CAD_PATTERNS = {\n", "    'drawing_number': [\n", "        r'(?i)drawing\\s*(?:no\\.?|number)\\s*:?\\s*([A-Z0-9\\-\\.]+)',\n", "        r'(?i)dwg\\s*(?:no\\.?|#)\\s*:?\\s*([A-Z0-9\\-\\.]+)',\n", "        r'([A-Z]{2,}\\.[A-Z]{2,}\\.[A-Z]\\.[0-9]{2}\\.[A-Z]{2}\\.[A-Z]\\.[0-9]{5}\\.[0-9]{2}\\.[0-9]{3}\\.[0-9]{2})',  # ENEL pattern\n", "        r'([A-Z]\\-[0-9]{3})',  # Simple pattern like S-203\n", "    ],\n", "    'revision': [\n", "        r'(?i)rev\\.?\\s*:?\\s*([A-Z0-9]+)',\n", "        r'(?i)revision\\s*:?\\s*([A-Z0-9]+)',\n", "    ],\n", "    'scale': [\n", "        r'(?i)scale\\s*:?\\s*(1:[0-9,]+)',\n", "        r'(?i)scale\\s*:?\\s*([0-9]+\\s*=\\s*[0-9]+)',\n", "    ],\n", "    'date': [\n", "        r'(?i)date\\s*:?\\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',\n", "        r'([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',\n", "    ],\n", "    'project_name': [\n", "        r'(?i)project\\s*:?\\s*([A-Za-z0-9\\s\\-\\.]+?)(?:\\n|$)',\n", "    ],\n", "    'sheet_title': [\n", "        r'(?i)(?:sheet\\s*title|title)\\s*:?\\s*([A-Za-z0-9\\s\\-\\.]+?)(?:\\n|$)',\n", "    ]\n", "}\n", "\n", "def extract_cad_metadata_from_text(text: str) -> Dict[str, Any]:\n", "    \"\"\"Extract CAD-specific metadata from text content.\"\"\"\n", "    cad_metadata = {}\n", "    \n", "    if not text:\n", "        return cad_metadata\n", "    \n", "    # Apply each pattern category\n", "    for category, patterns in CAD_PATTERNS.items():\n", "        matches = []\n", "        for pattern in patterns:\n", "            found = re.findall(pattern, text)\n", "            matches.extend(found)\n", "        \n", "        if matches:\n", "            # Take the first match or most common if multiple\n", "            if len(matches) == 1:\n", "                cad_metadata[category] = matches[0].strip()\n", "            else:\n", "                # Use most common match\n", "                from collections import Counter\n", "                most_common = Counter(matches).most_common(1)\n", "                cad_metadata[category] = most_common[0][0].strip() if most_common else matches[0].strip()\n", "                cad_metadata[f'{category}_alternatives'] = list(set(matches))\n", "    \n", "    return cad_metadata\n", "\n", "print(\"CAD-specific metadata extraction patterns defined\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:07,519 - INFO - Beginning metadata extraction from 33 PDF files\n", "2025-06-17 13:22:07,520 - INFO - Processing metadata for file 1: ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf\n", "2025-06-17 13:22:07,526 - INFO - Processing metadata for file 2: Forum_AltheaI&II_CA_NXH_ENG_L3_03-11-2025_Rev 4.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Starting metadata extraction...\n", "\n", "Processing 1/33: ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 2/33: Forum_AltheaI&II_CA_NXH_ENG_L3_03-11-2025_Rev 4.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:07,722 - INFO - Processing metadata for file 3: GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf\n", "2025-06-17 13:22:07,742 - INFO - Processing metadata for file 4: GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.pdf\n", "2025-06-17 13:22:07,882 - INFO - Processing metadata for file 5: GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.pdf\n", "2025-06-17 13:22:07,891 - INFO - Processing metadata for file 6: GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 3/33: GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 4/33: GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 5/33: GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 6/33: GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:08,005 - INFO - Processing metadata for file 7: GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.pdf\n", "2025-06-17 13:22:08,008 - INFO - Processing metadata for file 8: GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf\n", "2025-06-17 13:22:08,016 - INFO - Processing metadata for file 9: GRE.EEC.D.00.IT.P.12645.00.134.00.pdf\n", "2025-06-17 13:22:08,022 - INFO - Processing metadata for file 10: Plinto Area - Alzamento a 700mm.pdf\n", "2025-06-17 13:22:08,025 - INFO - Processing metadata for file 11: GRE.EEC.D.00.IT.P.12645.00.327.00.pdf\n", "2025-06-17 13:22:08,032 - INFO - Processing metadata for file 12: Plinto - Alzamento a 700mm.pdf\n", "2025-06-17 13:22:08,034 - INFO - Processing metadata for file 13: GRE.EEC.D.00.IT.P.12645.00.322.00.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 7/33: GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 8/33: GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 9/33: GRE.EEC.D.00.IT.P.12645.00.134.00.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 10/33: Plinto Area - Alzamento a 700mm.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 11/33: GRE.EEC.D.00.IT.P.12645.00.327.00.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 12/33: Plinto - Alzamento a 700mm.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 13/33: GRE.EEC.D.00.IT.P.12645.00.322.00.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:08,315 - INFO - Processing metadata for file 14: GRE.EEC.R.00.IT.P.12645.00.133.00.pdf\n", "2025-06-17 13:22:08,364 - INFO - Processing metadata for file 15: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.pdf\n", "2025-06-17 13:22:08,371 - INFO - Processing metadata for file 16: GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.pdf\n", "2025-06-17 13:22:08,374 - INFO - Processing metadata for file 17: GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.pdf\n", "2025-06-17 13:22:08,378 - INFO - Processing metadata for file 18: GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.pdf\n", "2025-06-17 13:22:08,438 - INFO - Processing metadata for file 19: GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.pdf\n", "2025-06-17 13:22:08,496 - INFO - Processing metadata for file 20: GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.pdf\n", "2025-06-17 13:22:08,498 - INFO - Processing metadata for file 21: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 14/33: GRE.EEC.R.00.IT.P.12645.00.133.00.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 15/33: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 16/33: GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 17/33: GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 18/33: GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 19/33: GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 20/33: GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 21/33: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:08,545 - INFO - Processing metadata for file 22: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Post.pdf\n", "2025-06-17 13:22:08,548 - INFO - Processing metadata for file 23: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Ante.pdf\n", "2025-06-17 13:22:08,625 - INFO - Processing metadata for file 24: GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 22/33: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Post.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 23/33: GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Ante.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 24/33: GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:09,829 - INFO - Processing metadata for file 25: GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.pdf\n", "2025-06-17 13:22:09,836 - INFO - Processing metadata for file 26: GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.pdf\n", "2025-06-17 13:22:09,847 - INFO - Processing metadata for file 27: GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.pdf\n", "2025-06-17 13:22:09,867 - INFO - Processing metadata for file 28: GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.pdf\n", "2025-06-17 13:22:09,873 - INFO - Processing metadata for file 29: PB01_-_POWERBLOCK_PLAN.pdf\n", "2025-06-17 13:22:09,878 - INFO - Processing metadata for file 30: S-501_ FSLR S6+ Pier Tolerances  Rev.2 markup (1).pdf\n", "2025-06-17 13:22:09,936 - INFO - Processing metadata for file 31: PB03_-_POWERBLOCK_PLAN.pdf\n", "2025-06-17 13:22:09,940 - INFO - Processing metadata for file 32: S-203_ PIER PLAN BLOCK--3 Rev.2.pdf\n", "2025-06-17 13:22:09,992 - INFO - Processing metadata for file 33: PB02_-_POWERBLOCK_PLAN.pdf\n", "2025-06-17 13:22:09,997 - INFO - Metadata extraction completed. Success: 33, Failed: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 25/33: GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 26/33: GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 27/33: GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 28/33: GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 29/33: PB01_-_POWERBLOCK_PLAN.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 30/33: S-501_ FSLR S6+ Pier Tolerances  Rev.2 markup (1).pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 31/33: PB03_-_POWERBLOCK_PLAN.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 32/33: S-203_ PIER PLAN BLOCK--3 Rev.2.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n", "\n", "Processing 33/33: PB02_-_POWERBLOCK_PLAN.pdf\n", "  Success: 0 CAD fields extracted (pdfplumber)\n"]}], "source": ["# Initialize containers for metadata results\n", "metadata_results = {}\n", "metadata_errors = []\n", "metadata_stats = {\n", "    'total_files': len(pdf_files),\n", "    'successful_extractions': 0,\n", "    'failed_extractions': 0,\n", "    'files_with_cad_metadata': 0,\n", "    'files_with_drawing_numbers': 0,\n", "    'files_with_revisions': 0\n", "}\n", "\n", "print(\"Starting metadata extraction...\")\n", "logger.info(f\"Beginning metadata extraction from {len(pdf_files)} PDF files\")\n", "\n", "# Process each PDF file\n", "for i, pdf_path in enumerate(pdf_files, 1):\n", "    pdf_path_obj = Path(pdf_path)\n", "    file_name = pdf_path_obj.name\n", "    \n", "    print(f\"\\nProcessing {i}/{len(pdf_files)}: {file_name}\")\n", "    logger.info(f\"Processing metadata for file {i}: {file_name}\")\n", "    \n", "    try:\n", "        # Extract basic PDF metadata\n", "        if PDFPLUMBER_AVAILABLE:\n", "            try:\n", "                pdf_metadata = extract_pdf_metadata_pdfplumber(pdf_path)\n", "                extraction_method = \"pdfplumber\"\n", "            except Exception as e:\n", "                logger.warning(f\"pdfplumber failed for {file_name}: {e}\")\n", "                if PYMUPDF_AVAILABLE:\n", "                    pdf_metadata = extract_pdf_metadata_pymupdf(pdf_path)\n", "                    extraction_method = \"pymupdf\"\n", "                else:\n", "                    raise e\n", "        else:\n", "            pdf_metadata = extract_pdf_metadata_pymupdf(pdf_path)\n", "            extraction_method = \"pymupdf\"\n", "        \n", "        # Extract CAD-specific metadata from text if available\n", "        cad_metadata = {}\n", "        if file_name in extraction_results and extraction_results[file_name].get('text'):\n", "            text_content = extraction_results[file_name]['text']\n", "            cad_metadata = extract_cad_metadata_from_text(text_content)\n", "        \n", "        # Combine all metadata\n", "        combined_metadata = {\n", "            'file_path': pdf_path,\n", "            'file_name': file_name,\n", "            'file_size_bytes': pdf_path_obj.stat().st_size,\n", "            'extraction_timestamp': datetime.now().isoformat(),\n", "            'extraction_method': extraction_method,\n", "            **pdf_metadata,\n", "            'cad_metadata': cad_metadata\n", "        }\n", "        \n", "        # Store results\n", "        metadata_results[file_name] = combined_metadata\n", "        \n", "        # Update statistics\n", "        metadata_stats['successful_extractions'] += 1\n", "        if cad_metadata:\n", "            metadata_stats['files_with_cad_metadata'] += 1\n", "        if cad_metadata.get('drawing_number'):\n", "            metadata_stats['files_with_drawing_numbers'] += 1\n", "        if cad_metadata.get('revision'):\n", "            metadata_stats['files_with_revisions'] += 1\n", "        \n", "        print(f\"  Success: {len(cad_metadata)} CAD fields extracted ({extraction_method})\")\n", "        \n", "    except Exception as e:\n", "        error_msg = str(e)\n", "        metadata_errors.append((file_name, error_msg))\n", "        metadata_stats['failed_extractions'] += 1\n", "        \n", "        logger.error(f\"Failed to extract metadata from {file_name}: {error_msg}\")\n", "        print(f\"  Error: {error_msg}\")\n", "\n", "logger.info(f\"Metadata extraction completed. Success: {metadata_stats['successful_extractions']}, Failed: {metadata_stats['failed_extractions']}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "PDF METADATA EXTRACTION SUMMARY\n", "============================================================\n", "\n", "Total files processed: 33\n", "Successful extractions: 33\n", "Failed extractions: 0\n", "Success rate: 100.0%\n", "\n", "CAD-specific metadata:\n", "Files with CAD metadata: 0\n", "Files with drawing numbers: 0\n", "Files with revision info: 0\n", "\n", "============================================================\n"]}], "source": ["# Display metadata extraction summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF METADATA EXTRACTION SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nTotal files processed: {metadata_stats['total_files']}\")\n", "print(f\"Successful extractions: {metadata_stats['successful_extractions']}\")\n", "print(f\"Failed extractions: {metadata_stats['failed_extractions']}\")\n", "print(f\"Success rate: {metadata_stats['successful_extractions']/metadata_stats['total_files']*100:.1f}%\")\n", "\n", "print(f\"\\nCAD-specific metadata:\")\n", "print(f\"Files with CAD metadata: {metadata_stats['files_with_cad_metadata']}\")\n", "print(f\"Files with drawing numbers: {metadata_stats['files_with_drawing_numbers']}\")\n", "print(f\"Files with revision info: {metadata_stats['files_with_revisions']}\")\n", "\n", "if metadata_errors:\n", "    print(\"\\nFiles with extraction errors:\")\n", "    for file_name, error_msg in metadata_errors:\n", "        print(f\"  {file_name}: {error_msg}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "CREATING STRUCTURED METADATA DATAFRAME...\n", "Created metadata DataFrame with 33 rows and 22 columns\n", "Columns: ['file_name', 'file_path', 'file_size_mb', 'page_count', 'page_width', 'page_height', 'page_rotation', 'extraction_method', 'extraction_timestamp', 'pdf_title', 'pdf_author', 'pdf_subject', 'pdf_creator', 'pdf_producer', 'pdf_creation_date', 'pdf_modification_date', 'drawing_number', 'revision', 'scale', 'cad_date', 'project_name', 'sheet_title']\n"]}], "source": ["# Create structured metadata DataFrame\n", "print(\"\\nCREATING STRUCTURED METADATA DATAFRAME...\")\n", "\n", "metadata_records = []\n", "for file_name, metadata in metadata_results.items():\n", "    # Flatten the metadata structure\n", "    record = {\n", "        'file_name': file_name,\n", "        'file_path': metadata['file_path'],\n", "        'file_size_mb': metadata['file_size_bytes'] / (1024 * 1024),\n", "        'page_count': metadata.get('page_count', 0),\n", "        'page_width': metadata.get('page_width', 0),\n", "        'page_height': metadata.get('page_height', 0),\n", "        'page_rotation': metadata.get('page_rotation', 0),\n", "        'extraction_method': metadata['extraction_method'],\n", "        'extraction_timestamp': metadata['extraction_timestamp']\n", "    }\n", "    \n", "    # Add PDF metadata fields\n", "    pdf_meta = metadata.get('pdf_metadata', {})\n", "    if pdf_meta:\n", "        record.update({\n", "            'pdf_title': pdf_meta.get('title', ''),\n", "            'pdf_author': pdf_meta.get('author', ''),\n", "            'pdf_subject': pdf_meta.get('subject', ''),\n", "            'pdf_creator': pdf_meta.get('creator', ''),\n", "            'pdf_producer': pdf_meta.get('producer', ''),\n", "            'pdf_creation_date': pdf_meta.get('creationDate', ''),\n", "            'pdf_modification_date': pdf_meta.get('modDate', '')\n", "        })\n", "    \n", "    # Add CAD-specific metadata\n", "    cad_meta = metadata.get('cad_metadata', {})\n", "    record.update({\n", "        'drawing_number': cad_meta.get('drawing_number', ''),\n", "        'revision': cad_meta.get('revision', ''),\n", "        'scale': cad_meta.get('scale', ''),\n", "        'cad_date': cad_meta.get('date', ''),\n", "        'project_name': cad_meta.get('project_name', ''),\n", "        'sheet_title': cad_meta.get('sheet_title', '')\n", "    })\n", "    \n", "    metadata_records.append(record)\n", "\n", "# Create DataFrame\n", "metadata_df = pd.DataFrame(metadata_records)\n", "\n", "print(f\"Created metadata DataFrame with {len(metadata_df)} rows and {len(metadata_df.columns)} columns\")\n", "print(f\"Columns: {list(metadata_df.columns)}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "METADATA PREVIEW:\n", "----------------------------------------\n", "\n", "DataFrame shape: (33, 22)\n", "\n", "File size statistics (MB):\n", "count    33.000000\n", "mean      7.801598\n", "std      15.720461\n", "min       0.098599\n", "25%       1.493559\n", "50%       3.195295\n", "75%       6.323387\n", "max      86.042836\n", "Name: file_size_mb, dtype: float64\n", "\n", "Page count statistics:\n", "count    33.000000\n", "mean      7.636364\n", "std      11.728220\n", "min       1.000000\n", "25%       1.000000\n", "50%       2.000000\n", "75%       9.000000\n", "max      48.000000\n", "Name: page_count, dtype: float64\n", "\n", "Sample metadata records:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "      <th>file_path</th>\n", "      <th>file_size_mb</th>\n", "      <th>page_count</th>\n", "      <th>page_width</th>\n", "      <th>page_height</th>\n", "      <th>page_rotation</th>\n", "      <th>extraction_method</th>\n", "      <th>extraction_timestamp</th>\n", "      <th>pdf_title</th>\n", "      <th>...</th>\n", "      <th>pdf_creator</th>\n", "      <th>pdf_producer</th>\n", "      <th>pdf_creation_date</th>\n", "      <th>pdf_modification_date</th>\n", "      <th>drawing_number</th>\n", "      <th>revision</th>\n", "      <th>scale</th>\n", "      <th>cad_date</th>\n", "      <th>project_name</th>\n", "      <th>sheet_title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ForumEnergyPartners_Althea1&amp;2_Eng_RPCS-FHM_081...</td>\n", "      <td>../../../data/cad/rpcs/ForumEnergyPartners_Alt...</td>\n", "      <td>1.493559</td>\n", "      <td>1</td>\n", "      <td>2448.0</td>\n", "      <td>1584.0</td>\n", "      <td>0</td>\n", "      <td>pdfplumber</td>\n", "      <td>2025-06-17T13:22:07.526584</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Forum_AltheaI&amp;II_CA_NXH_ENG_L3_03-11-2025_Rev ...</td>\n", "      <td>../../../data/cad/rpcs/Forum_AltheaI&amp;II_CA_NXH...</td>\n", "      <td>12.893034</td>\n", "      <td>21</td>\n", "      <td>2592.0</td>\n", "      <td>1728.0</td>\n", "      <td>0</td>\n", "      <td>pdfplumber</td>\n", "      <td>2025-06-17T13:22:07.722832</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadrament...</td>\n", "      <td>../../../data/cad/castro/GRE.EEC.D.00.IT.P.126...</td>\n", "      <td>15.359516</td>\n", "      <td>4</td>\n", "      <td>2384.0</td>\n", "      <td>1684.0</td>\n", "      <td>270</td>\n", "      <td>pdfplumber</td>\n", "      <td>2025-06-17T13:22:07.742585</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                                           file_name  \\\n", "0  ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081...   \n", "1  Forum_AltheaI&II_CA_NXH_ENG_L3_03-11-2025_Rev ...   \n", "2  GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadrament...   \n", "\n", "                                           file_path  file_size_mb  \\\n", "0  ../../../data/cad/rpcs/ForumEnergyPartners_Alt...      1.493559   \n", "1  ../../../data/cad/rpcs/Forum_AltheaI&II_CA_NXH...     12.893034   \n", "2  ../../../data/cad/castro/GRE.EEC.D.00.IT.P.126...     15.359516   \n", "\n", "   page_count  page_width  page_height  page_rotation extraction_method  \\\n", "0           1      2448.0       1584.0              0        pdfplumber   \n", "1          21      2592.0       1728.0              0        pdfplumber   \n", "2           4      2384.0       1684.0            270        pdfplumber   \n", "\n", "         extraction_timestamp pdf_title  ... pdf_creator pdf_producer  \\\n", "0  2025-06-17T13:22:07.526584            ...                            \n", "1  2025-06-17T13:22:07.722832            ...                            \n", "2  2025-06-17T13:22:07.742585            ...                            \n", "\n", "  pdf_creation_date pdf_modification_date drawing_number revision scale  \\\n", "0                                                                         \n", "1                                                                         \n", "2                                                                         \n", "\n", "  cad_date project_name sheet_title  \n", "0                                    \n", "1                                    \n", "2                                    \n", "\n", "[3 rows x 22 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Display metadata preview\n", "print(\"\\nMETADATA PREVIEW:\")\n", "print(\"-\" * 40)\n", "\n", "if not metadata_df.empty:\n", "    # Show basic statistics\n", "    print(f\"\\nDataFrame shape: {metadata_df.shape}\")\n", "    print(f\"\\nFile size statistics (MB):\")\n", "    print(metadata_df['file_size_mb'].describe())\n", "    \n", "    print(f\"\\nPage count statistics:\")\n", "    print(metadata_df['page_count'].describe())\n", "    \n", "    # Show files with drawing numbers\n", "    files_with_drawings = metadata_df[metadata_df['drawing_number'] != '']\n", "    if not files_with_drawings.empty:\n", "        print(f\"\\nFiles with drawing numbers ({len(files_with_drawings)}):\")\n", "        for _, row in files_with_drawings.head(5).iterrows():\n", "            print(f\"  {row['file_name']}: {row['drawing_number']}\")\n", "        if len(files_with_drawings) > 5:\n", "            print(f\"  ... and {len(files_with_drawings) - 5} more\")\n", "    \n", "    # Show sample of the DataFrame\n", "    print(f\"\\nSample metadata records:\")\n", "    display(metadata_df.head(3))\n", "    \n", "else:\n", "    print(\"No metadata records were created.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:10,042 - INFO - PDF metadata extraction results exported successfully\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "EXPORTING METADATA RESULTS...\n", "Metadata DataFrame saved to: ../../../data/cad/cad_pdf_metadata.csv\n", "Detailed metadata saved to: ../../../data/cad/cad_pdf_metadata_detailed.json\n", "\n", "Results exported to variables:\n", "  - pdf_metadata_results: 33 file results\n", "  - pdf_metadata_dataframe: DataFrame with 33 rows\n", "  - pdf_metadata_stats: Summary statistics\n"]}], "source": ["# Export results for downstream processing\n", "print(\"\\nEXPORTING METADATA RESULTS...\")\n", "\n", "# Store results in global variables for use by subsequent notebooks\n", "globals()['pdf_metadata_results'] = metadata_results\n", "globals()['pdf_metadata_stats'] = metadata_stats\n", "globals()['pdf_metadata_errors'] = metadata_errors\n", "globals()['pdf_metadata_dataframe'] = metadata_df\n", "\n", "# Save to CSV for external use\n", "output_dir = Path('../../../data/cad')\n", "csv_path = output_dir / 'cad_pdf_metadata.csv'\n", "try:\n", "    metadata_df.to_csv(csv_path, index=False)\n", "    print(f\"Metadata DataFrame saved to: {csv_path}\")\n", "except Exception as e:\n", "    logger.warning(f\"Could not save CSV file: {e}\")\n", "\n", "# Save detailed metadata as JSON\n", "json_path = output_dir / 'cad_pdf_metadata_detailed.json'\n", "try:\n", "    with open(json_path, 'w') as f:\n", "        json.dump(metadata_results, f, indent=2, default=str)\n", "    print(f\"Detailed metadata saved to: {json_path}\")\n", "except Exception as e:\n", "    logger.warning(f\"Could not save JSON file: {e}\")\n", "\n", "print(f\"\\nResults exported to variables:\")\n", "print(f\"  - pdf_metadata_results: {len(metadata_results)} file results\")\n", "print(f\"  - pdf_metadata_dataframe: DataFrame with {len(metadata_df)} rows\")\n", "print(f\"  - pdf_metadata_stats: Summary statistics\")\n", "\n", "logger.info(\"PDF metadata extraction results exported successfully\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-17 13:22:10,047 - INFO - PDF metadata extraction notebook execution completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "PDF METADATA EXTRACTION COMPLETED\n", "============================================================\n", "Successfully processed 33 PDF files\n", "Extracted metadata for 33 files\n", "Found 0 files with drawing numbers\n", "Found 0 files with revision information\n", "Ready for next stage of CAD PDF processing pipeline\n", "\n", "Completion time: 2025-06-17 13:22:10\n"]}], "source": ["# Final status report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PDF METADATA EXTRACTION COMPLETED\")\n", "print(\"=\"*60)\n", "\n", "if metadata_stats['successful_extractions'] > 0:\n", "    print(f\"Successfully processed {metadata_stats['successful_extractions']} PDF files\")\n", "    print(f\"Extracted metadata for {len(metadata_df)} files\")\n", "    print(f\"Found {metadata_stats['files_with_drawing_numbers']} files with drawing numbers\")\n", "    print(f\"Found {metadata_stats['files_with_revisions']} files with revision information\")\n", "    print(\"Ready for next stage of CAD PDF processing pipeline\")\n", "else:\n", "    print(\"No PDF files were successfully processed\")\n", "    print(\"Please check file accessibility and library installations\")\n", "\n", "print(f\"\\nCompletion time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "logger.info(\"PDF metadata extraction notebook execution completed\")"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}