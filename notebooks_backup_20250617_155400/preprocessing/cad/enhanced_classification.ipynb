{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhanced Multi-Site PDF Classification System\n", "\n", "This notebook demonstrates an enhanced classification system that can handle different naming conventions across multiple sites while maintaining accuracy and flexibility.\n", "\n", "**Features**:\n", "- Multi-pattern matching with priority weighting\n", "- Site-specific configuration support\n", "- Content-based fallback classification\n", "- Confidence scoring and uncertainty handling\n", "- Learning from user feedback"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "from pathlib import Path\n", "from typing import Dict, List, Tuple, Optional\n", "from collections import defaultdict, Counter\n", "\n", "print(\"Enhanced Multi-Site PDF Classification System\")\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class EnhancedPDFClassifier:\n", "    \"\"\"Enhanced PDF classifier that handles multiple naming conventions and sites.\"\"\"\n", "    \n", "    def __init__(self, config_path: Optional[str] = None):\n", "        self.classification_patterns = self._load_default_patterns()\n", "        self.site_configs = {}\n", "        self.user_feedback = {}\n", "        self.confidence_threshold = 0.7\n", "        \n", "        if config_path:\n", "            self.load_site_config(config_path)\n", "    \n", "    def _load_default_patterns(self) -> Dict[str, List[Dict]]:\n", "        \"\"\"Load default classification patterns with multiple variations.\"\"\"\n", "        return {\n", "            'foundation_tolerance': [\n", "                {'pattern': r'tolerance', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'pier', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'tolerancia', 'weight': 1.0, 'language': 'es'},\n", "                {'pattern': r'toleranz', 'weight': 1.0, 'language': 'de'},\n", "                {'pattern': r'tolérance', 'weight': 1.0, 'language': 'fr'},\n", "                {'pattern': r'tol[-_]\\d+', 'weight': 0.9, 'language': 'code'},\n", "                {'pattern': r'pier[-_]analysis', 'weight': 0.9, 'language': 'en'},\n", "                {'pattern': r'foundation[-_]tol', 'weight': 0.8, 'language': 'en'}\n", "            ],\n", "            'electrical': [\n", "                {'pattern': r'sld', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'schema elettrico', 'weight': 1.0, 'language': 'it'},\n", "                {'pattern': r'electrical', 'weight': 0.9, 'language': 'en'},\n", "                {'pattern': r'schéma électrique', 'weight': 1.0, 'language': 'fr'},\n", "                {'pattern': r'elektrisch', 'weight': 1.0, 'language': 'de'},\n", "                {'pattern': r'eléctrico', 'weight': 1.0, 'language': 'es'},\n", "                {'pattern': r'elec[-_]\\d+', 'weight': 0.8, 'language': 'code'},\n", "                {'pattern': r'single[-_]line', 'weight': 0.9, 'language': 'en'},\n", "                {'pattern': r'power[-_]diagram', 'weight': 0.8, 'language': 'en'}\n", "            ],\n", "            'elevation_detail': [\n", "                {'pattern': r'alzamento', 'weight': 1.0, 'language': 'it'},\n", "                {'pattern': r'elevation', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'höhenansicht', 'weight': 1.0, 'language': 'de'},\n", "                {'pattern': r'élévation', 'weight': 1.0, 'language': 'fr'},\n", "                {'pattern': r'elevación', 'weight': 1.0, 'language': 'es'},\n", "                {'pattern': r'elev[-_]\\d+', 'weight': 0.8, 'language': 'code'},\n", "                {'pattern': r'section[-_]view', 'weight': 0.7, 'language': 'en'},\n", "                {'pattern': r'detail[-_]view', 'weight': 0.6, 'language': 'en'}\n", "            ],\n", "            'layout_plan': [\n", "                {'pattern': r'plan.*powerblock', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'layout.*plan', 'weight': 0.9, 'language': 'en'},\n", "                {'pattern': r'inquadramento.*layout', 'weight': 1.0, 'language': 'it'},\n", "                {'pattern': r'plano.*distribución', 'weight': 1.0, 'language': 'es'},\n", "                {'pattern': r'plan[-_]layout', 'weight': 0.8, 'language': 'en'},\n", "                {'pattern': r'site[-_]plan', 'weight': 0.7, 'language': 'en'}\n", "            ],\n", "            'foundation_detail': [\n", "                {'pattern': r'foundation', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'footing', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'fundación', 'weight': 1.0, 'language': 'es'},\n", "                {'pattern': r'fondation', 'weight': 1.0, 'language': 'fr'},\n", "                {'pattern': r'fundament', 'weight': 1.0, 'language': 'de'},\n", "                {'pattern': r'fondazione', 'weight': 1.0, 'language': 'it'},\n", "                {'pattern': r'found[-_]\\d+', 'weight': 0.8, 'language': 'code'}\n", "            ],\n", "            'flood_map': [\n", "                {'pattern': r'flood', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'inundación', 'weight': 1.0, 'language': 'es'},\n", "                {'pattern': r'inondation', 'weight': 1.0, 'language': 'fr'},\n", "                {'pattern': r'hochwasser', 'weight': 1.0, 'language': 'de'},\n", "                {'pattern': r'alluvione', 'weight': 1.0, 'language': 'it'}\n", "            ],\n", "            'site_civil': [\n", "                {'pattern': r'earthwork', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'civil', 'weight': 0.8, 'language': 'en'},\n", "                {'pattern': r'site', 'weight': 0.7, 'language': 'en'},\n", "                {'pattern': r'movimiento.*tierra', 'weight': 1.0, 'language': 'es'},\n", "                {'pattern': r'terrassement', 'weight': 1.0, 'language': 'fr'},\n", "                {'pattern': r'erdarbeiten', 'weight': 1.0, 'language': 'de'}\n", "            ],\n", "            'tracker_mounting': [\n", "                {'pattern': r'tracker', 'weight': 1.0, 'language': 'en'},\n", "                {'pattern': r'plinto', 'weight': 1.0, 'language': 'it'},\n", "                {'pattern': r'mounting', 'weight': 0.9, 'language': 'en'},\n", "                {'pattern': r'seguidor', 'weight': 1.0, 'language': 'es'},\n", "                {'pattern': r'montage', 'weight': 0.9, 'language': 'fr'}\n", "            ]\n", "        }\n", "    \n", "    def classify_with_confidence(self, file_path: str, site_id: Optional[str] = None) -> Tuple[str, float, Dict]:\n", "        \"\"\"Classify PDF with confidence score and detailed reasoning.\"\"\"\n", "        file_path_lower = str(file_path).lower()\n", "        scores = defaultdict(float)\n", "        matches = defaultdict(list)\n", "        \n", "        # Apply site-specific patterns if available\n", "        patterns_to_use = self.classification_patterns\n", "        if site_id and site_id in self.site_configs:\n", "            patterns_to_use = self.site_configs[site_id]\n", "        \n", "        # Score each category\n", "        for category, pattern_list in patterns_to_use.items():\n", "            for pattern_info in pattern_list:\n", "                pattern = pattern_info['pattern']\n", "                weight = pattern_info['weight']\n", "                \n", "                if re.search(pattern, file_path_lower):\n", "                    scores[category] += weight\n", "                    matches[category].append({\n", "                        'pattern': pattern,\n", "                        'weight': weight,\n", "                        'language': pattern_info['language']\n", "                    })\n", "        \n", "        # Determine best classification\n", "        if not scores:\n", "            return 'other', 0.0, {'reason': 'No patterns matched'}\n", "        \n", "        best_category = max(scores, key=scores.get)\n", "        best_score = scores[best_category]\n", "        \n", "        # Normalize confidence (simple approach)\n", "        max_possible_score = max(len(pattern_list) for pattern_list in patterns_to_use.values())\n", "        confidence = min(best_score / max_possible_score, 1.0)\n", "        \n", "        reasoning = {\n", "            'all_scores': dict(scores),\n", "            'matches': dict(matches),\n", "            'confidence': confidence,\n", "            'threshold_met': confidence >= self.confidence_threshold\n", "        }\n", "        \n", "        # Return 'other' if confidence is too low\n", "        if confidence < self.confidence_threshold:\n", "            return 'other', confidence, reasoning\n", "        \n", "        return best_category, confidence, reasoning\n", "    \n", "    def add_site_patterns(self, site_id: str, category: str, patterns: List[Dict]):\n", "        \"\"\"Add site-specific patterns.\"\"\"\n", "        if site_id not in self.site_configs:\n", "            self.site_configs[site_id] = self.classification_patterns.copy()\n", "        \n", "        if category not in self.site_configs[site_id]:\n", "            self.site_configs[site_id][category] = []\n", "        \n", "        self.site_configs[site_id][category].extend(patterns)\n", "    \n", "    def learn_from_feedback(self, file_path: str, correct_classification: str, site_id: Optional[str] = None):\n", "        \"\"\"Learn from user feedback to improve classification.\"\"\"\n", "        feedback_key = f\"{site_id}:{file_path}\" if site_id else file_path\n", "        self.user_feedback[feedback_key] = correct_classification\n", "        \n", "        # Extract potential new patterns from the filename\n", "        filename = Path(file_path).stem.lower()\n", "        words = re.findall(r'\\b\\w+\\b', filename)\n", "        \n", "        # Suggest new patterns (simplified approach)\n", "        suggested_patterns = []\n", "        for word in words:\n", "            if len(word) > 3:  # Avoid very short words\n", "                suggested_patterns.append({\n", "                    'pattern': word,\n", "                    'weight': 0.5,  # Lower weight for learned patterns\n", "                    'language': 'learned',\n", "                    'source': 'user_feedback'\n", "                })\n", "        \n", "        return suggested_patterns\n", "\n", "print(\"Enhanced PDF Classifier class defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the enhanced classifier with different naming conventions\n", "classifier = EnhancedPDFClassifier()\n", "\n", "# Test files from different sites with various naming conventions\n", "test_files_multi_site = {\n", "    'Site_A_Current': [\n", "        \"Foundation_Tolerance_Analysis.pdf\",\n", "        \"GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf\",\n", "        \"Plinto_Area_Alzamento_a_700mm.pdf\"\n", "    ],\n", "    'Site_B_Coded': [\n", "        \"TOL-001-PIER-ANALYSIS.pdf\",\n", "        \"ELEC-SCHEMA-MAIN-001.pdf\",\n", "        \"ELEV-SECTION-A-001.pdf\",\n", "        \"FOUND-DETAIL-BLOCK-1.pdf\"\n", "    ],\n", "    'Site_C_International': [\n", "        \"Tolerancia_Fundacion_Analisis.pdf\",  # Spanish\n", "        \"Schéma_Électrique_Principal.pdf\",     # French\n", "        \"Höhenansicht_Detail.pdf\",             # German\n", "        \"Plano_Distribución_General.pdf\"       # Spanish\n", "    ],\n", "    'Site_D_Descriptive': [\n", "        \"Pier_Foundation_Tolerance_Study.pdf\",\n", "        \"Single_Line_Electrical_Diagram.pdf\",\n", "        \"Elevation_View_Section_Details.pdf\",\n", "        \"Site_Civil_Earthworks_Plan.pdf\"\n", "    ]\n", "}\n", "\n", "print(\"Testing Enhanced Classification Across Multiple Sites\")\n", "print(\"=\" * 60)\n", "\n", "results_by_site = {}\n", "\n", "for site_name, files in test_files_multi_site.items():\n", "    print(f\"\\n{site_name}:\")\n", "    print(\"-\" * 40)\n", "    \n", "    site_results = []\n", "    for filename in files:\n", "        classification, confidence, reasoning = classifier.classify_with_confidence(filename, site_name)\n", "        site_results.append({\n", "            'filename': filename,\n", "            'classification': classification,\n", "            'confidence': confidence,\n", "            'reasoning': reasoning\n", "        })\n", "        \n", "        confidence_indicator = \"✅\" if confidence >= 0.7 else \"⚠️\" if confidence >= 0.5 else \"❌\"\n", "        print(f\"{filename:<40} → {classification:<20} ({confidence:.2f}) {confidence_indicator}\")\n", "    \n", "    results_by_site[site_name] = site_results\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze classification performance across sites\n", "print(\"\\nCLASSIFICATION PERFORMANCE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "total_files = 0\n", "high_confidence = 0\n", "medium_confidence = 0\n", "low_confidence = 0\n", "unclassified = 0\n", "\n", "classification_distribution = Counter()\n", "\n", "for site_name, results in results_by_site.items():\n", "    site_high = sum(1 for r in results if r['confidence'] >= 0.7)\n", "    site_medium = sum(1 for r in results if 0.5 <= r['confidence'] < 0.7)\n", "    site_low = sum(1 for r in results if 0.3 <= r['confidence'] < 0.5)\n", "    site_unclassified = sum(1 for r in results if r['classification'] == 'other')\n", "    \n", "    total_files += len(results)\n", "    high_confidence += site_high\n", "    medium_confidence += site_medium\n", "    low_confidence += site_low\n", "    unclassified += site_unclassified\n", "    \n", "    for result in results:\n", "        classification_distribution[result['classification']] += 1\n", "    \n", "    print(f\"\\n{site_name}:\")\n", "    print(f\"  High confidence (≥0.7): {site_high}/{len(results)} ({site_high/len(results)*100:.1f}%)\")\n", "    print(f\"  Medium confidence (0.5-0.7): {site_medium}/{len(results)} ({site_medium/len(results)*100:.1f}%)\")\n", "    print(f\"  Low confidence (0.3-0.5): {site_low}/{len(results)} ({site_low/len(results)*100:.1f}%)\")\n", "    print(f\"  Unclassified: {site_unclassified}/{len(results)} ({site_unclassified/len(results)*100:.1f}%)\")\n", "\n", "print(f\"\\nOVERALL PERFORMANCE:\")\n", "print(f\"Total files: {total_files}\")\n", "print(f\"High confidence: {high_confidence}/{total_files} ({high_confidence/total_files*100:.1f}%)\")\n", "print(f\"Medium confidence: {medium_confidence}/{total_files} ({medium_confidence/total_files*100:.1f}%)\")\n", "print(f\"Low confidence: {low_confidence}/{total_files} ({low_confidence/total_files*100:.1f}%)\")\n", "print(f\"Unclassified: {unclassified}/{total_files} ({unclassified/total_files*100:.1f}%)\")\n", "\n", "print(f\"\\nCLASSIFICATION DISTRIBUTION:\")\n", "for classification, count in classification_distribution.most_common():\n", "    print(f\"  {classification}: {count} files ({count/total_files*100:.1f}%)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}