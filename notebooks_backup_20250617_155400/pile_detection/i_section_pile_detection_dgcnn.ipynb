{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🏗️ I-Section Pile Detection (DGCNN)\n", "\n", "This notebook implements deep learning-based I-section pile detection as part of the pile detection stage. It uses DGCNN (Dynamic Graph CNN) to process ground-filtered or aligned point clouds and detect I-section pile structures.\n", "\n", "**Stage**: <PERSON>le Detection  \n", "**Input Data**: Ground-filtered or aligned point cloud  \n", "**Output**: Pile center coordinates + types (I-section, cylindrical, etc.)  \n", "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n", "**Model**: DGCNN for point-wise classification  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Ground-Filtered Data**: Import processed point cloud from ground segmentation or alignment\n", "2. **Patch Generation**: Create overlapping 3D patches for DGCNN processing\n", "3. **I-Section Detection**: Apply DGCNN model for point-wise classification\n", "4. **Post-Processing**: Cluster detected points and extract pile centers\n", "5. **Export Results**: Save detected pile data in .csv format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install torch torchvision torch-geometric torch-points3d open3d matplotlib numpy scipy pandas\n", "!pip install scikit-learn plotly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import open3d as o3d\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import ConvexHull\n", "from scipy.spatial.distance import cdist\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch_geometric.nn import DynamicEdgeConv, global_max_pool\n", "from torch_geometric.data import Data, Batch\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. I-<PERSON> Characteristics\n", "\n", "Define the geometric characteristics of I-section piles for detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ISectionPileConfig:\n", "    \"\"\"\n", "    Configuration for I-section pile detection.\n", "    \"\"\"\n", "    def __init__(self):\n", "        # I-section dimensions (typical ranges)\n", "        self.flange_width_range = (0.1, 0.4)  # meters\n", "        self.web_thickness_range = (0.01, 0.05)  # meters\n", "        self.flange_thickness_range = (0.01, 0.05)  # meters\n", "        self.height_range = (0.1, 1.0)  # meters\n", "        \n", "        # Detection parameters\n", "        self.patch_size = 2.0  # meters\n", "        self.min_points_per_patch = 100\n", "        self.overlap_ratio = 0.5\n", "        \n", "        # DGCNN parameters\n", "        self.k_neighbors = 20\n", "        self.num_points = 1024\n", "        self.feature_dims = [64, 128, 256, 512]\n", "        self.num_classes = 2  # pile vs non-pile\n", "\n", "config = ISectionPileConfig()\n", "print(\"I-Section Pile Detection Configuration:\")\n", "print(f\"Flange width range: {config.flange_width_range} m\")\n", "print(f\"Web thickness range: {config.web_thickness_range} m\")\n", "print(f\"Height range: {config.height_range} m\")\n", "print(f\"Patch size: {config.patch_size} m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Point Cloud Patch Generation\n", "\n", "Generate patches from point clouds for training and inference."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_patches_from_point_cloud(points, colors=None, patch_size=2.0, overlap_ratio=0.5, min_points=100):\n", "    \"\"\"\n", "    Generate overlapping patches from a point cloud.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray, optional\n", "        RGB colors (N, 3)\n", "    patch_size : float\n", "        Size of each patch in meters\n", "    overlap_ratio : float\n", "        Overlap ratio between adjacent patches\n", "    min_points : int\n", "        Minimum number of points per patch\n", "        \n", "    Returns:\n", "    --------\n", "    patches : list\n", "        List of patch dictionaries containing points, colors, and metadata\n", "    \"\"\"\n", "    # Calculate bounds\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    \n", "    # Calculate step size\n", "    step_size = patch_size * (1 - overlap_ratio)\n", "    \n", "    patches = []\n", "    patch_id = 0\n", "    \n", "    # Generate grid of patch centers\n", "    x_centers = np.arange(min_coords[0], max_coords[0], step_size)\n", "    y_centers = np.arange(min_coords[1], max_coords[1], step_size)\n", "    \n", "    for x_center in x_centers:\n", "        for y_center in y_centers:\n", "            # Define patch bounds\n", "            x_min = x_center - patch_size / 2\n", "            x_max = x_center + patch_size / 2\n", "            y_min = y_center - patch_size / 2\n", "            y_max = y_center + patch_size / 2\n", "            \n", "            # Find points within patch\n", "            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &\n", "                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))\n", "            \n", "            patch_points = points[mask]\n", "            \n", "            if len(patch_points) >= min_points:\n", "                # Center the patch points\n", "                patch_center = np.array([x_center, y_center, patch_points[:, 2].mean()])\n", "                centered_points = patch_points - patch_center\n", "                \n", "                patch_data = {\n", "                    'id': patch_id,\n", "                    'points': centered_points,\n", "                    'original_points': patch_points,\n", "                    'center': patch_center,\n", "                    'bounds': (x_min, y_min, x_max, y_max),\n", "                    'num_points': len(patch_points)\n", "                }\n", "                \n", "                if colors is not None:\n", "                    patch_data['colors'] = colors[mask]\n", "                \n", "                patches.append(patch_data)\n", "                patch_id += 1\n", "    \n", "    print(f\"Generated {len(patches)} patches from point cloud\")\n", "    return patches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. I-Section Pile Geometric Analysis\n", "\n", "Analyze point cloud patches for I-section pile characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_i_section_geometry(points, tolerance=0.05):\n", "    \"\"\"\n", "    Analyze point cloud patch for I-section pile geometry.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud patch (N, 3)\n", "    tolerance : float\n", "        Tolerance for geometric analysis\n", "        \n", "    Returns:\n", "    --------\n", "    features : dict\n", "        Geometric features indicating I-section characteristics\n", "    \"\"\"\n", "    if len(points) < 10:\n", "        return None\n", "    \n", "    # Project points to XY plane for cross-section analysis\n", "    xy_points = points[:, :2]\n", "    \n", "    # Find convex hull\n", "    try:\n", "        hull = ConvexHull(xy_points)\n", "        hull_points = xy_points[hull.vertices]\n", "    except:\n", "        return None\n", "    \n", "    # Calculate bounding box\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    dimensions = max_coords - min_coords\n", "    \n", "    # Analyze cross-sectional shape\n", "    # For I-section, we expect:\n", "    # 1. Elongated shape in one horizontal direction\n", "    # 2. Three main clusters: two flanges and web\n", "    # 3. Specific width-to-thickness ratios\n", "    \n", "    # Calculate aspect ratios\n", "    xy_aspect = dimensions[0] / dimensions[1] if dimensions[1] > 0 else 0\n", "    if xy_aspect < 1:\n", "        xy_aspect = 1 / xy_aspect\n", "    \n", "    # Analyze point distribution for I-section pattern\n", "    # Cluster points in cross-section\n", "    clustering = DBSCAN(eps=tolerance, min_samples=5)\n", "    cluster_labels = clustering.fit_predict(xy_points)\n", "    \n", "    unique_labels = np.unique(cluster_labels)\n", "    num_clusters = len(unique_labels[unique_labels >= 0])  # Exclude noise (-1)\n", "    \n", "    # Calculate density distribution\n", "    # For I-section, density should be higher at flanges and web\n", "    center_x, center_y = xy_points.mean(axis=0)\n", "    \n", "    # Divide into grid and calculate density\n", "    grid_size = 10\n", "    x_bins = np.linspace(xy_points[:, 0].min(), xy_points[:, 0].max(), grid_size)\n", "    y_bins = np.linspace(xy_points[:, 1].min(), xy_points[:, 1].max(), grid_size)\n", "    \n", "    density_grid = np.zeros((grid_size-1, grid_size-1))\n", "    for i in range(grid_size-1):\n", "        for j in range(grid_size-1):\n", "            mask = ((xy_points[:, 0] >= x_bins[i]) & (xy_points[:, 0] < x_bins[i+1]) &\n", "                   (xy_points[:, 1] >= y_bins[j]) & (xy_points[:, 1] < y_bins[j+1]))\n", "            density_grid[j, i] = mask.sum()\n", "    \n", "    # Calculate features\n", "    features = {\n", "        'num_points': len(points),\n", "        'dimensions': dimensions,\n", "        'xy_aspect_ratio': xy_aspect,\n", "        'height': dimensions[2],\n", "        'width': max(dimensions[0], dimensions[1]),\n", "        'thickness': min(dimensions[0], dimensions[1]),\n", "        'num_clusters': num_clusters,\n", "        'hull_area': hull.volume if hasattr(hull, 'volume') else 0,\n", "        'density_variance': np.var(density_grid),\n", "        'density_max': np.max(density_grid),\n", "        'compactness': len(points) / hull.volume if hasattr(hull, 'volume') and hull.volume > 0 else 0\n", "    }\n", "    \n", "    return features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. DGCNN Model for I-Section Pile Detection\n", "\n", "Implement Dynamic Graph CNN for point-wise classification."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class DGCNNISectionPile(nn.Module):\n", "    \"\"\"\n", "    DGCNN model for I-section pile detection.\n", "    \"\"\"\n", "    def __init__(self, k=20, feature_dims=[64, 128, 256, 512], num_classes=2, dropout=0.5):\n", "        super(DGCNNISectionPile, self).__init__()\n", "        self.k = k\n", "        self.num_classes = num_classes\n", "        \n", "        # Edge convolution layers\n", "        self.conv1 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(6, feature_dims[0]),  # 6 = 3 (xyz) * 2 (point and neighbor)\n", "            nn.BatchNorm1d(feature_dims[0]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[0], feature_dims[0])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv2 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[0] * 2, feature_dims[1]),\n", "            nn.BatchNorm1d(feature_dims[1]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[1], feature_dims[1])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv3 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[1] * 2, feature_dims[2]),\n", "            nn.BatchNorm1d(feature_dims[2]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[2], feature_dims[2])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv4 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[2] * 2, feature_dims[3]),\n", "            nn.BatchNorm1d(feature_dims[3]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[3], feature_dims[3])\n", "        ), k=k, aggr='max')\n", "        \n", "        # Global feature aggregation\n", "        total_features = sum(feature_dims)\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.Linear(total_features, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "        \n", "        # Point-wise classification head\n", "        self.point_classifier = nn.Sequential(\n", "            nn.Linear(total_features, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON>(256, 128),\n", "            nn.<PERSON>chNorm1d(128),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(128, num_classes)\n", "        )\n", "    \n", "    def forward(self, data):\n", "        x, batch = data.x, data.batch\n", "        \n", "        # Extract features through edge convolutions\n", "        x1 = self.conv1(x, batch)\n", "        x2 = self.conv2(x1, batch)\n", "        x3 = self.conv3(x2, batch)\n", "        x4 = self.conv4(x3, batch)\n", "        \n", "        # Concatenate all features\n", "        x_concat = torch.cat([x1, x2, x3, x4], dim=1)\n", "        \n", "        # Global classification (patch-level)\n", "        global_features = global_max_pool(x_concat, batch)\n", "        global_pred = self.classifier(global_features)\n", "        \n", "        # Point-wise classification\n", "        point_pred = self.point_classifier(x_concat)\n", "        \n", "        return global_pred, point_pred"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}