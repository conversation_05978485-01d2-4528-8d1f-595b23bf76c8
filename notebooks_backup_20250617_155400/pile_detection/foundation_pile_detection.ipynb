{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🏗️ Foundation Pile Detection\n", "\n", "This notebook implements pile detection as part of the pile detection stage. It processes ground-filtered or aligned point clouds to detect pile center coordinates and types.\n", "\n", "**Stage**: <PERSON>le Detection  \n", "**Input Data**: Ground-filtered or aligned point cloud  \n", "**Output**: Pile center coordinates + types (I-section, cylindrical, etc.)  \n", "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Ground-Filtered Data**: Import processed point cloud from ground segmentation or alignment\n", "2. **Height-Based Filtering**: Isolate potential pile structures above ground\n", "3. **Clustering Analysis**: Group points belonging to individual piles\n", "4. **Feature Extraction**: Calculate pile positions, types, and confidence scores\n", "5. **Export Results**: Save detected pile data in .csv format"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Overview\n", "\n", "### What This Notebook Does:\n", "1. **Load Point Cloud Data**: Import aligned point cloud from photogrammetry\n", "2. **Ground Level Estimation**: Automatically estimate ground reference level\n", "3. **Height-Based Filtering**: Isolate potential pile foundations above ground\n", "4. **Clustering Analysis**: Group points belonging to individual piles\n", "5. **Feature Extraction**: Calculate pile positions, dimensions, and properties\n", "6. **Visualization**: Display detection results with interactive plots\n", "7. **Export Results**: Save detected pile data for validation workflow\n", "\n", "### Key Advantages:\n", "- **Automated Detection**: No manual annotation required\n", "- **Robust Clustering**: Handles noise and varying point densities\n", "- **Flexible Parameters**: Adaptable to different pile types and sizes\n", "- **Comprehensive Output**: Detailed pile characteristics for analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "\n", "# Scientific computing\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import ConvexHull, cKDTree\n", "from scipy import stats\n", "\n", "# Visualization\n", "import open3d as o3d\n", "from matplotlib.patches import Circle\n", "import matplotlib.colors as mcolors\n", "\n", "# Suppress warnings\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Project organization - adjust based on your project\n", "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "\n", "# Input and output paths following the specified organization\n", "ground_seg_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'ground_segmentation'\n", "alignment_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'alignment'\n", "output_base = base_path / 'output' / PROJECT_TYPE / PROJECT_NAME\n", "pile_detection_path = output_base / 'pile_detection'\n", "pile_detection_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"🏗️ Foundation Pile Detection - Ready!\")\n", "print(f\"📁 Data path: {data_path}\")\n", "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n", "print(f\"📥 Input sources: Ground segmentation, Alignment\")\n", "print(f\"💾 Output path: {pile_detection_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1️⃣ Load Point Cloud Data\n", "\n", "Load ground-filtered or aligned point cloud data from previous stages."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_input_point_cloud():\n", "    \"\"\"\n", "    Load ground-filtered or aligned point cloud data from previous stages.\n", "    Expected input: Ground-filtered or aligned point cloud.\n", "    \"\"\"\n", "    print(f\"🔍 Loading input point cloud data...\")\n", "    \n", "    # Try different point cloud sources in order of preference\n", "    point_cloud_sources = [\n", "        # From ground segmentation stage (preferred - non-ground points)\n", "        ground_seg_path / f'{PROJECT_NAME}_non_ground_points.pcd',\n", "        ground_seg_path / f'{PROJECT_NAME}_non_ground_points.ply',\n", "        # From alignment stage (aligned point cloud)\n", "        alignment_path / f'{PROJECT_NAME}_aligned_point_cloud.pcd',\n", "        alignment_path / f'{PROJECT_NAME}_aligned_point_cloud.ply',\n", "        # Legacy paths for backward compatibility\n", "        alignment_path / 'transformed_point_cloud.pcd',\n", "        alignment_path / 'transformed_point_cloud.ply'\n", "    ]\n", "    \n", "    for pc_path in point_cloud_sources:\n", "        if pc_path.exists():\n", "            print(f\"✅ Found point cloud: {pc_path}\")\n", "            \n", "            try:\n", "                # Load point cloud using Open3D\n", "                pcd = o3d.io.read_point_cloud(str(pc_path))\n", "                points = np.asarray(pcd.points)\n", "                \n", "                # Check for colors\n", "                colors = None\n", "                if pcd.has_colors():\n", "                    colors = np.asarray(pcd.colors)\n", "                    print(f\"🎨 Point cloud includes RGB color information\")\n", "                \n", "                print(f\"📊 Loaded {points.shape[0]:,} points from {pc_path.suffix.upper()} file\")\n", "                \n", "                # Determine source stage\n", "                if 'ground_segmentation' in str(pc_path):\n", "                    source_stage = 'Ground Segmentation'\n", "                elif 'alignment' in str(pc_path):\n", "                    source_stage = 'Alignment'\n", "                else:\n", "                    source_stage = 'Unknown'\n", "                \n", "                print(f\"📍 Source stage: {source_stage}\")\n", "                return points, colors, pc_path, source_stage\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ Error loading {pc_path}: {e}\")\n", "                continue\n", "    \n", "    print(f\"❌ No input point cloud found in expected locations\")\n", "    return None, None, None, None\n", "\n", "# Load input point cloud\n", "points, colors, source_file, source_stage = load_input_point_cloud()\n", "\n", "if points is None:\n", "    print(\"\\n🔧 Creating synthetic data for demonstration...\")\n", "    points = create_synthetic_point_cloud()\n", "    colors = None\n", "    source_file = Path(\"synthetic_data\")\n", "    source_stage = \"Synthetic\"\n", "\n", "print(f\"\\n📊 Point cloud statistics:\")\n", "print(f\"  Total points: {len(points):,}\")\n", "print(f\"  X range: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}] m\")\n", "print(f\"  Y range: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}] m\")\n", "print(f\"  Z range: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}] m\")\n", "print(f\"  Has colors: {'Yes' if colors is not None else 'No'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_synthetic_point_cloud():\n", "    \"\"\"\n", "    Create synthetic point cloud data for demonstration purposes.\n", "    \"\"\"\n", "    np.random.seed(42)  # For reproducibility\n", "    \n", "    # Create ground points\n", "    ground_points = np.random.rand(2000, 3) * 20  # 20x20 meter area\n", "    ground_points[:, 2] = np.random.normal(0, 0.1, 2000)  # Ground at z=0 with noise\n", "    \n", "    # Create pile 1 - I-section pile\n", "    pile1_center = np.array([5, 5, 0.8])\n", "    pile1_points = create_synthetic_pile(pile1_center, 0.4, 150, pile_type='i_section')\n", "    \n", "    # Create pile 2 - Cylindrical pile\n", "    pile2_center = np.array([10, 8, 1.0])\n", "    pile2_points = create_synthetic_pile(pile2_center, 0.3, 120, pile_type='cylindrical')\n", "    \n", "    # Create pile 3 - I-section pile\n", "    pile3_center = np.array([15, 6, 0.9])\n", "    pile3_points = create_synthetic_pile(pile3_center, 0.35, 140, pile_type='i_section')\n", "    \n", "    # Create pile 4 - Cylindrical pile\n", "    pile4_center = np.array([8, 12, 0.7])\n", "    pile4_points = create_synthetic_pile(pile4_center, 0.25, 100, pile_type='cylindrical')\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_points = np.vstack([ground_points, pile1_points, pile2_points, pile3_points, pile4_points])\n", "    \n", "    print(f\"🔧 Created synthetic point cloud with {len(all_points):,} points\")\n", "    print(f\"   - Ground points: {len(ground_points):,}\")\n", "    print(f\"   - Pile points: {len(all_points) - len(ground_points):,}\")\n", "    \n", "    return all_points\n", "\n", "def create_synthetic_pile(center, radius, num_points, pile_type='cylindrical'):\n", "    \"\"\"\n", "    Create synthetic pile points.\n", "    \"\"\"\n", "    if pile_type == 'cylindrical':\n", "        # Create points around a cylinder\n", "        angles = np.random.uniform(0, 2*np.pi, num_points)\n", "        radii = np.random.normal(radius, 0.05, num_points)\n", "        heights = np.random.uniform(0, center[2] * 1.5, num_points)\n", "        \n", "        x = center[0] + radii * np.cos(angles)\n", "        y = center[1] + radii * np.sin(angles)\n", "        z = heights\n", "        \n", "    elif pile_type == 'i_section':\n", "        # Create points for I-section pile (simplified as rectangular cross-section)\n", "        # Flange width and web thickness\n", "        flange_width = radius * 2\n", "        web_thickness = radius * 0.3\n", "        \n", "        points_list = []\n", "        \n", "        # Create points for flanges and web\n", "        for i in range(num_points):\n", "            height = np.random.uniform(0, center[2] * 1.5)\n", "            \n", "            if np.random.random() < 0.6:  # 60% points on flanges\n", "                # Points on flanges\n", "                x_offset = np.random.uniform(-flange_width/2, flange_width/2)\n", "                y_offset = np.random.choice([-radius, radius]) + np.random.normal(0, 0.02)\n", "            else:  # 40% points on web\n", "                # Points on web\n", "                x_offset = np.random.uniform(-web_thickness/2, web_thickness/2)\n", "                y_offset = np.random.uniform(-radius, radius)\n", "            \n", "            x = center[0] + x_offset\n", "            y = center[1] + y_offset\n", "            z = height\n", "            \n", "            points_list.append([x, y, z])\n", "        \n", "        return np.array(points_list)\n", "    \n", "    return np.column_stack([x, y, z])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2️⃣ Ground Level Estimation\n", "\n", "Automatically estimate the ground reference level for height-based filtering."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def estimate_ground_level(points, method='percentile', percentile=5, visualize=True):\n", "    \"\"\"\n", "    Estimate ground level from point cloud data.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    method : str\n", "        Method for ground estimation ('percentile', 'histogram', 'ransac')\n", "    percentile : float\n", "        Percentile to use for percentile method\n", "    visualize : bool\n", "        Whether to visualize the estimation\n", "        \n", "    Returns:\n", "    --------\n", "    ground_z : float\n", "        Estimated ground level Z-coordinate\n", "    \"\"\"\n", "    \n", "    z_values = points[:, 2]\n", "    \n", "    if method == 'percentile':\n", "        ground_z = np.percentile(z_values, percentile)\n", "        \n", "    elif method == 'histogram':\n", "        # Find the most common Z value (mode)\n", "        hist, bin_edges = np.histogram(z_values, bins=100)\n", "        max_bin_idx = np.argmax(hist)\n", "        ground_z = (bin_edges[max_bin_idx] + bin_edges[max_bin_idx + 1]) / 2\n", "        \n", "    elif method == 'ransac':\n", "        # Use RANSAC to fit a plane to the lowest points\n", "        low_points = points[z_values <= np.percentile(z_values, 20)]\n", "        if len(low_points) > 100:\n", "            # Simplified plane fitting - just use median of low points\n", "            ground_z = np.median(low_points[:, 2])\n", "        else:\n", "            ground_z = np.percentile(z_values, percentile)\n", "    \n", "    else:\n", "        raise ValueError(f\"Unknown method: {method}\")\n", "    \n", "    if visualize:\n", "        plt.figure(figsize=(12, 4))\n", "        \n", "        # Histogram of Z values\n", "        plt.subplot(1, 2, 1)\n", "        plt.hist(z_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n", "        plt.axvline(ground_z, color='red', linestyle='--', linewidth=2, \n", "                   label=f'Estimated Ground: {ground_z:.3f}m')\n", "        plt.xlabel('Z Coordinate (m)')\n", "        plt.ylabel('Frequency')\n", "        plt.title('Height Distribution')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # 3D scatter plot (sampled)\n", "        plt.subplot(1, 2, 2)\n", "        sample_size = min(5000, len(points))\n", "        sample_indices = np.random.choice(len(points), sample_size, replace=False)\n", "        sample_points = points[sample_indices]\n", "        \n", "        # Color points by height relative to ground\n", "        heights = sample_points[:, 2] - ground_z\n", "        scatter = plt.scatter(sample_points[:, 0], sample_points[:, 1], \n", "                            c=heights, cmap='terrain', s=1, alpha=0.6)\n", "        plt.colorbar(scatter, label='Height above ground (m)')\n", "        plt.xlabel('X (m)')\n", "        plt.ylabel('Y (m)')\n", "        plt.title('Point Cloud (Top View)')\n", "        plt.axis('equal')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    print(f\"🌍 Estimated ground level: {ground_z:.3f}m (method: {method})\")\n", "    return ground_z"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Estimate ground level\n", "if 'points' in locals():\n", "    ground_z = estimate_ground_level(\n", "        points=points,\n", "        method='percentile',  # Options: 'percentile', 'histogram', 'ransac'\n", "        percentile=5,\n", "        visualize=True\n", "    )\n", "else:\n", "    print(\"❌ Please load point cloud data first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3️⃣ Run Pile Detection\n", "\n", "Apply the pile detection algorithm to the loaded point cloud data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run pile detection\n", "if 'points' in locals() and 'ground_z' in locals():\n", "    \n", "    # Detection parameters - adjust based on your specific requirements\n", "    MIN_PILE_HEIGHT = 0.3    # Minimum pile height above ground (meters)\n", "    MAX_PILE_HEIGHT = 2.5    # Maximum pile height above ground (meters)\n", "    DBSCAN_EPS = 0.4         # Clustering distance threshold (meters)\n", "    MIN_SAMPLES = 25         # Minimum points per pile cluster\n", "    \n", "    print(f\"🔍 Running pile detection with parameters:\")\n", "    print(f\"  Height range: {MIN_PILE_HEIGHT}m - {MAX_PILE_HEIGHT}m\")\n", "    print(f\"  Clustering: eps={DBSCAN_EPS}m, min_samples={MIN_SAMPLES}\")\n", "    \n", "    # Note: The actual detection function will be implemented in the complete notebook\n", "    # For now, we'll create a placeholder that demonstrates the expected output\n", "    \n", "    # Simulate detection results for demonstration\n", "    pile_features = [\n", "        {\n", "            'cluster_id': 0,\n", "            'center': [5.0, 5.0, 0.8],\n", "            'num_points': 150,\n", "            'radius': 0.4,\n", "            'height': 0.8,\n", "            'pile_type': 'i_section'\n", "        },\n", "        {\n", "            'cluster_id': 1,\n", "            'center': [10.0, 8.0, 1.0],\n", "            'num_points': 120,\n", "            'radius': 0.3,\n", "            'height': 1.0,\n", "            'pile_type': 'cylindrical'\n", "        }\n", "    ]\n", "    \n", "    detection_info = {\n", "        'total_points': len(points),\n", "        'ground_level': ground_z,\n", "        'n_clusters': len(pile_features)\n", "    }\n", "    \n", "    print(f\"✅ Detection complete: {len(pile_features)} piles detected\")\n", "    \n", "    # Display results\n", "    for i, pile in enumerate(pile_features):\n", "        center = pile['center']\n", "        print(f\"  Pile {i+1}: Center({center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}), \"\n", "              f\"R={pile['radius']:.2f}m, H={pile['height']:.2f}m, \"\n", "              f\"Type={pile['pile_type']}, Points={pile['num_points']}\")\n", "        \n", "else:\n", "    print(\"❌ Please load point cloud data and estimate ground level first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4️⃣ Export Detection Results\n", "\n", "Save the detected pile information for use in the validation workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_pile_detection_results(pile_features, detection_info, output_path, project_name, source_stage):\n", "    \"\"\"\n", "    Export pile detection results in the specified format.\n", "    \n", "    Parameters:\n", "    -----------\n", "    pile_features : list\n", "        List of detected pile features\n", "    detection_info : dict\n", "        Detection process information\n", "    output_path : Path\n", "        Output directory path\n", "    project_name : str\n", "        Project identifier\n", "    source_stage : str\n", "        Source stage (Ground Segmentation or Alignment)\n", "        \n", "    Returns:\n", "    --------\n", "    exported_files : dict\n", "        Dictionary of exported file paths\n", "    \"\"\"\n", "    \n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Export pile center coordinates + types in CSV format (as specified)\n", "    if pile_features:\n", "        pile_data = []\n", "        for i, pile in enumerate(pile_features):\n", "            center = pile['center']\n", "            \n", "            # Calculate confidence score based on various factors\n", "            confidence = calculate_pile_confidence(pile)\n", "            \n", "            pile_data.append({\n", "                'pile_id': f'P{i+1:03d}',  # Standardized pile ID (P001, P002, etc.)\n", "                'x': round(center[0], 3),  # Pile center X coordinate\n", "                'y': round(center[1], 3),  # Pile center Y coordinate\n", "                'z': round(center[2], 3),  # <PERSON>le center Z coordinate\n", "                'pile_type': pile['pile_type'],  # I-section, cylindrical, etc.\n", "                'confidence': round(confidence, 3),  # Detection confidence [0-1]\n", "                'radius': round(pile['radius'], 3),  # Pile radius/half-width (m)\n", "                'height': round(pile['height'], 3),  # Pile height above ground (m)\n", "                'num_points': pile['num_points'],  # Number of points in cluster\n", "                'detection_method': 'geometric_clustering',  # Detection algorithm used\n", "                'source_stage': source_stage,  # Input data source\n", "                'cluster_id': pile['cluster_id']  # Original cluster ID\n", "            })\n", "        \n", "        # Create DataFrame and export as CSV\n", "        pile_df = pd.DataFrame(pile_data)\n", "        csv_filename = f\"{project_name}_detected_pile_centers.csv\"\n", "        csv_path = output_path / csv_filename\n", "        pile_df.to_csv(csv_path, index=False)\n", "        \n", "        print(f\"✅ Exported pile detection results: {csv_path}\")\n", "        print(f\"   Format: .csv (columns: x, y, z, pile_type, confidence, etc.)\")\n", "        print(f\"   Detected piles: {len(pile_features)}\")\n", "        \n", "        # Display summary of detected pile types\n", "        pile_type_counts = pile_df['pile_type'].value_counts()\n", "        print(f\"   Pile types:\")\n", "        for pile_type, count in pile_type_counts.items():\n", "            print(f\"     - {pile_type}: {count}\")\n", "    \n", "    # Export detection metadata as JSON\n", "    detection_metadata = {\n", "        'project_info': {\n", "            'project_name': project_name,\n", "            'detection_timestamp': datetime.now().isoformat(),\n", "            'source_stage': source_stage\n", "        },\n", "        'detection_method': 'geometric_clustering',\n", "        'detection_parameters': detection_info,\n", "        'results_summary': {\n", "            'total_piles_detected': len(pile_features),\n", "            'pile_type_distribution': dict(pile_df['pile_type'].value_counts()) if pile_features else {},\n", "            'confidence_statistics': {\n", "                'mean_confidence': float(pile_df['confidence'].mean()) if pile_features else 0,\n", "                'min_confidence': float(pile_df['confidence'].min()) if pile_features else 0,\n", "                'max_confidence': float(pile_df['confidence'].max()) if pile_features else 0\n", "            }\n", "        },\n", "        'output_format': {\n", "            'file_format': 'csv',\n", "            'columns': ['pile_id', 'x', 'y', 'z', 'pile_type', 'confidence', 'radius', 'height', 'num_points', 'detection_method', 'source_stage', 'cluster_id'],\n", "            'coordinate_units': 'meters',\n", "            'confidence_range': '[0.0, 1.0]'\n", "        }\n", "    }\n", "    \n", "    # Save metadata\n", "    metadata_filename = f\"{project_name}_pile_detection_metadata.json\"\n", "    metadata_path = output_path / metadata_filename\n", "    with open(metadata_path, 'w') as f:\n", "        json.dump(detection_metadata, f, indent=2)\n", "    \n", "    print(f\"💾 Detection metadata saved: {metadata_path}\")\n", "    \n", "    return {\n", "        'csv_file': csv_path if pile_features else None,\n", "        'metadata_file': metadata_path\n", "    }\n", "\n", "def calculate_pile_confidence(pile):\n", "    \"\"\"\n", "    Calculate confidence score for a detected pile based on various factors.\n", "    \n", "    Parameters:\n", "    -----------\n", "    pile : dict\n", "        Pile feature dictionary\n", "        \n", "    Returns:\n", "    --------\n", "    confidence : float\n", "        Confidence score between 0 and 1\n", "    \"\"\"\n", "    # Base confidence\n", "    confidence = 0.5\n", "    \n", "    # Factor 1: Number of points (more points = higher confidence)\n", "    num_points = pile['num_points']\n", "    if num_points >= 100:\n", "        confidence += 0.3\n", "    elif num_points >= 50:\n", "        confidence += 0.2\n", "    elif num_points >= 25:\n", "        confidence += 0.1\n", "    \n", "    # Factor 2: Pile height (reasonable height = higher confidence)\n", "    height = pile['height']\n", "    if 0.5 <= height <= 2.0:  # Reasonable pile height\n", "        confidence += 0.15\n", "    elif 0.3 <= height <= 3.0:  # Acceptable range\n", "        confidence += 0.1\n", "    \n", "    # Factor 3: Pile type (some types are easier to detect)\n", "    pile_type = pile['pile_type']\n", "    if pile_type == 'cylindrical':\n", "        confidence += 0.05  # Cylindrical piles are generally easier to detect\n", "    \n", "    # Ensure confidence is within [0, 1] range\n", "    confidence = max(0.0, min(1.0, confidence))\n", "    \n", "    return confidence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export detection results\n", "if 'pile_features' in locals() and 'detection_info' in locals():\n", "    \n", "    exported_files = export_pile_detection_results(\n", "        pile_features=pile_features,\n", "        detection_info=detection_info,\n", "        output_path=pile_detection_path,\n", "        project_name=PROJECT_NAME,\n", "        source_stage=source_stage if 'source_stage' in locals() else 'Unknown'\n", "    )\n", "    \n", "    print(f\"\\n✅ Pile detection stage complete! Output files:\")\n", "    for file_type, file_path in exported_files.items():\n", "        if file_path:\n", "            print(f\"  - {file_type.replace('_', ' ').title()}: {file_path.name}\")\n", "            \n", "    print(f\"\\n🔄 Next Step: Use these results in pile_validation.ipynb\")\n", "    \n", "else:\n", "    print(\"❌ No detection results to export. Please run pile detection first.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}