import os
import argparse
from PyPDF2 import Pdf<PERSON>ead<PERSON>
from pdf2image import convert_from_path
import pytesseract

def extract_pdf_info(pdf_path):
    print(f"\n📄 File: {os.path.basename(pdf_path)}")

    # Extract PDF metadata and page count
    try:
        reader = PdfReader(pdf_path)
        meta = reader.metadata
        num_pages = len(reader.pages)
        print(f"📚 Pages: {num_pages}")
        if meta:
            title = meta.title or "N/A"
            author = meta.author or "N/A"
            print(f"📝 Title: {title}")
            print(f"🧑 Author: {author}")
    except Exception as e:
        print(f"❌ PyPDF2 failed: {e}")
        reader = None

    # OCR first page only
    try:
        print("🔍 OCR preview (first page):")
        images = convert_from_path(pdf_path, first_page=1, last_page=1)
        text = pytesseract.image_to_string(images[0])
        print(text[:500])  # Limit to first 500 characters
    except Exception as e:
        print(f"❌ OCR failed: {e}")

def main():
    parser = argparse.ArgumentParser(description="Extract metadata and OCR preview from CAD-style PDFs.")
    parser.add_argument("input_path", help="PDF file or directory containing PDFs")

    args = parser.parse_args()
    path = args.input_path

    if os.path.isfile(path) and path.lower().endswith(".pdf"):
        extract_pdf_info(path)
    elif os.path.isdir(path):
        pdf_files = [os.path.join(path, f) for f in os.listdir(path) if f.lower().endswith(".pdf")]
        if not pdf_files:
            print("No PDF files found in directory.")
        for file in pdf_files:
            extract_pdf_info(file)
    else:
        print("❌ Invalid path or no PDF files found.")

if __name__ == "__main__":
    main()

