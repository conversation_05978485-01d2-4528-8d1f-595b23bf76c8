from chromadb import PersistentClient
from sentence_transformers import SentenceTransformer

# Use the same path used in vectorize_cad_pdf.py
client = PersistentClient(path="./chroma_db")
collection = client.get_collection(name="cad_pdf_chunks")

model = SentenceTransformer("all-MiniLM-L6-v2")

def search(query, top_k=3):
    embedding = model.encode(query).tolist()
    results = collection.query(
        query_embeddings=[embedding],
        n_results=top_k
    )

    for i, doc in enumerate(results["documents"][0]):
        print(f"\n🔎 Result {i+1}:")
        print(doc)
        print("📄 Metadata:", results["metadatas"][0][i])

if __name__ == "__main__":
    print("🔍 Sample queries you can try:")
    print("- PILE schedule for BLOCK-62")
    print("- HEAVY ARRAY PIER in BLOCK-69")
    print("- Tracker layout or PV pile types\n")

    query = input("🔎 Enter your search query: ")
    search(query)

