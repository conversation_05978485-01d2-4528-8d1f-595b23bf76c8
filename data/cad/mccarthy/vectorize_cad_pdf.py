import pdfplumber
from sentence_transformers import SentenceTransformer
import chromadb
from chromadb.config import Settings
import os

# Configure persistent Chroma client
from chromadb import PersistentClient

PERSIST_DIR = "./chroma_db"
client = PersistentClient(path=PERSIST_DIR)
collection_name = "cad_pdf_chunks"
collection = client.get_or_create_collection(name=collection_name)



def extract_text_chunks_from_pdf(pdf_path, chunk_size=500):
    text_chunks = []
    with pdfplumber.open(pdf_path) as pdf:
        for i, page in enumerate(pdf.pages):
            text = page.extract_text()
            if not text:
                continue
            for j in range(0, len(text), chunk_size):
                chunk = text[j:j+chunk_size]
                text_chunks.append({"text": chunk, "metadata": {"page": i+1}})
    return text_chunks

def vectorize_and_store(chunks):
    model = SentenceTransformer("all-MiniLM-L6-v2")
    collection = client.get_or_create_collection(name="cad_pdf_chunks")

    for i, chunk in enumerate(chunks):
        embedding = model.encode(chunk["text"]).tolist()
        collection.add(
            documents=[chunk["text"]],
            embeddings=[embedding],
            metadatas=[chunk["metadata"]],
            ids=[f"chunk_{i}"]
        )

    print(f"✅ Stored {len(chunks)} chunks into Chroma DB at './chroma_db'")


if __name__ == "__main__":
    pdf_path = "PB01_-_POWERBLOCK_PLAN.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ PDF not found: {pdf_path}")
    else:
        chunks = extract_text_chunks_from_pdf(pdf_path)
        vectorize_and_store(chunks)

