import json
import csv
import re
from pathlib import Path

def extract_block_ids(text):
    return re.findall(r"\*\*BLOCK-(\d+)\*\*", text)

def extract_pile_schedule_entries(text, schedule_type):
    pattern = re.compile(
        r"(W6X[\d\.]+).*?(HEAVY|STD|.*?PIER).*?(\d+\.\d+)'.*?(\d+\.\d+)'[\s/]*?(\d+)",
        re.DOTALL | re.IGNORECASE
    )
    matches = pattern.findall(text)
    rows = []
    for shape, pile_type, length, reveal, qty in matches:
        rows.append({
            "shape": shape.strip(),
            "pile_type": pile_type.strip(),
            "reveal": reveal.strip() + "'",
            "embed": length.strip() + "'",
            "qty": qty.strip(),
            "schedule_type": schedule_type
        })
    return rows

def parse_and_export(json_path, csv_out_path):
    with open(json_path, "r") as f:
        data = json.load(f)[0]  # assuming single document chunk

    text = data.get("text", "")
    meta = data.get("metadata", {})

    block_ids = extract_block_ids(text)
    all_rows = []

    for schedule_type in ["4.23'", "4.73'", "4.33'", "3.83'"]:
        schedule_rows = extract_pile_schedule_entries(text, schedule_type)
        for r in schedule_rows:
            for block_id in block_ids:
                r.update({
                    "block_id": f"BLOCK-{block_id}",
                    "file": Path(json_path).stem.replace(".json", ""),
                    "page": meta.get("page", 1)
                })
                all_rows.append(r)

    keys = ["block_id", "pile_type", "shape", "reveal", "embed", "qty", "schedule_type", "file", "page"]

    with open(csv_out_path, "w", newline="") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=keys)
        writer.writeheader()
        for row in all_rows:
            writer.writerow(row)

    print(f"✅ Exported structured CSV to: {csv_out_path}")

# Usage
parse_and_export("PB02_-_POWERBLOCK_PLAN.json", "PB01_structured_output.csv")

