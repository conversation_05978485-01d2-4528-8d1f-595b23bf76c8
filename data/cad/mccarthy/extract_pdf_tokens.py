import argparse
import json
import pymupdf4llm
from pathlib import Path

def make_serializable(obj):
    if isinstance(obj, dict):
        return {k: make_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_serializable(i) for i in obj]
    elif hasattr(obj, '__dict__'):
        return str(obj)  # fallback: convert to string
    return obj

def main(pdf_path, save_json=False):
    chunks = pymupdf4llm.to_markdown(pdf_path, page_chunks=True)
    print(f"\n✅ Extracted {len(chunks)} chunks from {pdf_path}")
    for i, ch in enumerate(chunks[:3]):
        print("---")
        print(ch["text"][:200])
        print("Metadata:", ch["metadata"])

    if save_json:
        output_path = Path(pdf_path).with_suffix(".json")
        serializable_chunks = make_serializable(chunks)
        with open(output_path, "w") as f:
            json.dump(serializable_chunks, f, indent=2)
        print(f"✅ Saved JSON output to {output_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("pdf_path")
    parser.add_argument("--json", action="store_true")
    args = parser.parse_args()
    main(args.pdf_path, args.json)

