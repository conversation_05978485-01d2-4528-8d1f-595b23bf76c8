#!/usr/bin/env python3
"""
Project Data Assessment for Thesis

Simple script to evaluate all available point cloud datasets using AWS CLI commands.
Generates a comprehensive assessment matrix for thesis dataset selection.
"""

import subprocess
import pandas as pd
import json
from pathlib import Path
import sys

# Project configuration
PROJECTS = {
    'Castro': {
        'full_name': '<PERSON><PERSON><PERSON>',
        'url': 's3://preetam-filezilla-test/Castro/Pointcloud/',
        'type': 'S3_Folder',
        'estimated_gb': 46.8,
        'command': 'aws s3 ls s3://preetam-filezilla-test/Castro/Pointcloud/ --recursive --human-readable'
    },
    'Mudjar': {
        'full_name': 'Mud<PERSON> - ENEL',
        'url': 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las',
        'type': 'HTTPS_Direct',
        'estimated_gb': 1.4,
        'command': 'curl -I --max-time 30 "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las"'
    },
    'Giorgio': {
        'full_name': 'Piani Di Giorgio - ENEL',
        'url': 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_pointcloud.las',
        'type': 'HTTPS_Direct',
        'estimated_gb': 2.8,
        'command': 'curl -I --max-time 30 "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_pointcloud.las"'
    },
    'McCarthy': {
        'full_name': 'Sunstreams Project - McCarthy',
        'url': 's3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/',
        'type': 'S3_Folder',
        'estimated_gb': 2.1,
        'command': 'aws s3 ls s3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/ --recursive --human-readable'
    },
    'RPCS': {
        'full_name': 'Althea - RPCS',
        'url': 's3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/',
        'type': 'S3_Folder',
        'estimated_gb': 1.8,
        'command': 'aws s3 ls s3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/ --recursive --human-readable'
    },
    'RES': {
        'full_name': 'Nortan - RES Renewables',
        'url': 's3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/',
        'type': 'S3_Folder',
        'estimated_gb': 15.7,
        'command': 'aws s3 ls "s3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/" --recursive --human-readable'
    }
}

# Thesis constraints
THESIS_SIZE_LIMIT_GB = 10.0
OPTIMAL_SIZE_RANGE = (0.5, 5.0)

def run_aws_command(command, project_name):
    """Execute AWS CLI command and return results"""
    print(f"Checking {project_name}...")
    print(f"Command: {command}")
    
    try:
        result = subprocess.run(
            command.split(), 
            capture_output=True, 
            text=True, 
            timeout=60
        )
        
        if result.returncode == 0:
            return {'success': True, 'output': result.stdout, 'error': None}
        else:
            return {'success': False, 'output': None, 'error': result.stderr}
            
    except subprocess.TimeoutExpired:
        return {'success': False, 'output': None, 'error': 'Command timeout'}
    except Exception as e:
        return {'success': False, 'output': None, 'error': str(e)}

def parse_s3_output(output):
    """Parse AWS S3 ls output to extract file information"""
    files = []
    total_size_gb = 0
    
    for line in output.strip().split('\n'):
        if '.las' in line.lower() and line.strip():
            parts = line.strip().split()
            if len(parts) >= 3:
                size_str = parts[2]
                filename = parts[-1].split('/')[-1]
                
                # Convert size to GB
                if 'GiB' in size_str:
                    size_gb = float(size_str.replace('GiB', ''))
                elif 'MiB' in size_str:
                    size_gb = float(size_str.replace('MiB', '')) / 1024
                elif 'KiB' in size_str:
                    size_gb = float(size_str.replace('KiB', '')) / (1024 * 1024)
                else:
                    try:
                        size_bytes = float(size_str)
                        size_gb = size_bytes / (1024**3)
                    except:
                        size_gb = 0
                
                files.append({'filename': filename, 'size_gb': size_gb})
                total_size_gb += size_gb
    
    return files, total_size_gb

def parse_curl_output(output):
    """Parse curl -I output to extract file size"""
    try:
        for line in output.split('\n'):
            if 'content-length:' in line.lower():
                size_bytes = int(line.split(':')[1].strip())
                size_gb = size_bytes / (1024**3)
                return size_gb
        return 0
    except:
        return 0

def assess_thesis_suitability(size_gb, accessible):
    """Determine thesis suitability based on size and accessibility"""
    if not accessible:
        return "Not Accessible", "None", "Skip - Not accessible"
    elif size_gb == 0:
        return "No Data", "None", "Skip - No data found"
    elif size_gb <= 0.1:
        return "Very Small", "Low", "Warning - May lack sufficient data"
    elif size_gb <= OPTIMAL_SIZE_RANGE[1]:
        return "Excellent", "High", "Optimal - Perfect for thesis timeline"
    elif size_gb <= THESIS_SIZE_LIMIT_GB:
        return "Good", "Medium", "Usable - May need optimization"
    else:
        return "Too Large", "Low", "Skip - Too large for thesis timeline"

def main():
    """Main assessment function"""
    print("PROJECT DATA ASSESSMENT FOR THESIS")
    print("=" * 50)
    print("Systematic evaluation of available point cloud datasets")
    print("12-week thesis timeline constraint: datasets must be manageable")
    print()
    
    # Assessment results
    results = []
    
    for project_id, config in PROJECTS.items():
        print(f"\n[{project_id}] {config['full_name']}")
        print(f"Estimated size: {config['estimated_gb']} GB")
        print(f"URL type: {config['type']}")
        
        # Run command
        cmd_result = run_aws_command(config['command'], project_id)
        
        if cmd_result['success']:
            if config['type'] == 'S3_Folder':
                files, actual_size_gb = parse_s3_output(cmd_result['output'])
                file_count = len(files)
                accessible = True
                error_msg = None
                
                print(f"Found {file_count} LAS files, {actual_size_gb:.1f} GB total")
                if files:
                    print("Files:")
                    for file_info in files[:3]:  # Show first 3 files
                        print(f"  - {file_info['filename']}: {file_info['size_gb']:.1f} GB")
                    if len(files) > 3:
                        print(f"  ... and {len(files) - 3} more files")
                        
            else:  # HTTPS_Direct
                actual_size_gb = parse_curl_output(cmd_result['output'])
                file_count = 1 if actual_size_gb > 0 else 0
                accessible = actual_size_gb > 0
                error_msg = None if accessible else "File not accessible"
                files = [{'filename': config['url'].split('/')[-1], 'size_gb': actual_size_gb}] if accessible else []
                
                if accessible:
                    print(f"File accessible, {actual_size_gb:.1f} GB")
                else:
                    print("File not accessible")
        else:
            actual_size_gb = 0
            file_count = 0
            accessible = False
            error_msg = cmd_result['error']
            files = []
            print(f"Error: {error_msg}")
        
        # Assess suitability
        suitability, priority, remarks = assess_thesis_suitability(actual_size_gb, accessible)
        
        # Store results
        results.append({
            'Project': project_id,
            'Full_Name': config['full_name'],
            'URL_Type': config['type'],
            'Estimated_Size_GB': config['estimated_gb'],
            'Actual_Size_GB': round(actual_size_gb, 2),
            'File_Count': file_count,
            'Accessible': accessible,
            'Thesis_Suitability': suitability,
            'Priority': priority,
            'Selection_Remarks': remarks,
            'Error': error_msg or '',
            'Files': files[:3]  # Store first 3 files for reference
        })
    
    # Create assessment matrix
    print("\n\nTHESIS DATASET ASSESSMENT MATRIX")
    print("=" * 80)
    
    df = pd.DataFrame(results)
    display_columns = ['Project', 'Actual_Size_GB', 'File_Count', 'Accessible', 
                      'Thesis_Suitability', 'Priority', 'Selection_Remarks']
    
    print(df[display_columns].to_string(index=False))
    
    # Recommendations
    print("\n\nRECOMMENDATIONS")
    print("=" * 30)
    
    # Filter optimal datasets
    optimal = df[(df['Accessible'] == True) & 
                (df['Actual_Size_GB'] > 0) & 
                (df['Actual_Size_GB'] <= THESIS_SIZE_LIMIT_GB)].sort_values('Actual_Size_GB')
    
    if len(optimal) >= 3:
        selected = optimal.head(3)
        total_size = selected['Actual_Size_GB'].sum()
        
        print("Recommended thesis dataset combination:")
        for i, (_, row) in enumerate(selected.iterrows(), 1):
            print(f"{i}. {row['Project']}: {row['Actual_Size_GB']} GB - {row['Selection_Remarks']}")
        
        print(f"\nTotal combined size: {total_size:.1f} GB")
        print(f"Estimated download time: {total_size * 2:.0f} minutes")
        print("\nBenefits:")
        print("- Multi-site validation for statistical significance")
        print("- Manageable data sizes for 12-week timeline")
        print("- Diverse construction scenarios for robustness")
        
    else:
        print("Limited optimal datasets available.")
        print("Consider chunking larger datasets or adjusting methodology.")
    
    # Export results
    output_file = 'thesis_dataset_assessment.csv'
    df.to_csv(output_file, index=False)
    print(f"\nAssessment matrix exported to: {output_file}")
    
    # Export detailed results as JSON
    detailed_output = 'thesis_dataset_assessment_detailed.json'
    with open(detailed_output, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"Detailed results exported to: {detailed_output}")

if __name__ == "__main__":
    main()
