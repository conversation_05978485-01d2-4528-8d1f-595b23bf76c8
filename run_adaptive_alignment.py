#!/usr/bin/env python3
"""
Run adaptive alignment for point clouds using available reference data.

This script executes the adaptive alignment notebook using Papermill,
automatically selecting the best alignment strategy based on available data.
"""

import argparse
import subprocess
import sys
from pathlib import Path
from datetime import datetime

def run_adaptive_alignment(site_name, project_type="USA", point_cloud_path="", 
                          alignment_strategy="auto", output_dir="output_runs"):
    """
    Run adaptive alignment for a specific site.
    
    Parameters:
    -----------
    site_name : str
        Name of the site to process
    project_type : str
        Project type (ENEL, USA)
    point_cloud_path : str
        Path to specific point cloud file (optional)
    alignment_strategy : str
        Alignment strategy (auto, ifc, cad, gps, relative)
    output_dir : str
        Output directory for results
    """
    
    # Set up paths
    notebook_path = Path("adaptive_alignment_strategy.ipynb")
    if not notebook_path.exists():
        print(f"Error: Notebook not found at {notebook_path}")
        return False
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Generate output notebook name
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_notebook = output_path / f"{site_name}_alignment_{timestamp}_executed.ipynb"
    
    # Prepare papermill parameters
    parameters = {
        'site_name': site_name,
        'project_type': project_type,
        'alignment_strategy': alignment_strategy
    }
    
    if point_cloud_path:
        parameters['point_cloud_path'] = point_cloud_path
    
    # Build papermill command
    cmd = [
        'papermill',
        str(notebook_path),
        str(output_notebook)
    ]
    
    # Add parameters
    for key, value in parameters.items():
        cmd.extend(['-p', key, str(value)])
    
    try:
        print(f"=== Running adaptive alignment for {project_type}/{site_name} ===")
        print(f"Strategy: {alignment_strategy}")
        print(f"Output notebook: {output_notebook}")
        
        # Execute papermill
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        print(f"Alignment completed successfully for {site_name}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Alignment failed for {site_name}: ")
        print(e.stderr)
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Run adaptive point cloud alignment')
    
    parser.add_argument('site_name', help='Name of the site to process')
    parser.add_argument('--project-type', default='USA', choices=['ENEL', 'USA'],
                       help='Project type (default: USA)')
    parser.add_argument('--point-cloud-path', default='',
                       help='Path to specific point cloud file (optional)')
    parser.add_argument('--strategy', default='auto', 
                       choices=['auto', 'ifc', 'cad', 'gps', 'relative'],
                       help='Alignment strategy (default: auto)')
    parser.add_argument('--output-dir', default='output_runs',
                       help='Output directory (default: output_runs)')
    
    args = parser.parse_args()
    
    success = run_adaptive_alignment(
        site_name=args.site_name,
        project_type=args.project_type,
        point_cloud_path=args.point_cloud_path,
        alignment_strategy=args.strategy,
        output_dir=args.output_dir
    )
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
