"""
Classification module for the Solar Panel Point Cloud Analyzer.

This module provides methods for detecting anomalies in solar panel installations:
- Feature-based anomaly detection
- Machine learning models for classification
"""

# Import key functions for easier access (to be implemented)
# from .anomaly_detection import detect_anomalies, get_anomaly_scores
# from .models import load_model, train_model
