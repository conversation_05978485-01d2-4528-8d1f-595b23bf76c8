#!/usr/bin/env python3
"""
Run the complete plane detection and panel segmentation pipeline.

This script runs the complete pipeline:
1. RANSAC plane detection
2. DBSCAN panel segmentation

Usage:
    python run_pipeline.py --input <point_cloud_file> --output <output_dir>
"""

import os
import argparse
import logging
import time
import numpy as np
import open3d as o3d
from typing import Optional

from ransac import detect_multiple_planes, save_planes_data
from dbscan import segment_planes_into_panels

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_point_cloud(file_path: str) -> Optional[np.ndarray]:
    """
    Load a point cloud from a file.
    
    Parameters:
    -----------
    file_path : str
        Path to the point cloud file
        
    Returns:
    --------
    points : numpy.ndarray or None
        Point cloud data (N x 3) or None if loading failed
    """
    try:
        # Check file extension
        ext = os.path.splitext(file_path)[1].lower()
        
        if ext == '.ply':
            # Load PLY file
            pcd = o3d.io.read_point_cloud(file_path)
            points = np.asarray(pcd.points)
        elif ext == '.pcd':
            # Load PCD file
            pcd = o3d.io.read_point_cloud(file_path)
            points = np.asarray(pcd.points)
        elif ext == '.las' or ext == '.laz':
            # Load LAS/LAZ file
            try:
                import laspy
                las = laspy.read(file_path)
                points = np.vstack((las.x, las.y, las.z)).T
            except ImportError:
                logger.error("laspy not installed. Cannot load LAS/LAZ files.")
                return None
        elif ext == '.txt' or ext == '.xyz':
            # Load text file (assuming x,y,z format)
            points = np.loadtxt(file_path, delimiter=',')
            if points.shape[1] > 3:
                points = points[:, :3]  # Take only x,y,z columns
        elif ext == '.npy':
            # Load NumPy file
            points = np.load(file_path)
            if points.shape[1] > 3:
                points = points[:, :3]  # Take only x,y,z columns
        else:
            logger.error(f"Unsupported file format: {ext}")
            return None
        
        logger.info(f"Loaded {points.shape[0]} points from {file_path}")
        return points
    
    except Exception as e:
        logger.error(f"Error loading point cloud: {e}")
        return None

def run_pipeline(
    input_file: str, 
    output_dir: str,
    max_planes: int = 10,
    distance_threshold: float = 0.01,
    ransac_iterations: int = 1000,
    min_inliers: int = 100,
    min_ratio: float = 0.05,
    dbscan_eps: Optional[float] = None,
    dbscan_min_samples: Optional[int] = None,
    use_normals: bool = True,
    normal_weight: float = 0.5,
    save_intermediate: bool = True,
    point_cloud_format: str = 'ply'
) -> bool:
    """
    Run the complete plane detection and panel segmentation pipeline.
    
    Parameters:
    -----------
    input_file : str
        Path to the input point cloud file
    output_dir : str
        Directory to save the output files
    max_planes : int
        Maximum number of planes to detect
    distance_threshold : float
        Maximum distance for a point to be considered an inlier
    ransac_iterations : int
        Number of RANSAC iterations to perform
    min_inliers : int
        Minimum number of inliers required to accept a plane
    min_ratio : float
        Minimum ratio of inliers to remaining points
    dbscan_eps : float, optional
        DBSCAN eps parameter (if None, will be estimated for each plane)
    dbscan_min_samples : int, optional
        DBSCAN min_samples parameter (if None, will be estimated for each plane)
    use_normals : bool
        Whether to use normal vectors for clustering
    normal_weight : float
        Weight of normal vectors in the distance calculation (0-1)
    save_intermediate : bool
        Whether to save intermediate results
    point_cloud_format : str
        Format to save point clouds ('ply', 'pcd', 'las')
        
    Returns:
    --------
    success : bool
        Whether the pipeline completed successfully
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load the point cloud
    points = load_point_cloud(input_file)
    if points is None:
        return False
    
    # Create subdirectories for each stage
    ransac_dir = os.path.join(output_dir, 'ransac')
    dbscan_dir = os.path.join(output_dir, 'dbscan')
    os.makedirs(ransac_dir, exist_ok=True)
    os.makedirs(dbscan_dir, exist_ok=True)
    
    # Save a copy of the original point cloud
    try:
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        o3d.io.write_point_cloud(os.path.join(output_dir, 'point_cloud.ply'), pcd)
    except Exception as e:
        logger.warning(f"Error saving original point cloud: {e}")
    
    # Step 1: RANSAC plane detection
    logger.info("\n=== Step 1: RANSAC Plane Detection ===\n")
    start_time = time.time()
    
    # Detect planes
    detected_planes = detect_multiple_planes(
        points,
        max_planes=max_planes,
        distance_threshold=distance_threshold,
        num_iterations=ransac_iterations,
        min_inliers=min_inliers,
        min_ratio=min_ratio
    )
    
    # Save planes data
    planes_file = save_planes_data(
        detected_planes,
        ransac_dir,
        save_point_clouds=save_intermediate,
        point_cloud_format=point_cloud_format
    )
    
    ransac_time = time.time() - start_time
    logger.info(f"RANSAC completed in {ransac_time:.2f} seconds")
    logger.info(f"Detected {len(detected_planes)} planes")
    
    # Step 2: DBSCAN panel segmentation
    logger.info("\n=== Step 2: DBSCAN Panel Segmentation ===\n")
    start_time = time.time()
    
    # Segment planes into panels
    panels_file = segment_planes_into_panels(
        planes_file,
        dbscan_dir,
        eps=dbscan_eps,
        min_samples=dbscan_min_samples,
        use_normals=use_normals,
        normal_weight=normal_weight,
        save_panels=save_intermediate,
        point_cloud_format=point_cloud_format
    )
    
    dbscan_time = time.time() - start_time
    logger.info(f"DBSCAN completed in {dbscan_time:.2f} seconds")
    
    # Load the panels data to get the number of panels
    panels_data = np.load(panels_file, allow_pickle=True).item()
    num_panels = panels_data['num_panels']
    logger.info(f"Segmented {num_panels} panels")
    
    # Print summary
    logger.info("\n=== Pipeline Summary ===\n")
    logger.info(f"Input file: {input_file}")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Number of points: {points.shape[0]}")
    logger.info(f"Number of planes: {len(detected_planes)}")
    logger.info(f"Number of panels: {num_panels}")
    logger.info(f"RANSAC time: {ransac_time:.2f} seconds")
    logger.info(f"DBSCAN time: {dbscan_time:.2f} seconds")
    logger.info(f"Total time: {ransac_time + dbscan_time:.2f} seconds")
    
    return True

def main():
    """Parse command line arguments and run the pipeline."""
    parser = argparse.ArgumentParser(description='Run the plane detection and panel segmentation pipeline.')
    parser.add_argument('--input', required=True, help='Path to the input point cloud file')
    parser.add_argument('--output', required=True, help='Directory to save the output files')
    parser.add_argument('--max-planes', type=int, default=10, help='Maximum number of planes to detect')
    parser.add_argument('--distance-threshold', type=float, default=0.01, help='Maximum distance for a point to be considered an inlier')
    parser.add_argument('--ransac-iterations', type=int, default=1000, help='Number of RANSAC iterations to perform')
    parser.add_argument('--min-inliers', type=int, default=100, help='Minimum number of inliers required to accept a plane')
    parser.add_argument('--min-ratio', type=float, default=0.05, help='Minimum ratio of inliers to remaining points')
    parser.add_argument('--dbscan-eps', type=float, help='DBSCAN eps parameter (if not provided, will be estimated)')
    parser.add_argument('--dbscan-min-samples', type=int, help='DBSCAN min_samples parameter (if not provided, will be estimated)')
    parser.add_argument('--use-normals', action='store_true', help='Use normal vectors for clustering')
    parser.add_argument('--normal-weight', type=float, default=0.5, help='Weight of normal vectors in the distance calculation (0-1)')
    parser.add_argument('--no-save-intermediate', action='store_true', help='Do not save intermediate results')
    parser.add_argument('--format', choices=['ply', 'pcd', 'las'], default='ply', help='Format to save point clouds')
    
    args = parser.parse_args()
    
    # Run the pipeline
    success = run_pipeline(
        args.input,
        args.output,
        max_planes=args.max_planes,
        distance_threshold=args.distance_threshold,
        ransac_iterations=args.ransac_iterations,
        min_inliers=args.min_inliers,
        min_ratio=args.min_ratio,
        dbscan_eps=args.dbscan_eps,
        dbscan_min_samples=args.dbscan_min_samples,
        use_normals=args.use_normals,
        normal_weight=args.normal_weight,
        save_intermediate=not args.no_save_intermediate,
        point_cloud_format=args.format
    )
    
    return 0 if success else 1

if __name__ == '__main__':
    exit(main())
