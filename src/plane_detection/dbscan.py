"""
DBSCAN-based panel segmentation module for the Solar Panel Point Cloud Analyzer.

This module provides functions for segmenting planes into individual panels
using the Density-Based Spatial Clustering of Applications with Noise (DBSCAN) algorithm.
"""

import numpy as np
import os
import time
import logging
from typing import List, Dict, Tuple, Optional, Any, Union
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors

# Configure logging
logger = logging.getLogger(__name__)

def estimate_dbscan_eps(points: np.ndarray, k: int = 10) -> float:
    """
    Estimate a good eps value for DBSCAN based on k-distance graph.

    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    k : int
        Number of neighbors to consider

    Returns:
    --------
    eps : float
        Estimated eps value for DBSCAN
    """
    # Compute distances to k nearest neighbors
    nbrs = NearestNeighbors(n_neighbors=k).fit(points)
    distances, indices = nbrs.kneighbors(points)

    # Sort the distances to the kth neighbor
    k_distances = np.sort(distances[:, -1])

    # Find the "elbow" in the k-distance graph
    # This is a simple heuristic - more sophisticated methods could be used
    diffs = np.diff(k_distances)
    threshold = np.mean(diffs) + 2 * np.std(diffs)
    elbow_idx = np.where(diffs > threshold)[0]

    if len(elbow_idx) > 0:
        eps = k_distances[elbow_idx[0]]
    else:
        # If no clear elbow is found, use a percentile of the distances
        eps = np.percentile(k_distances, 90)

    logger.info(f"Estimated DBSCAN eps: {eps:.4f}")
    return eps

def compute_point_normals(points: np.ndarray, k: int = 10) -> np.ndarray:
    """
    Compute normal vectors for each point in the point cloud.

    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    k : int
        Number of neighbors to consider for normal estimation

    Returns:
    --------
    normals : numpy.ndarray
        Normal vectors for each point (N x 3)
    """
    try:
        import open3d as o3d

        # Create Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)

        # Estimate normals
        pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamKNN(k))

        # Convert to numpy array
        normals = np.asarray(pcd.normals)

        # Ensure all normals point in the same direction (upward)
        for i in range(len(normals)):
            if normals[i, 2] < 0:
                normals[i] = -normals[i]

        return normals

    except ImportError:
        logger.warning("Open3D not available, using simple normal estimation")

        # Simple normal estimation using PCA on local neighborhoods
        normals = np.zeros((points.shape[0], 3))
        nbrs = NearestNeighbors(n_neighbors=k).fit(points)
        distances, indices = nbrs.kneighbors(points)

        for i in range(points.shape[0]):
            # Get neighbors
            neighbors = points[indices[i]]

            # Compute covariance matrix
            centered = neighbors - np.mean(neighbors, axis=0)
            cov = np.dot(centered.T, centered) / neighbors.shape[0]

            # Compute eigenvectors
            eigenvalues, eigenvectors = np.linalg.eigh(cov)

            # Normal is the eigenvector corresponding to the smallest eigenvalue
            normal = eigenvectors[:, 0]

            # Ensure normal points upward (positive z)
            if normal[2] < 0:
                normal = -normal

            normals[i] = normal

        return normals

def cluster_points_dbscan(
    points: np.ndarray,
    eps: float = 0.1,
    min_samples: int = 10,
    use_normals: bool = True,
    normal_weight: float = 0.5
) -> np.ndarray:
    """
    Cluster points using DBSCAN.

    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    eps : float
        DBSCAN eps parameter (maximum distance between points in a cluster)
    min_samples : int
        DBSCAN min_samples parameter (minimum number of points to form a cluster)
    use_normals : bool
        Whether to use normal vectors for clustering
    normal_weight : float
        Weight of normal vectors in the distance calculation (0-1)

    Returns:
    --------
    labels : numpy.ndarray
        Cluster labels for each point (-1 for noise)
    """
    # If using normals, compute them and create a feature vector
    if use_normals:
        normals = compute_point_normals(points)

        # Create a feature vector combining position and normal
        # Scale normals by normal_weight
        features = np.hstack([
            points,
            normals * normal_weight
        ])
    else:
        features = points

    # Run DBSCAN
    db = DBSCAN(eps=eps, min_samples=min_samples).fit(features)
    labels = db.labels_

    # Count clusters
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    n_noise = list(labels).count(-1)

    logger.info(f"DBSCAN found {n_clusters} clusters and {n_noise} noise points")
    logger.info(f"Noise ratio: {n_noise / len(labels):.2f}")

    return labels

def segment_plane_into_panels(
    plane_points: np.ndarray,
    original_indices: np.ndarray,
    eps: Optional[float] = None,
    min_samples: Optional[int] = None,
    use_normals: bool = True,
    normal_weight: float = 0.5
) -> List[Dict[str, Any]]:
    """
    Segment a plane into individual panels using DBSCAN.

    Parameters:
    -----------
    plane_points : numpy.ndarray
        Points belonging to a plane (N x 3)
    original_indices : numpy.ndarray
        Original indices of the points in the full point cloud
    eps : float, optional
        DBSCAN eps parameter (if None, will be estimated)
    min_samples : int, optional
        DBSCAN min_samples parameter (if None, will be estimated)
    use_normals : bool
        Whether to use normal vectors for clustering
    normal_weight : float
        Weight of normal vectors in the distance calculation (0-1)

    Returns:
    --------
    panels : list of dict
        List of segmented panels, each containing:
        - 'points': Panel points
        - 'original_indices': Original indices of the points
        - 'centroid': Panel centroid
        - 'normal': Panel normal vector
    """
    # Estimate DBSCAN parameters if not provided
    if eps is None:
        eps = estimate_dbscan_eps(plane_points)

    if min_samples is None:
        # Set min_samples based on the number of points
        min_samples = max(10, int(plane_points.shape[0] * 0.01))  # At least 1% of points

    # Cluster the points
    labels = cluster_points_dbscan(
        plane_points,
        eps=eps,
        min_samples=min_samples,
        use_normals=use_normals,
        normal_weight=normal_weight
    )

    # Create a list of panels
    panels = []
    unique_labels = set(labels)

    # Skip noise points (label -1)
    if -1 in unique_labels:
        unique_labels.remove(-1)

    for label in unique_labels:
        # Get points for this panel
        mask = labels == label
        panel_points = plane_points[mask]
        panel_original_indices = original_indices[mask]

        # Compute panel properties
        centroid = np.mean(panel_points, axis=0)

        # Compute normal vector
        normals = compute_point_normals(panel_points)
        normal = np.mean(normals, axis=0)
        normal = normal / np.linalg.norm(normal)  # Normalize

        # Create panel dictionary
        panel = {
            'points': panel_points,
            'original_indices': panel_original_indices,
            'centroid': centroid,
            'normal': normal
        }

        panels.append(panel)

    logger.info(f"Segmented plane into {len(panels)} panels")
    return panels

def segment_planes_into_panels(
    planes_file: str,
    output_dir: str,
    eps: Optional[float] = None,
    min_samples: Optional[int] = None,
    use_normals: bool = True,
    normal_weight: float = 0.5,
    save_panels: bool = True,
    point_cloud_format: str = 'ply'
) -> str:
    """
    Segment all detected planes into individual panels.

    Parameters:
    -----------
    planes_file : str
        Path to the detected planes file (from RANSAC)
    output_dir : str
        Directory to save the output files
    eps : float, optional
        DBSCAN eps parameter (if None, will be estimated for each plane)
    min_samples : int, optional
        DBSCAN min_samples parameter (if None, will be estimated for each plane)
    use_normals : bool
        Whether to use normal vectors for clustering
    normal_weight : float
        Weight of normal vectors in the distance calculation (0-1)
    save_panels : bool
        Whether to save individual point clouds for each panel
    point_cloud_format : str
        Format to save point clouds ('ply', 'pcd', 'las')

    Returns:
    --------
    panels_file : str
        Path to the saved panels data file
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Load the planes data
    planes_data = np.load(planes_file, allow_pickle=True).item()
    num_planes = planes_data['num_planes']
    logger.info(f"Loaded data for {num_planes} planes from {planes_file}")

    # Load the original point cloud if needed
    original_point_cloud = None

    # Process each plane
    all_panels = []
    panel_count = 0

    for i in range(num_planes):
        logger.info(f"\nProcessing plane {i+1}/{num_planes}")

        # Get plane information
        plane_info = planes_data['planes'][i]

        # Get original indices for this plane
        original_indices = np.array(plane_info['original_indices'])

        # Load the plane points
        try:
            # Try to load from the PLY file first
            import open3d as o3d
            plane_file = os.path.join(os.path.dirname(planes_file), f'plane_{i+1}.ply')
            if os.path.exists(plane_file):
                pcd = o3d.io.read_point_cloud(plane_file)
                plane_points = np.asarray(pcd.points)
                logger.info(f"Loaded {plane_points.shape[0]} points from {plane_file}")
            else:
                # If PLY file doesn't exist, try to load from the original point cloud
                if original_point_cloud is None:
                    # Try to find the original point cloud file
                    point_cloud_file = os.path.join(os.path.dirname(os.path.dirname(planes_file)), 'point_cloud.ply')
                    if os.path.exists(point_cloud_file):
                        pcd = o3d.io.read_point_cloud(point_cloud_file)
                        original_point_cloud = np.asarray(pcd.points)
                        logger.info(f"Loaded original point cloud with {original_point_cloud.shape[0]} points")
                    else:
                        raise FileNotFoundError(f"Could not find original point cloud file: {point_cloud_file}")

                # Extract plane points from the original point cloud
                plane_points = original_point_cloud[original_indices]
                logger.info(f"Extracted {plane_points.shape[0]} points from original point cloud")

        except (ImportError, FileNotFoundError) as e:
            logger.warning(f"Error loading plane points: {e}")
            logger.warning("Skipping plane")
            continue

        # Segment the plane into panels
        panels = segment_plane_into_panels(
            plane_points,
            original_indices,
            eps=eps,
            min_samples=min_samples,
            use_normals=use_normals,
            normal_weight=normal_weight
        )

        # Add plane information to each panel
        for panel in panels:
            panel['plane_index'] = i
            panel['plane_equation'] = plane_info['equation']

        # Add panels to the list
        all_panels.extend(panels)
        panel_count += len(panels)

        # Save individual panels if requested
        if save_panels:
            try:
                import open3d as o3d

                for j, panel in enumerate(panels):
                    # Create an Open3D point cloud for the panel
                    panel_pcd = o3d.geometry.PointCloud()
                    panel_pcd.points = o3d.utility.Vector3dVector(panel['points'])

                    # Save the point cloud
                    if point_cloud_format.lower() == 'ply':
                        panel_file = os.path.join(output_dir, f'plane_{i+1}_panel_{j+1}.ply')
                        o3d.io.write_point_cloud(panel_file, panel_pcd)
                    elif point_cloud_format.lower() == 'pcd':
                        panel_file = os.path.join(output_dir, f'plane_{i+1}_panel_{j+1}.pcd')
                        o3d.io.write_point_cloud(panel_file, panel_pcd)
                    else:
                        # Default to PLY
                        panel_file = os.path.join(output_dir, f'plane_{i+1}_panel_{j+1}.ply')
                        o3d.io.write_point_cloud(panel_file, panel_pcd)

                    logger.info(f"Saved panel {j+1} to {panel_file}")

                    # Also save the original indices for each panel
                    indices_file = os.path.join(output_dir, f'plane_{i+1}_panel_{j+1}_indices.npy')
                    np.save(indices_file, panel['original_indices'])
                    logger.info(f"Saved panel {j+1} indices to {indices_file}")

            except ImportError:
                logger.warning("Open3D not available, skipping panel saving")

    # Create a dictionary to store the panel information
    panels_data = {
        'num_planes': num_planes,
        'num_panels': panel_count,
        'panels': [],
        'metadata': {
            'coordinate_system': {
                'description': 'Right-handed coordinate system with Z-axis pointing up',
                'x_axis': 'East',
                'y_axis': 'North',
                'z_axis': 'Up',
                'units': 'meters'
            },
            'creation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    }

    # Add information for each panel
    for i, panel in enumerate(all_panels):
        panel_info = {
            'plane_index': panel['plane_index'],
            'plane_equation': panel['plane_equation'],
            'centroid': panel['centroid'].tolist(),
            'normal': panel['normal'].tolist(),
            'num_points': len(panel['points']),
            'original_indices': panel['original_indices'].tolist()
        }
        panels_data['panels'].append(panel_info)

    # Save the panels data as a NumPy file
    panels_file = os.path.join(output_dir, 'segmented_panels.npy')
    np.save(panels_file, panels_data)
    logger.info(f"\nSaved {panel_count} segmented panels to {panels_file}")

    return panels_file
