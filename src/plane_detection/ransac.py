"""
RANSAC-based plane detection module for the Solar Panel Point Cloud Analyzer.

This module provides functions for detecting planes in point clouds using the
Random Sample Consensus (RANSAC) algorithm.
"""

import numpy as np
import random
import time
import os
import logging
from typing import List, Dict, Tuple, Optional, Any, Union

# Configure logging
logger = logging.getLogger(__name__)

def fit_plane(points: np.ndarray) -> Optional[np.ndarray]:
    """
    Fit a plane to a set of points using least squares.

    Parameters:
    -----------
    points : numpy.ndarray
        Points to fit a plane to (N x 3)

    Returns:
    --------
    plane_params : numpy.ndarray or None
        Plane parameters [a, b, c, d] where ax + by + cz + d = 0
    """
    # Ensure we have at least 3 points
    if points.shape[0] < 3:
        return None

    # Center the points
    centroid = np.mean(points, axis=0)
    centered_points = points - centroid

    # Compute the covariance matrix
    cov = np.dot(centered_points.T, centered_points) / points.shape[0]

    # Compute the eigenvectors and eigenvalues
    eigenvalues, eigenvectors = np.linalg.eigh(cov)

    # The normal vector is the eigenvector corresponding to the smallest eigenvalue
    normal = eigenvectors[:, 0]

    # Ensure the normal vector points upward (positive z)
    if normal[2] < 0:
        normal = -normal

    # Compute the d parameter
    d = -np.dot(normal, centroid)

    # Return the plane parameters [a, b, c, d]
    return np.append(normal, d)

def compute_plane_distance(points: np.ndarray, plane_params: np.ndarray) -> np.ndarray:
    """
    Compute the distance from points to a plane.

    Parameters:
    -----------
    points : numpy.ndarray
        Points to compute distance for (N x 3)
    plane_params : numpy.ndarray
        Plane parameters [a, b, c, d] where ax + by + cz + d = 0

    Returns:
    --------
    distances : numpy.ndarray
        Distances from points to the plane
    """
    # Extract plane parameters
    a, b, c, d = plane_params

    # Compute the distance
    # Distance = |ax + by + cz + d| / sqrt(a^2 + b^2 + c^2)
    numerator = np.abs(np.dot(points, plane_params[:3]) + d)
    denominator = np.sqrt(a**2 + b**2 + c**2)

    return numerator / denominator

def ransac_plane_detection(
    points: np.ndarray,
    distance_threshold: float = 0.01,
    num_iterations: int = 1000,
    min_inliers: int = 100,
    early_stop_ratio: float = 0.8
) -> Tuple[Optional[np.ndarray], np.ndarray]:
    """
    Detect planes in a point cloud using RANSAC.

    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    distance_threshold : float
        Maximum distance for a point to be considered an inlier
    num_iterations : int
        Number of iterations to perform
    min_inliers : int
        Minimum number of inliers required to accept a plane
    early_stop_ratio : float
        Ratio of inliers to total points for early stopping

    Returns:
    --------
    best_plane_params : numpy.ndarray or None
        Parameters of the best plane [a, b, c, d]
    best_inliers : numpy.ndarray
        Indices of inlier points for the best plane
    """
    # Initialize variables
    best_plane_params = None
    best_inliers = np.array([], dtype=int)
    max_inliers = 0
    n_points = points.shape[0]

    # Start timer
    start_time = time.time()

    # Run RANSAC iterations
    for i in range(num_iterations):
        # Randomly select 3 points
        sample_indices = random.sample(range(n_points), 3)
        sample_points = points[sample_indices]

        # Fit a plane to the sampled points
        plane_params = fit_plane(sample_points)
        if plane_params is None:
            continue

        # Compute distances from all points to the plane
        distances = compute_plane_distance(points, plane_params)

        # Find inliers
        inliers = np.where(distances < distance_threshold)[0]
        n_inliers = len(inliers)

        # Update best plane if we found more inliers
        if n_inliers > max_inliers and n_inliers >= min_inliers:
            # Refit the plane using all inliers
            refined_plane_params = fit_plane(points[inliers])
            if refined_plane_params is not None:
                best_plane_params = refined_plane_params
                best_inliers = inliers
                max_inliers = n_inliers

                # Log progress
                if i % 100 == 0 or i == num_iterations - 1:
                    logger.info(f"Iteration {i+1}/{num_iterations}: Found {n_inliers} inliers")

                # Early stopping if we found a very good plane
                inlier_ratio = n_inliers / n_points
                if inlier_ratio > early_stop_ratio:
                    logger.info(f"Early stopping at iteration {i+1}: Found {n_inliers} inliers ({inlier_ratio:.2f} ratio)")
                    break

    # End timer
    end_time = time.time()
    logger.info(f"RANSAC completed in {end_time - start_time:.2f} seconds")
    logger.info(f"Best plane has {max_inliers} inliers ({max_inliers / n_points:.2f} ratio)")

    return best_plane_params, best_inliers

def detect_multiple_planes(
    points: np.ndarray,
    max_planes: int = 10,
    distance_threshold: float = 0.01,
    num_iterations: int = 1000,
    min_inliers: int = 100,
    min_ratio: float = 0.05
) -> List[Dict[str, Any]]:
    """
    Detect multiple planes in a point cloud using RANSAC.

    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    max_planes : int
        Maximum number of planes to detect
    distance_threshold : float
        Maximum distance for a point to be considered an inlier
    num_iterations : int
        Number of iterations to perform for each plane
    min_inliers : int
        Minimum number of inliers required to accept a plane
    min_ratio : float
        Minimum ratio of inliers to remaining points

    Returns:
    --------
    planes : list of dict
        List of detected planes, each containing:
        - 'equation': Plane equation coefficients [a, b, c, d]
        - 'inliers': Indices of inlier points in the original point cloud
        - 'points': Inlier points
    """
    # Make a copy of the points
    remaining_points = np.copy(points)
    original_indices = np.arange(points.shape[0])
    planes = []

    for i in range(max_planes):
        # Check if we have enough points left
        if remaining_points.shape[0] < min_inliers:
            logger.info(f"Stopping after {i} planes: Not enough points left")
            break

        # Detect a plane
        logger.info(f"\nDetecting plane {i+1}/{max_planes}...")
        plane_params, inliers = ransac_plane_detection(
            remaining_points,
            distance_threshold=distance_threshold,
            num_iterations=num_iterations,
            min_inliers=min_inliers
        )

        # Check if we found a plane
        if plane_params is None or len(inliers) < min_inliers:
            logger.info(f"No more planes found with at least {min_inliers} inliers")
            break

        # Check if the plane has enough inliers relative to remaining points
        inlier_ratio = len(inliers) / remaining_points.shape[0]
        if inlier_ratio < min_ratio:
            logger.info(f"Stopping: Plane {i+1} has only {inlier_ratio:.2f} inlier ratio (below {min_ratio})")
            break

        # Get the original indices of the inliers
        original_inliers = original_indices[inliers]

        # Add the plane to the list
        planes.append({
            'equation': plane_params,
            'inliers': original_inliers,
            'points': points[original_inliers]
        })

        # Remove inliers from the remaining points
        mask = np.ones(remaining_points.shape[0], dtype=bool)
        mask[inliers] = False
        remaining_points = remaining_points[mask]
        original_indices = original_indices[mask]

        logger.info(f"Plane {i+1} detected with {len(inliers)} inliers")
        logger.info(f"Remaining points: {remaining_points.shape[0]}")

    logger.info(f"\nDetected {len(planes)} planes in total")
    return planes

def save_planes_data(
    planes: List[Dict[str, Any]],
    output_dir: str,
    save_point_clouds: bool = True,
    point_cloud_format: str = 'ply'
) -> str:
    """
    Save detected planes data to files.

    Parameters:
    -----------
    planes : list of dict
        List of detected planes from detect_multiple_planes
    output_dir : str
        Directory to save the output files
    save_point_clouds : bool
        Whether to save individual point clouds for each plane
    point_cloud_format : str
        Format to save point clouds ('ply', 'pcd', 'las')

    Returns:
    --------
    planes_file : str
        Path to the saved planes data file
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create a dictionary to store the plane information
    planes_data = {
        'num_planes': len(planes),
        'planes': [],
        'metadata': {
            'coordinate_system': {
                'description': 'Right-handed coordinate system with Z-axis pointing up',
                'x_axis': 'East',
                'y_axis': 'North',
                'z_axis': 'Up',
                'units': 'meters'
            },
            'creation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    }

    # Add information for each plane
    for i, plane in enumerate(planes):
        plane_info = {
            'equation': plane['equation'].tolist(),
            'num_inliers': len(plane['inliers']),
            'inlier_ratio': len(plane['inliers']) / (np.sum([len(p['inliers']) for p in planes]) + 1e-10),
            'original_indices': plane['inliers'].tolist()  # Save the original indices
        }
        planes_data['planes'].append(plane_info)

    # Save the planes data as a NumPy file
    planes_file = os.path.join(output_dir, 'detected_planes.npy')
    np.save(planes_file, planes_data)
    logger.info(f"Saved detected planes to {planes_file}")

    # Save each plane's points as a separate file if requested
    if save_point_clouds:
        try:
            import open3d as o3d

            for i, plane in enumerate(planes):
                # Create an Open3D point cloud for the plane
                plane_pcd = o3d.geometry.PointCloud()
                plane_pcd.points = o3d.utility.Vector3dVector(plane['points'])

                # Save the point cloud
                if point_cloud_format.lower() == 'ply':
                    plane_file = os.path.join(output_dir, f'plane_{i+1}.ply')
                    o3d.io.write_point_cloud(plane_file, plane_pcd)
                elif point_cloud_format.lower() == 'pcd':
                    plane_file = os.path.join(output_dir, f'plane_{i+1}.pcd')
                    o3d.io.write_point_cloud(plane_file, plane_pcd)
                else:
                    # Default to PLY
                    plane_file = os.path.join(output_dir, f'plane_{i+1}.ply')
                    o3d.io.write_point_cloud(plane_file, plane_pcd)

                logger.info(f"Saved plane {i+1} to {plane_file}")

                # Also save a JSON file with the original indices for each plane
                indices_file = os.path.join(output_dir, f'plane_{i+1}_indices.npy')
                np.save(indices_file, plane['inliers'])
                logger.info(f"Saved plane {i+1} indices to {indices_file}")

        except ImportError:
            logger.warning("Open3D not available, skipping point cloud saving")

    return planes_file
