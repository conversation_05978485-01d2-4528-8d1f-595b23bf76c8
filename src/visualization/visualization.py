"""
Visualization utilities for the Solar Panel Point Cloud Analyzer.
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D


def visualize_point_cloud(point_cloud, color=None, size=1, ax=None, title=None):
    """
    Visualize a point cloud in 3D.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    color : numpy.ndarray or str
        Color data for each point (N x 3) or a single color
    size : float
        Point size
    ax : matplotlib.axes.Axes
        Axes to plot on
    title : str
        Plot title
        
    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    ax : matplotlib.axes.Axes
        Axes object
    """
    if ax is None:
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
    else:
        fig = ax.figure
    
    # Plot the point cloud
    ax.scatter(
        point_cloud[:, 0],
        point_cloud[:, 1],
        point_cloud[:, 2],
        c=color,
        s=size,
        marker='.'
    )
    
    # Set labels and title
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    
    if title:
        ax.set_title(title)
    
    # Set equal aspect ratio
    max_range = np.max([
        point_cloud[:, 0].max() - point_cloud[:, 0].min(),
        point_cloud[:, 1].max() - point_cloud[:, 1].min(),
        point_cloud[:, 2].max() - point_cloud[:, 2].min()
    ])
    
    mid_x = (point_cloud[:, 0].max() + point_cloud[:, 0].min()) * 0.5
    mid_y = (point_cloud[:, 1].max() + point_cloud[:, 1].min()) * 0.5
    mid_z = (point_cloud[:, 2].max() + point_cloud[:, 2].min()) * 0.5
    
    ax.set_xlim(mid_x - max_range * 0.5, mid_x + max_range * 0.5)
    ax.set_ylim(mid_y - max_range * 0.5, mid_y + max_range * 0.5)
    ax.set_zlim(mid_z - max_range * 0.5, mid_z + max_range * 0.5)
    
    return fig, ax


def visualize_planes(point_cloud, planes, ax=None, title=None, plane_colors=None, plane_alpha=0.5):
    """
    Visualize detected planes in a point cloud.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    planes : list of dict
        List of plane dictionaries, each containing:
        - 'points': Points belonging to the plane
        - 'equation': Plane equation coefficients [a, b, c, d]
    ax : matplotlib.axes.Axes
        Axes to plot on
    title : str
        Plot title
    plane_colors : list
        List of colors for each plane
    plane_alpha : float
        Transparency of the planes
        
    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    ax : matplotlib.axes.Axes
        Axes object
    """
    # First visualize the point cloud
    fig, ax = visualize_point_cloud(point_cloud, ax=ax, title=title)
    
    # Generate colors if not provided
    if plane_colors is None:
        plane_colors = plt.cm.tab10(np.linspace(0, 1, len(planes)))
    
    # Plot each plane
    for i, plane in enumerate(planes):
        # Extract plane equation
        a, b, c, d = plane['equation']
        
        # Extract points belonging to the plane
        plane_points = plane['points']
        
        # Plot the points with the plane color
        ax.scatter(
            plane_points[:, 0],
            plane_points[:, 1],
            plane_points[:, 2],
            c=[plane_colors[i]],
            s=5,
            marker='.'
        )
        
        # Create a mesh grid for the plane
        x_min, x_max = plane_points[:, 0].min(), plane_points[:, 0].max()
        y_min, y_max = plane_points[:, 1].min(), plane_points[:, 1].max()
        
        xx, yy = np.meshgrid(np.linspace(x_min, x_max, 10), np.linspace(y_min, y_max, 10))
        
        # Calculate z values for the plane
        if c != 0:
            zz = (-a * xx - b * yy - d) / c
        else:
            # Handle vertical planes
            zz = np.zeros_like(xx)
        
        # Plot the plane
        ax.plot_surface(xx, yy, zz, alpha=plane_alpha, color=plane_colors[i])
    
    return fig, ax


def visualize_alignment(source, target, aligned_source=None, ax=None, title=None):
    """
    Visualize the alignment of two point clouds.
    
    Parameters:
    -----------
    source : numpy.ndarray
        Source point cloud (N x 3)
    target : numpy.ndarray
        Target point cloud (M x 3)
    aligned_source : numpy.ndarray or None
        Aligned source point cloud (N x 3)
    ax : matplotlib.axes.Axes
        Axes to plot on
    title : str
        Plot title
        
    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    ax : matplotlib.axes.Axes
        Axes object
    """
    if ax is None:
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
    else:
        fig = ax.figure
    
    # Plot the target point cloud
    ax.scatter(
        target[:, 0],
        target[:, 1],
        target[:, 2],
        c='blue',
        s=1,
        marker='.',
        label='Target'
    )
    
    # Plot the source point cloud
    ax.scatter(
        source[:, 0],
        source[:, 1],
        source[:, 2],
        c='red',
        s=1,
        marker='.',
        label='Source'
    )
    
    # Plot the aligned source point cloud if provided
    if aligned_source is not None:
        ax.scatter(
            aligned_source[:, 0],
            aligned_source[:, 1],
            aligned_source[:, 2],
            c='green',
            s=1,
            marker='.',
            label='Aligned Source'
        )
    
    # Set labels and title
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    
    if title:
        ax.set_title(title)
    
    ax.legend()
    
    return fig, ax


def visualize_anomaly_heatmap(point_cloud, planes, anomaly_scores, ax=None, title=None, cmap='viridis'):
    """
    Visualize anomaly scores as a heatmap on the point cloud.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    planes : list of dict
        List of plane dictionaries
    anomaly_scores : numpy.ndarray
        Anomaly scores for each plane
    ax : matplotlib.axes.Axes
        Axes to plot on
    title : str
        Plot title
    cmap : str
        Colormap name
        
    Returns:
    --------
    fig : matplotlib.figure.Figure
        Figure object
    ax : matplotlib.axes.Axes
        Axes object
    """
    if ax is None:
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
    else:
        fig = ax.figure
    
    # Normalize anomaly scores
    normalized_scores = (anomaly_scores - anomaly_scores.min()) / (anomaly_scores.max() - anomaly_scores.min())
    
    # Create a colormap
    colormap = plt.cm.get_cmap(cmap)
    
    # Plot each plane with its anomaly score
    for i, (plane, score) in enumerate(zip(planes, normalized_scores)):
        # Extract points belonging to the plane
        plane_points = plane['points']
        
        # Get color from colormap
        color = colormap(score)
        
        # Plot the points with the color
        ax.scatter(
            plane_points[:, 0],
            plane_points[:, 1],
            plane_points[:, 2],
            c=[color],
            s=5,
            marker='.'
        )
    
    # Add a colorbar
    sm = plt.cm.ScalarMappable(cmap=colormap)
    sm.set_array(normalized_scores)
    plt.colorbar(sm, ax=ax, label='Anomaly Score')
    
    # Set labels and title
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    
    if title:
        ax.set_title(title)
    
    return fig, ax


def save_visualization(fig, file_path, dpi=300):
    """
    Save a visualization to a file.
    
    Parameters:
    -----------
    fig : matplotlib.figure.Figure
        Figure to save
    file_path : str
        Path to save the figure
    dpi : int
        Resolution in dots per inch
    """
    fig.savefig(file_path, dpi=dpi, bbox_inches='tight')
