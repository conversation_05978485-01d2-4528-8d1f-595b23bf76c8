"""
Point cloud alignment module.

This module provides methods for aligning point clouds using various approaches:
- Iterative Closest Point (ICP)
- Neural Network-based alignment
- Hybrid approach combining ICP and Neural Networks
"""

# Import key functions for easier access
from .icp import align_point_clouds_icp
from .neural_network import align_point_clouds_nn
from .hybrid import align_point_clouds_hybrid
