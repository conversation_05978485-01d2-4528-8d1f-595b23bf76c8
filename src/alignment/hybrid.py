"""
Hybrid approach combining neural network and ICP for point cloud alignment.
"""

import numpy as np
from .icp import align_point_clouds_icp
from .neural_network import align_point_clouds_nn


def align_point_clouds_hybrid(source, target, nn_first=True, icp_max_iterations=20, icp_tolerance=1e-6, model=None, num_points=1024):
    """
    Align source point cloud to target point cloud using a hybrid approach.
    
    Parameters:
    -----------
    source : numpy.ndarray
        Source point cloud (N x 3)
    target : numpy.ndarray
        Target point cloud (M x 3)
    nn_first : bool
        Whether to use neural network for initial alignment (True) or ICP (False)
    icp_max_iterations : int
        Maximum number of iterations for ICP
    icp_tolerance : float
        Convergence tolerance for ICP
    model : tf.keras.Model or None
        Pre-trained neural network model for alignment
    num_points : int
        Number of points to use for neural network alignment
        
    Returns:
    --------
    aligned_source : numpy.ndarray
        Aligned source point cloud
    transformation : dict
        Dictionary containing the rotation and translation
    """
    if nn_first:
        # Step 1: Use neural network for initial alignment
        nn_aligned_source, nn_transformation = align_point_clouds_nn(
            source, target, model=model, num_points=num_points
        )
        
        # Step 2: Refine with ICP
        icp_aligned_source, icp_transformation = align_point_clouds_icp(
            nn_aligned_source, target, 
            max_iterations=icp_max_iterations, 
            tolerance=icp_tolerance
        )
        
        # Extract final rotation and translation
        R_nn = nn_transformation['rotation_matrix']
        t_nn = nn_transformation['translation_vector']
        
        R_icp = icp_transformation[:3, :3]
        t_icp = icp_transformation[:3, 3]
        
        # Combine transformations
        R_final = np.dot(R_icp, R_nn)
        t_final = np.dot(R_icp, t_nn) + t_icp
        
    else:
        # Step 1: Use ICP for initial alignment
        icp_aligned_source, icp_transformation = align_point_clouds_icp(
            source, target, 
            max_iterations=icp_max_iterations, 
            tolerance=icp_tolerance
        )
        
        # Step 2: Refine with neural network
        nn_aligned_source, nn_transformation = align_point_clouds_nn(
            icp_aligned_source, target, model=model, num_points=num_points
        )
        
        # Extract final rotation and translation
        R_icp = icp_transformation[:3, :3]
        t_icp = icp_transformation[:3, 3]
        
        R_nn = nn_transformation['rotation_matrix']
        t_nn = nn_transformation['translation_vector']
        
        # Combine transformations
        R_final = np.dot(R_nn, R_icp)
        t_final = np.dot(R_nn, t_icp) + t_nn
    
    # Create final transformation dictionary
    transformation = {
        'rotation_matrix': R_final,
        'translation_vector': t_final,
        'method': 'hybrid_nn_icp' if nn_first else 'hybrid_icp_nn'
    }
    
    # Apply final transformation to the original source
    aligned_source = np.dot(source, R_final.T) + t_final
    
    return aligned_source, transformation
