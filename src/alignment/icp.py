"""
Iterative Closest Point (ICP) implementation for point cloud alignment.
"""

import numpy as np
from sklearn.neighbors import NearestNeighbors


def best_fit_transform(source, target):
    """
    Compute the best-fit transformation that maps source to target.
    
    Parameters:
    -----------
    source : numpy.ndarray
        Source points (N x 3)
    target : numpy.ndarray
        Target points (N x 3)
        
    Returns:
    --------
    T : numpy.ndarray
        4 x 4 homogeneous transformation matrix
    R : numpy.ndarray
        3 x 3 rotation matrix
    t : numpy.ndarray
        3 x 1 translation vector
    """
    # Center the points
    source_centroid = np.mean(source, axis=0)
    target_centroid = np.mean(target, axis=0)
    source_centered = source - source_centroid
    target_centered = target - target_centroid
    
    # Compute the covariance matrix
    H = np.dot(source_centered.T, target_centered)
    
    # Compute the SVD
    U, S, Vt = np.linalg.svd(H)
    
    # Compute the rotation matrix
    R = np.dot(Vt.T, U.T)
    
    # Special reflection case
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)
    
    # Compute the translation
    t = target_centroid - np.dot(R, source_centroid)
    
    # Create the transformation matrix
    T = np.identity(4)
    T[:3, :3] = R
    T[:3, 3] = t
    
    return T, R, t


def nearest_neighbor(source, target):
    """
    Find the nearest neighbor for each source point in the target.
    
    Parameters:
    -----------
    source : numpy.ndarray
        Source points (N x 3)
    target : numpy.ndarray
        Target points (M x 3)
        
    Returns:
    --------
    distances : numpy.ndarray
        Distances to the nearest neighbors
    indices : numpy.ndarray
        Indices of the nearest neighbors
    """
    # Create a nearest neighbor search object
    nn = NearestNeighbors(n_neighbors=1, algorithm='kd_tree').fit(target)
    
    # Find the nearest neighbors
    distances, indices = nn.kneighbors(source)
    
    return distances.ravel(), indices.ravel()


def align_point_clouds_icp(source, target, max_iterations=20, tolerance=1e-6):
    """
    Align source point cloud to target point cloud using ICP.
    
    Parameters:
    -----------
    source : numpy.ndarray
        Source point cloud (N x 3)
    target : numpy.ndarray
        Target point cloud (M x 3)
    max_iterations : int
        Maximum number of iterations
    tolerance : float
        Convergence tolerance
        
    Returns:
    --------
    aligned_source : numpy.ndarray
        Aligned source point cloud
    T : numpy.ndarray
        4 x 4 homogeneous transformation matrix
    """
    # Make a copy of the source point cloud
    source_copy = np.copy(source)
    
    # Initialize the transformation matrix
    T_final = np.identity(4)
    
    # Run ICP for max_iterations or until convergence
    prev_error = float('inf')
    for i in range(max_iterations):
        # Find the nearest neighbors
        distances, indices = nearest_neighbor(source_copy, target)
        
        # Compute the mean error
        mean_error = np.mean(distances)
        
        # Check for convergence
        if abs(prev_error - mean_error) < tolerance:
            break
        
        prev_error = mean_error
        
        # Compute the best-fit transformation
        T, R, t = best_fit_transform(source_copy, target[indices])
        
        # Update the source point cloud
        source_copy = np.dot(source_copy, R.T) + t
        
        # Update the transformation matrix
        T_final = np.dot(T, T_final)
    
    # Extract the final rotation and translation
    R_final = T_final[:3, :3]
    t_final = T_final[:3, 3]
    
    # Apply the final transformation to the original source
    aligned_source = np.dot(source, R_final.T) + t_final
    
    return aligned_source, T_final
