"""
Neural network-based point cloud alignment.
"""

import numpy as np
import tensorflow as tf
import transforms3d.euler as t3d_euler
import transforms3d.quaternions as t3d_quaternions


def create_point_net_feature_extractor(num_points=1024):
    """
    Create a PointNet-style feature extractor.
    
    Parameters:
    -----------
    num_points : int
        Number of points in the input point cloud
        
    Returns:
    --------
    model : tf.keras.Model
        PointNet feature extractor model
    """
    inputs = tf.keras.layers.Input(shape=(num_points, 3))
    
    # MLP on each point
    x = tf.keras.layers.Conv1D(64, 1, activation='relu')(inputs)
    x = tf.keras.layers.BatchNormalization()(x)
    x = tf.keras.layers.Conv1D(128, 1, activation='relu')(x)
    x = tf.keras.layers.BatchNormalization()(x)
    x = tf.keras.layers.Conv1D(256, 1, activation='relu')(x)
    x = tf.keras.layers.BatchNormalization()(x)
    
    # Global feature
    x = tf.keras.layers.GlobalMaxPooling1D()(x)
    
    # Feature expansion
    x = tf.keras.layers.Dense(512, activation='relu')(x)
    x = tf.keras.layers.BatchNormalization()(x)
    x = tf.keras.layers.Dense(256, activation='relu')(x)
    x = tf.keras.layers.BatchNormalization()(x)
    
    model = tf.keras.Model(inputs=inputs, outputs=x)
    return model


def create_alignment_model(num_points=1024, use_quaternion=True):
    """
    Create a neural network model for point cloud alignment.
    
    Parameters:
    -----------
    num_points : int
        Number of points in the input point clouds
    use_quaternion : bool
        Whether to use quaternion representation for rotation
        
    Returns:
    --------
    model : tf.keras.Model
        Neural network model for point cloud alignment
    """
    # Input layers
    source_input = tf.keras.layers.Input(shape=(num_points, 3), name='source_input')
    target_input = tf.keras.layers.Input(shape=(num_points, 3), name='target_input')
    
    # Feature extraction (shared weights)
    feature_extractor = create_point_net_feature_extractor(num_points)
    source_features = feature_extractor(source_input)
    target_features = feature_extractor(target_input)
    
    # Concatenate features
    combined_features = tf.keras.layers.Concatenate()([source_features, target_features])
    
    # MLP for regression
    x = tf.keras.layers.Dense(256, activation='relu')(combined_features)
    x = tf.keras.layers.BatchNormalization()(x)
    x = tf.keras.layers.Dense(128, activation='relu')(x)
    x = tf.keras.layers.BatchNormalization()(x)
    
    # Output layers
    translation = tf.keras.layers.Dense(3, name='translation')(x)
    
    if use_quaternion:
        # Quaternion output (4 parameters)
        rotation = tf.keras.layers.Dense(4, name='quaternion')(x)
        # Normalize quaternion
        rotation = tf.keras.layers.Lambda(
            lambda q: q / tf.norm(q, axis=-1, keepdims=True)
        )(rotation)
    else:
        # Euler angles output (3 parameters)
        rotation = tf.keras.layers.Dense(3, name='euler')(x)
    
    model = tf.keras.Model(
        inputs=[source_input, target_input],
        outputs=[rotation, translation]
    )
    
    return model


def quaternion_distance_loss(y_true, y_pred):
    """
    Compute the distance between two quaternions.
    
    Parameters:
    -----------
    y_true : tf.Tensor
        True quaternions
    y_pred : tf.Tensor
        Predicted quaternions
        
    Returns:
    --------
    loss : tf.Tensor
        Quaternion distance loss
    """
    # Normalize quaternions
    q_true = y_true / tf.norm(y_true, axis=-1, keepdims=True)
    q_pred = y_pred / tf.norm(y_pred, axis=-1, keepdims=True)
    
    # Compute dot product
    dot_product = tf.reduce_sum(q_true * q_pred, axis=-1)
    
    # Compute angular distance
    distance = 1.0 - tf.abs(dot_product)
    
    return distance


def align_point_clouds_nn(source, target, model=None, num_points=1024):
    """
    Align source point cloud to target point cloud using a neural network.
    
    Parameters:
    -----------
    source : numpy.ndarray
        Source point cloud (N x 3)
    target : numpy.ndarray
        Target point cloud (M x 3)
    model : tf.keras.Model or None
        Pre-trained neural network model for alignment
    num_points : int
        Number of points to use for alignment
        
    Returns:
    --------
    aligned_source : numpy.ndarray
        Aligned source point cloud
    transformation : dict
        Dictionary containing the rotation and translation
    """
    # Load or create the model
    if model is None:
        model = create_alignment_model(num_points=num_points)
        # Load pre-trained weights (this is a placeholder)
        # model.load_weights('path/to/pretrained/weights')
    
    # Sample points if necessary
    if source.shape[0] > num_points:
        indices = np.random.choice(source.shape[0], num_points, replace=False)
        source_sample = source[indices]
    else:
        source_sample = source
    
    if target.shape[0] > num_points:
        indices = np.random.choice(target.shape[0], num_points, replace=False)
        target_sample = target[indices]
    else:
        target_sample = target
    
    # Normalize point clouds
    source_centroid = np.mean(source_sample, axis=0)
    target_centroid = np.mean(target_sample, axis=0)
    source_sample = source_sample - source_centroid
    target_sample = target_sample - target_centroid
    
    # Predict transformation
    source_batch = np.expand_dims(source_sample, axis=0)
    target_batch = np.expand_dims(target_sample, axis=0)
    rotation, translation = model.predict([source_batch, target_batch])
    
    # Extract rotation and translation
    quaternion = rotation[0]
    translation = translation[0]
    
    # Convert quaternion to rotation matrix
    R = t3d_quaternions.quat2mat(quaternion)
    
    # Adjust translation for the centroids
    t = translation + target_centroid - np.dot(R, source_centroid)
    
    # Apply transformation to the original source
    aligned_source = np.dot(source - source_centroid, R.T) + source_centroid + t
    
    # Create transformation dictionary
    transformation = {
        'rotation_matrix': R,
        'translation_vector': t,
        'quaternion': quaternion
    }
    
    return aligned_source, transformation
