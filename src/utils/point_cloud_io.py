"""
Utility functions for reading and writing point cloud data in various formats.
"""

import os
import numpy as np
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    import open3d as o3d
    O3D_SUPPORT = True
    logger.info("Open3D support is available.")
except ImportError:
    O3D_SUPPORT = False
    logger.warning("Open3D not installed. Some functionality may be limited.")

try:
    import laspy
    LASPY_SUPPORT = True
    logger.info("LASpy support is available.")
except ImportError:
    LASPY_SUPPORT = False
    logger.warning("laspy not installed. LAS/LAZ file support will be limited.")


def save_point_cloud(points, file_path, format=None, colors=None, normals=None, metadata=None):
    """
    Save a point cloud to a file in the specified format.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    file_path : str
        Path to save the point cloud
    format : str, optional
        Force a specific file format ('las', 'ply', 'pcd')
        If None, will be determined from file extension
    colors : numpy.ndarray, optional
        Point colors (N x 3), values should be in range [0, 1]
    normals : numpy.ndarray, optional
        Point normals (N x 3)
    metadata : dict, optional
        Additional metadata to include in the file
        
    Returns:
    --------
    bool
        True if successful, False otherwise
    """
    # Determine format if not specified
    if format is None:
        ext = os.path.splitext(file_path)[1].lower()
        if ext == '.las' or ext == '.laz':
            format = 'las'
        elif ext == '.ply':
            format = 'ply'
        elif ext == '.pcd':
            format = 'pcd'
        else:
            logger.warning(f"Unsupported file extension: {ext}. Defaulting to PLY format.")
            format = 'ply'
            if '.' not in file_path:
                file_path += '.ply'
    
    # Ensure points is a numpy array
    points = np.asarray(points)
    
    # Validate input
    if points.shape[1] != 3:
        logger.error("Points must be a Nx3 array.")
        return False
    
    if colors is not None and len(colors) != len(points):
        logger.error("Number of colors must match number of points.")
        return False
    
    if normals is not None and len(normals) != len(points):
        logger.error("Number of normals must match number of points.")
        return False
    
    # Save based on format
    try:
        if format == 'las':
            return save_to_las(points, file_path, colors, normals, metadata)
        elif format == 'ply' or format == 'pcd':
            return save_to_o3d_format(points, file_path, colors, normals)
        else:
            logger.error(f"Unsupported format: {format}")
            return False
    except Exception as e:
        logger.error(f"Error saving point cloud to {file_path}: {e}")
        return False


def save_to_las(points, file_path, colors=None, normals=None, metadata=None):
    """
    Save a point cloud to a LAS file.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    file_path : str
        Path to save the LAS file
    colors : numpy.ndarray, optional
        Point colors (N x 3), values should be in range [0, 1]
    normals : numpy.ndarray, optional
        Point normals (N x 3)
    metadata : dict, optional
        Additional metadata to include in the file
        
    Returns:
    --------
    bool
        True if successful, False otherwise
    """
    if not LASPY_SUPPORT:
        logger.error("LASpy is required for saving LAS files. Install it with 'pip install laspy'.")
        return False
    
    try:
        # Create a new LAS file
        header = laspy.LasHeader(point_format=3, version="1.4")
        
        # Set coordinate reference system if provided
        if metadata and 'crs' in metadata:
            header.add_crs(metadata['crs'])
        
        # Set scale and offset for better precision
        if metadata and 'scale' in metadata:
            header.scales = metadata['scale']
        else:
            # Default scale for meter-level precision
            header.scales = [0.001, 0.001, 0.001]
        
        if metadata and 'offset' in metadata:
            header.offsets = metadata['offset']
        else:
            # Calculate appropriate offset from data
            header.offsets = [
                np.min(points[:, 0]),
                np.min(points[:, 1]),
                np.min(points[:, 2])
            ]
        
        # Create LAS data
        las = laspy.LasData(header)
        
        # Set coordinates
        las.x = points[:, 0]
        las.y = points[:, 1]
        las.z = points[:, 2]
        
        # Set colors if provided
        if colors is not None:
            # Convert from [0,1] to [0,65535] for LAS format
            rgb = (colors * 65535).astype(np.uint16)
            las.red = rgb[:, 0]
            las.green = rgb[:, 1]
            las.blue = rgb[:, 2]
        
        # Set normals if provided and supported by point format
        if normals is not None and header.point_format.id >= 8:
            # LAS 1.4 with point format 8+ supports normals
            las.nx = normals[:, 0]
            las.ny = normals[:, 1]
            las.nz = normals[:, 2]
        elif normals is not None:
            logger.warning("Normals provided but not supported by LAS point format. Normals will not be saved.")
        
        # Add additional metadata as VLRs if provided
        if metadata and 'vlrs' in metadata:
            for vlr in metadata['vlrs']:
                las.vlrs.append(vlr)
        
        # Add custom metadata as a VLR
        if metadata and 'custom' in metadata:
            # Create a custom VLR for our metadata
            # This is a simplified approach - in a real implementation you'd want to
            # serialize the metadata properly
            try:
                import json
                metadata_str = json.dumps(metadata['custom']).encode('utf-8')
                
                # Create a VLR with our metadata
                vlr = laspy.VLR(
                    user_id="SOLAR_PANEL",
                    record_id=1,
                    description="Solar Panel Analyzer Metadata",
                    record_data=metadata_str
                )
                las.vlrs.append(vlr)
            except Exception as e:
                logger.warning(f"Failed to add custom metadata as VLR: {e}")
        
        # Write the LAS file
        las.write(file_path)
        logger.info(f"Saved point cloud with {len(points)} points to {file_path}")
        return True
    
    except Exception as e:
        logger.error(f"Error saving to LAS file {file_path}: {e}")
        return False


def save_to_o3d_format(points, file_path, colors=None, normals=None):
    """
    Save a point cloud to a PLY or PCD file using Open3D.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud data (N x 3)
    file_path : str
        Path to save the point cloud
    colors : numpy.ndarray, optional
        Point colors (N x 3), values should be in range [0, 1]
    normals : numpy.ndarray, optional
        Point normals (N x 3)
        
    Returns:
    --------
    bool
        True if successful, False otherwise
    """
    if not O3D_SUPPORT:
        logger.error("Open3D is required for saving PLY/PCD files. Install it with 'pip install open3d'.")
        return False
    
    try:
        # Create Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Add colors if provided
        if colors is not None:
            pcd.colors = o3d.utility.Vector3dVector(colors)
        
        # Add normals if provided
        if normals is not None:
            pcd.normals = o3d.utility.Vector3dVector(normals)
        
        # Save to file
        o3d.io.write_point_cloud(file_path, pcd)
        logger.info(f"Saved point cloud with {len(points)} points to {file_path}")
        return True
    
    except Exception as e:
        logger.error(f"Error saving to file {file_path}: {e}")
        return False


def load_point_cloud(file_path, format=None):
    """
    Load a point cloud from a file.
    
    Parameters:
    -----------
    file_path : str
        Path to the point cloud file
    format : str, optional
        Force a specific file format ('las', 'ply', 'pcd')
        If None, will be determined from file extension
        
    Returns:
    --------
    points : numpy.ndarray
        Point cloud data (N x 3)
    colors : numpy.ndarray or None
        Point colors (N x 3) if available
    normals : numpy.ndarray or None
        Point normals (N x 3) if available
    metadata : dict
        Additional metadata from the file
    """
    # Implementation would go here
    # This is a placeholder - we're focusing on the save functionality for now
    logger.warning("load_point_cloud function is not fully implemented yet.")
    return None, None, None, {}
