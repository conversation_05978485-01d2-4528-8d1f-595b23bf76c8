"""
Data loading utilities for the Solar Panel Point Cloud Analyzer.
"""

import numpy as np
import os


def load_point_cloud(file_path):
    """
    Load a point cloud from a file.
    
    Parameters:
    -----------
    file_path : str
        Path to the point cloud file
        
    Returns:
    --------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    """
    file_extension = os.path.splitext(file_path)[1].lower()
    
    if file_extension == '.obj':
        return load_from_obj(file_path)
    elif file_extension == '.ply':
        return load_from_ply(file_path)
    elif file_extension == '.las' or file_extension == '.laz':
        return load_from_las(file_path)
    elif file_extension == '.pcd':
        return load_from_pcd(file_path)
    elif file_extension == '.ifc':
        return load_from_ifc(file_path)
    else:
        raise ValueError(f"Unsupported file format: {file_extension}")


def load_from_obj(file_path):
    """
    Load a point cloud from an OBJ file.
    
    Parameters:
    -----------
    file_path : str
        Path to the OBJ file
        
    Returns:
    --------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    """
    vertices = []
    
    with open(file_path, 'r') as f:
        for line in f:
            if line.startswith('v '):
                parts = line.strip().split()
                if len(parts) >= 4:  # v x y z
                    vertex = [float(parts[1]), float(parts[2]), float(parts[3])]
                    vertices.append(vertex)
    
    return np.array(vertices)


def load_from_ply(file_path):
    """
    Load a point cloud from a PLY file.
    
    Parameters:
    -----------
    file_path : str
        Path to the PLY file
        
    Returns:
    --------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    """
    # Placeholder implementation
    # In a real implementation, you would use a library like Open3D or PyMesh
    raise NotImplementedError("PLY loading not yet implemented")


def load_from_las(file_path):
    """
    Load a point cloud from a LAS/LAZ file.
    
    Parameters:
    -----------
    file_path : str
        Path to the LAS/LAZ file
        
    Returns:
    --------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    """
    try:
        import laspy
        
        with laspy.open(file_path) as f:
            las = f.read()
            points = np.vstack((las.x, las.y, las.z)).transpose()
            return points
    except ImportError:
        raise ImportError("laspy is required for loading LAS/LAZ files. Install it with 'pip install laspy'.")


def load_from_pcd(file_path):
    """
    Load a point cloud from a PCD file.
    
    Parameters:
    -----------
    file_path : str
        Path to the PCD file
        
    Returns:
    --------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    """
    # Placeholder implementation
    # In a real implementation, you would use a library like Open3D
    raise NotImplementedError("PCD loading not yet implemented")


def load_from_ifc(file_path):
    """
    Load a point cloud from an IFC file.
    
    Parameters:
    -----------
    file_path : str
        Path to the IFC file
        
    Returns:
    --------
    point_cloud : numpy.ndarray
        Point cloud data (N x 3)
    """
    try:
        import ifcopenshell
        import ifcopenshell.geom
        
        # Set up the settings
        settings = ifcopenshell.geom.settings()
        settings.set(settings.USE_WORLD_COORDS, True)
        
        # Load the IFC file
        ifc_file = ifcopenshell.open(file_path)
        
        # Extract geometry
        vertices = []
        
        # Process all products
        products = ifc_file.by_type("IfcProduct")
        for product in products:
            if product.Representation:
                shape = ifcopenshell.geom.create_shape(settings, product)
                verts = shape.geometry.verts
                vertices.extend([verts[i:i+3] for i in range(0, len(verts), 3)])
        
        return np.array(vertices)
    except ImportError:
        raise ImportError("ifcopenshell is required for loading IFC files. Install it with 'pip install ifcopenshell'.")
