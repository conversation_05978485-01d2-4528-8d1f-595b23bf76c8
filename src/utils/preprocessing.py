"""
Point cloud preprocessing utilities for the Solar Panel Point Cloud Analyzer.
"""

import numpy as np
from sklearn.neighbors import NearestNeighbors


def preprocess_point_cloud(point_cloud, downsample=True, voxel_size=0.05, normalize=True, remove_outliers=False, outlier_std=2.0):
    """
    Preprocess a point cloud for analysis.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Input point cloud (N x 3)
    downsample : bool
        Whether to downsample the point cloud
    voxel_size : float
        Voxel size for downsampling
    normalize : bool
        Whether to normalize the point cloud
    remove_outliers : bool
        Whether to remove outliers
    outlier_std : float
        Standard deviation threshold for outlier removal
        
    Returns:
    --------
    processed_cloud : numpy.ndarray
        Processed point cloud
    """
    processed_cloud = point_cloud.copy()
    
    # Remove outliers
    if remove_outliers:
        processed_cloud = remove_statistical_outliers(processed_cloud, outlier_std)
    
    # Downsample
    if downsample:
        processed_cloud = downsample_voxel_grid(processed_cloud, voxel_size)
    
    # Normalize
    if normalize:
        processed_cloud = normalize_point_cloud(processed_cloud)
    
    return processed_cloud


def normalize_point_cloud(point_cloud):
    """
    Normalize a point cloud to have zero mean and unit variance.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Input point cloud (N x 3)
        
    Returns:
    --------
    normalized_cloud : numpy.ndarray
        Normalized point cloud
    """
    # Compute centroid
    centroid = np.mean(point_cloud, axis=0)
    
    # Center the point cloud
    centered_cloud = point_cloud - centroid
    
    # Compute scale
    scale = np.max(np.linalg.norm(centered_cloud, axis=1))
    
    # Scale the point cloud
    normalized_cloud = centered_cloud / scale
    
    return normalized_cloud


def downsample_voxel_grid(point_cloud, voxel_size):
    """
    Downsample a point cloud using voxel grid downsampling.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Input point cloud (N x 3)
    voxel_size : float
        Size of the voxel grid
        
    Returns:
    --------
    downsampled_cloud : numpy.ndarray
        Downsampled point cloud
    """
    # Compute voxel indices for each point
    voxel_indices = np.floor(point_cloud / voxel_size).astype(int)
    
    # Create a dictionary to store points in each voxel
    voxel_dict = {}
    for i, idx in enumerate(voxel_indices):
        voxel_key = tuple(idx)
        if voxel_key in voxel_dict:
            voxel_dict[voxel_key].append(i)
        else:
            voxel_dict[voxel_key] = [i]
    
    # Compute the centroid of each voxel
    downsampled_points = []
    for voxel_key, point_indices in voxel_dict.items():
        voxel_points = point_cloud[point_indices]
        centroid = np.mean(voxel_points, axis=0)
        downsampled_points.append(centroid)
    
    return np.array(downsampled_points)


def remove_statistical_outliers(point_cloud, std_ratio=2.0, k=20):
    """
    Remove outliers from a point cloud using statistical analysis.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Input point cloud (N x 3)
    std_ratio : float
        Standard deviation ratio for outlier removal
    k : int
        Number of neighbors to consider
        
    Returns:
    --------
    filtered_cloud : numpy.ndarray
        Filtered point cloud
    """
    # Find k nearest neighbors for each point
    nn = NearestNeighbors(n_neighbors=k+1).fit(point_cloud)
    distances, _ = nn.kneighbors(point_cloud)
    
    # Compute the average distance to k nearest neighbors
    avg_distances = np.mean(distances[:, 1:], axis=1)
    
    # Compute the global mean and standard deviation
    global_mean = np.mean(avg_distances)
    global_std = np.std(avg_distances)
    
    # Define the threshold
    threshold = global_mean + std_ratio * global_std
    
    # Filter points
    mask = avg_distances < threshold
    filtered_cloud = point_cloud[mask]
    
    return filtered_cloud


def add_noise(point_cloud, noise_level=0.01):
    """
    Add Gaussian noise to a point cloud.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Input point cloud (N x 3)
    noise_level : float
        Standard deviation of the noise
        
    Returns:
    --------
    noisy_cloud : numpy.ndarray
        Point cloud with added noise
    """
    noise = np.random.normal(0, noise_level, point_cloud.shape)
    noisy_cloud = point_cloud + noise
    
    return noisy_cloud
