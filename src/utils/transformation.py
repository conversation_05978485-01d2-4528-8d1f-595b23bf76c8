"""
Geometric transformation utilities for the Solar Panel Point Cloud Analyzer.
"""

import numpy as np
import transforms3d.euler as t3d_euler
import transforms3d.quaternions as t3d_quaternions


def apply_transformation(point_cloud, rotation, translation):
    """
    Apply a transformation to a point cloud.
    
    Parameters:
    -----------
    point_cloud : numpy.ndarray
        Input point cloud (N x 3)
    rotation : numpy.ndarray
        3x3 rotation matrix or 4-element quaternion
    translation : numpy.ndarray
        3-element translation vector
        
    Returns:
    --------
    transformed_cloud : numpy.ndarray
        Transformed point cloud
    """
    # Check if rotation is a quaternion
    if rotation.shape == (4,) or rotation.shape == (4, 1):
        rotation_matrix = quaternion_to_rotation_matrix(rotation)
    else:
        rotation_matrix = rotation
    
    # Apply rotation
    rotated_cloud = np.dot(point_cloud, rotation_matrix.T)
    
    # Apply translation
    transformed_cloud = rotated_cloud + translation
    
    return transformed_cloud


def euler_to_rotation_matrix(euler_angles, sequence='xyz'):
    """
    Convert Euler angles to a rotation matrix.
    
    Parameters:
    -----------
    euler_angles : numpy.ndarray
        Euler angles in radians (3-element array)
    sequence : str
        Rotation sequence (e.g., 'xyz', 'zyx')
        
    Returns:
    --------
    rotation_matrix : numpy.ndarray
        3x3 rotation matrix
    """
    return t3d_euler.euler2mat(euler_angles[0], euler_angles[1], euler_angles[2], sequence)


def rotation_matrix_to_euler(rotation_matrix, sequence='xyz'):
    """
    Convert a rotation matrix to Euler angles.
    
    Parameters:
    -----------
    rotation_matrix : numpy.ndarray
        3x3 rotation matrix
    sequence : str
        Rotation sequence (e.g., 'xyz', 'zyx')
        
    Returns:
    --------
    euler_angles : numpy.ndarray
        Euler angles in radians (3-element array)
    """
    return np.array(t3d_euler.mat2euler(rotation_matrix, sequence))


def quaternion_to_rotation_matrix(quaternion):
    """
    Convert a quaternion to a rotation matrix.
    
    Parameters:
    -----------
    quaternion : numpy.ndarray
        Quaternion (4-element array, scalar last)
        
    Returns:
    --------
    rotation_matrix : numpy.ndarray
        3x3 rotation matrix
    """
    return t3d_quaternions.quat2mat(quaternion)


def rotation_matrix_to_quaternion(rotation_matrix):
    """
    Convert a rotation matrix to a quaternion.
    
    Parameters:
    -----------
    rotation_matrix : numpy.ndarray
        3x3 rotation matrix
        
    Returns:
    --------
    quaternion : numpy.ndarray
        Quaternion (4-element array, scalar last)
    """
    return t3d_quaternions.mat2quat(rotation_matrix)


def euler_to_quaternion(euler_angles, sequence='xyz'):
    """
    Convert Euler angles to a quaternion.
    
    Parameters:
    -----------
    euler_angles : numpy.ndarray
        Euler angles in radians (3-element array)
    sequence : str
        Rotation sequence (e.g., 'xyz', 'zyx')
        
    Returns:
    --------
    quaternion : numpy.ndarray
        Quaternion (4-element array, scalar last)
    """
    rotation_matrix = euler_to_rotation_matrix(euler_angles, sequence)
    return rotation_matrix_to_quaternion(rotation_matrix)


def quaternion_to_euler(quaternion, sequence='xyz'):
    """
    Convert a quaternion to Euler angles.
    
    Parameters:
    -----------
    quaternion : numpy.ndarray
        Quaternion (4-element array, scalar last)
    sequence : str
        Rotation sequence (e.g., 'xyz', 'zyx')
        
    Returns:
    --------
    euler_angles : numpy.ndarray
        Euler angles in radians (3-element array)
    """
    rotation_matrix = quaternion_to_rotation_matrix(quaternion)
    return rotation_matrix_to_euler(rotation_matrix, sequence)


def create_transformation_matrix(rotation, translation):
    """
    Create a 4x4 transformation matrix from rotation and translation.
    
    Parameters:
    -----------
    rotation : numpy.ndarray
        3x3 rotation matrix or 4-element quaternion
    translation : numpy.ndarray
        3-element translation vector
        
    Returns:
    --------
    transformation_matrix : numpy.ndarray
        4x4 transformation matrix
    """
    # Check if rotation is a quaternion
    if rotation.shape == (4,) or rotation.shape == (4, 1):
        rotation_matrix = quaternion_to_rotation_matrix(rotation)
    else:
        rotation_matrix = rotation
    
    # Create transformation matrix
    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = rotation_matrix
    transformation_matrix[:3, 3] = translation
    
    return transformation_matrix


def decompose_transformation_matrix(transformation_matrix):
    """
    Decompose a 4x4 transformation matrix into rotation and translation.
    
    Parameters:
    -----------
    transformation_matrix : numpy.ndarray
        4x4 transformation matrix
        
    Returns:
    --------
    rotation_matrix : numpy.ndarray
        3x3 rotation matrix
    translation : numpy.ndarray
        3-element translation vector
    """
    rotation_matrix = transformation_matrix[:3, :3]
    translation = transformation_matrix[:3, 3]
    
    return rotation_matrix, translation
