import tensorflow as tf

class PoseRegressionNet(tf.keras.Model):
    def __init__(self, num_points=4096):
        super(PoseRegressionNet, self).__init__()
        self.num_points = num_points
        self.per_point_mlp = tf.keras.Sequential([
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dense(128, activation='relu'),
            tf.keras.layers.Dense(1024, activation='relu'),
        ])
        self.global_mlp = tf.keras.Sequential([
            tf.keras.layers.Dense(512, activation='relu'),
            tf.keras.layers.Dense(256, activation='relu'),
            tf.keras.layers.Dense(6)  # 3 translation + 3 rotation
        ])

    def call(self, inputs):
        x = tf.reshape(inputs, (-1, self.num_points * 2, 3))
        x = self.per_point_mlp(x)
        x = tf.reduce_max(x, axis=1)
        pose = self.global_mlp(x)
        return pose

