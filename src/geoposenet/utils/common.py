import numpy as np
import matplotlib.pyplot as plt
import transforms3d.euler as t3d
import open3d as o3d

def apply_pose(points, pose):
    """
    Apply 6-DoF pose (translation + rotation) to a point cloud

    Args:
        points: Nx3 point cloud
        pose: 6-element array [tx, ty, tz, rx, ry, rz]

    Returns:
        transformed_points: Nx3 transformed point cloud
    """
    t = pose[:3]
    angles = pose[3:]
    R = t3d.euler2mat(angles[0], angles[1], angles[2])
    transformed_points = np.dot(points, R) + t
    return transformed_points

def rmse_alignment(source, target):
    """
    Calculate Root Mean Square Error between two point clouds

    Args:
        source: Nx3 source point cloud
        target: Nx3 target point cloud

    Returns:
        rmse: Root Mean Square Error
    """
    assert source.shape == target.shape, "Point clouds must have the same shape"
    squared_dist = np.sum((source - target) ** 2, axis=1)
    return np.sqrt(np.mean(squared_dist))

def plot_alignment(source, target, title="Point Cloud Alignment"):
    """
    Visualize alignment between two point clouds

    Args:
        source: Nx3 source point cloud (aligned)
        target: Nx3 target point cloud
        title: Plot title
    """
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # Plot source points in red
    ax.scatter(source[:, 0], source[:, 1], source[:, 2], c='r', marker='.', s=1, label='Source (Aligned)')

    # Plot target points in green
    ax.scatter(target[:, 0], target[:, 1], target[:, 2], c='g', marker='.', s=1, label='Target')

    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title(title)
    ax.legend()

    plt.tight_layout()
    plt.show()

def save_ply(points, filename, color=None):
    """
    Save point cloud to PLY file

    Args:
        points: Nx3 point cloud
        filename: Output filename
        color: RGB color for all points [r, g, b] in range [0, 1]
    """
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)

    if color is not None:
        colors = np.tile(np.array(color), (points.shape[0], 1))
        pcd.colors = o3d.utility.Vector3dVector(colors)

    o3d.io.write_point_cloud(filename, pcd)
    print(f"Point cloud saved to {filename}")