import datetime

class Logger:
    @staticmethod
    def info(message):
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"\033[94m[{now}] [INFO]\033[0m {message}")

    @staticmethod
    def success(message):
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"\033[92m[{now}] [SUCCESS]\033[0m {message}")

    @staticmethod
    def warning(message):
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"\033[93m[{now}] [WARNING]\033[0m {message}")

    @staticmethod
    def error(message):
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"\033[91m[{now}] [ERROR]\033[0m {message}")
