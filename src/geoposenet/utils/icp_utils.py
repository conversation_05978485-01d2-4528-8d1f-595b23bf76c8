import numpy as np
import open3d as o3d
from scipy.spatial import cKDTree

def refine_with_icp(source, target):
    source_o3d = o3d.geometry.PointCloud()
    target_o3d = o3d.geometry.PointCloud()
    source_o3d.points = o3d.utility.Vector3dVector(source)
    target_o3d.points = o3d.utility.Vector3dVector(target)
    reg = o3d.pipelines.registration.registration_icp(
        source_o3d, target_o3d, max_correspondence_distance=0.05,
        init=np.eye(4),
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint())
    return np.asarray(source_o3d.transform(reg.transformation).points), reg.transformation

def kabsch_algorithm(P, Q):
    centroid_P = np.mean(P, axis=0)
    centroid_Q = np.mean(Q, axis=0)
    P_centered = P - centroid_P
    Q_centered = Q - centroid_Q
    H = np.dot(P_centered.T, Q_centered)
    U, S, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)
    t = centroid_Q - np.dot(centroid_P, R)
    return R, t
