import numpy as np
import laspy
import open3d as o3d
import transforms3d.euler as t3d

def read_las_file(filepath):
    las = laspy.read(filepath)
    return np.vstack((las.x, las.y, las.z)).T

def read_obj_file(filepath):
    mesh = o3d.io.read_triangle_mesh(filepath)
    return np.asarray(mesh.vertices)

def normalize_point_cloud(points):
    centroid = np.mean(points, axis=0)
    points = points - centroid
    furthest_distance = np.max(np.linalg.norm(points, axis=1))
    return points / furthest_distance

def create_real_dataset(source_points, target_points, batch_size=32, num_points=1024):
    source_batch, target_batch, gt_pose_batch = [], [], []
    for _ in range(batch_size):
        angles = np.random.uniform(-0.1, 0.1, size=3)
        R = t3d.euler2mat(angles[0], angles[1], angles[2])
        t = np.random.uniform(-0.05, 0.05, size=3)
        src_pts = np.dot(target_points, R.T) + t
        pose = np.hstack([t, angles])
        if src_pts.shape[0] > num_points:
            idx = np.random.choice(src_pts.shape[0], num_points, replace=False)
            src_pts = src_pts[idx]
            tgt_pts = target_points[idx]
        else:
            tgt_pts = target_points
        source_batch.append(src_pts)
        target_batch.append(tgt_pts)
        gt_pose_batch.append(pose)
    return np.array(source_batch), np.array(target_batch), np.array(gt_pose_batch)

