from setuptools import setup, find_packages

setup(
    name="solar-array-inspection-3d",
    version="0.1.0",
    author="Preetam Balijepalli",
    author_email="<EMAIL>",
    description="3D inspection of solar arrays using point cloud analysis for geometric measurement and anomaly detection",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/balijepalli/solar-array-inspection-3d",
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Processing",
    ],
    python_requires=">=3.8",
    install_requires=[
        "tensorflow>=2.8.0",
        "numpy>=1.20.0",
        "open3d>=0.15.0",
        "laspy>=2.0.0",
        "pyyaml>=6.0",
        "matplotlib>=3.5.0",
        "transforms3d>=0.3.1",
        "scipy>=1.7.0",
        "scikit-learn>=1.0.0",
        "tqdm>=4.62.0",
        "pandas>=1.3.0",
        "plotly>=5.5.0",
        "ifcopenshell>=0.6.0",
    ],
)
